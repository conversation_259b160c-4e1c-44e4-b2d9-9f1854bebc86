# Invoice API Documentation

## 🎯 **Overview**

The Invoice API provides functionality to generate PDF invoices for purchase orders. It supports generating invoices by external order ID or internal order ID, with customer information provided as parameters.

## 🔧 **API Endpoints**

### **1. Generate Invoice by External ID**
```
GET /api/invoices/generate?name={name}&email={email}&externalId={externalId}
```

#### **Parameters:**
- **`name`** (required) - Customer name
- **`email`** (required) - Customer email address
- **`externalId`** (required) - External order ID

#### **Response:**
- **Success (200)**: PDF file download
- **Not Found (404)**: Order not found
- **Error (500)**: Internal server error

#### **Example:**
```bash
curl -X GET "http://localhost:8080/api/invoices/generate?name=John%20Doe&email=<EMAIL>&externalId=ORDER_12345" \
     -H "Accept: application/pdf" \
     --output invoice.pdf
```

### **2. Generate Invoice by Order ID**
```
GET /api/invoices/generate/{orderId}?name={name}&email={email}
```

#### **Parameters:**
- **`orderId`** (path) - Internal purchase order ID
- **`name`** (required) - Customer name
- **`email`** (required) - Customer email address

#### **Response:**
- **Success (200)**: PDF file download
- **Not Found (404)**: Order not found
- **Error (500)**: Internal server error

#### **Example:**
```bash
curl -X GET "http://localhost:8080/api/invoices/generate/ORDER_ID_123?name=Jane%20Smith&email=<EMAIL>" \
     -H "Accept: application/pdf" \
     --output invoice.pdf
```

## 📋 **Invoice Structure**

### **PDF Invoice Contains:**

#### **1. Header Section:**
- **Invoice Title** - "INVOICE" (centered, bold)
- **Invoice Number** - Uses externalId or orderId
- **Invoice Date** - Current date and time

#### **2. Customer Information:**
- **Billed To** section with customer name and email
- **Sold By** section with company information

#### **3. Items Table:**
- **Item** - Product details and part number
- **Qty** - Quantity ordered
- **Unit Price** - Price per item
- **Total** - Line item total

#### **4. Total Section:**
- **Grand Total** - Sum of all items

#### **5. Footer:**
- **Payment Method** - "Purchase Order"
- **Thank you message** and disclaimer

## 🔍 **Data Extraction Logic**

### **Item Information Sources:**

#### **1. From ComplexValues (Preferred):**
```java
// Extracts from order.ComplexValues.PurchaseOrders[].Values
- OrderProductDetails → Item name
- OrderPartNumber → Part number
- OrderQuantity → Quantity
- OrderCost → Unit price
```

#### **2. From Values Array (Fallback):**
```java
// Extracts from order.Values[]
- ECM_GrandTotal → Total amount
// Creates generic "Purchase Order Items" entry
```

### **Data Extraction Methods:**
- **`extractStringValue()`** - Safely extracts string values from order attributes
- **`extractDoubleValue()`** - Safely extracts numeric values with type conversion
- **`extractGrandTotalFromValues()`** - Finds ECM_GrandTotal in Values array

## 🏗️ **Architecture**

### **Components:**

#### **1. InvoiceController**
- **REST endpoints** for invoice generation
- **Parameter validation** and error handling
- **HTTP response** management with proper headers

#### **2. InvoiceService Interface**
- **Service contract** for invoice operations
- **Method signatures** for different invoice generation approaches

#### **3. InvoiceServiceImpl**
- **PDF generation logic** using iText library
- **Data extraction** from KarmakPurchaseOrder
- **Error handling** and logging

#### **4. Dependencies:**
```xml
<dependency>
    <groupId>com.itextpdf</groupId>
    <artifactId>itext7-core</artifactId>
    <version>7.2.5</version>
</dependency>
```

## 📊 **Sample Invoice Output**

```
                           INVOICE

Invoice No: #ORDER_12345              Invoice Date: 28 Jan 2025, 10:30 AM

Billed To:                            Sold By:
John Doe                              ATP Parts Company
Email: <EMAIL>           Email: <EMAIL>

┌─────────────────────────────────────┬─────┬────────────┬─────────────┐
│ Item                                │ Qty │ Unit Price │ Total       │
├─────────────────────────────────────┼─────┼────────────┼─────────────┤
│ STT LMP,4"RED,SNOVALED,10DIODE      │  1  │   $ 24.69  │   $ 24.69   │
│ (Part #: 53252)                     │     │            │             │
└─────────────────────────────────────┴─────┴────────────┴─────────────┘

                                                Grand Total    $ 24.69

Payment Method: Purchase Order

Thank you for your business!
This is a system-generated invoice and does not require a signature.
```

## 🧪 **Testing**

### **Test Coverage:**
- ✅ **Successful invoice generation** by external ID
- ✅ **Successful invoice generation** by order ID
- ✅ **Order not found scenarios**
- ✅ **Service exception handling**
- ✅ **Complex values data extraction**
- ✅ **Fallback to Values array**

### **Test Example:**
```java
@Test
void testGenerateInvoice_Success() {
    // Given
    String name = "John Doe";
    String email = "<EMAIL>";
    String externalId = "ORDER_12345";
    
    when(purchaseOrderService.getPurchaseOrderByExternalId(externalId))
            .thenReturn(sampleOrder);
    
    // When
    ByteArrayResource result = invoiceService.generateInvoice(name, email, externalId);
    
    // Then
    assertNotNull(result);
    assertTrue(result.contentLength() > 0);
}
```

## 🔒 **Error Handling**

### **Error Scenarios:**

#### **1. Order Not Found:**
```java
// Returns HTTP 404 when order doesn't exist
if (order == null) {
    return ResponseEntity.notFound().build();
}
```

#### **2. PDF Generation Error:**
```java
// Returns HTTP 500 for PDF generation failures
catch (Exception e) {
    log.error("Error generating invoice", e);
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
}
```

#### **3. Data Extraction Error:**
```java
// Graceful fallback for missing data
private String extractStringValue(Object attribute, String defaultValue) {
    try {
        // Extract value using reflection
    } catch (Exception e) {
        log.debug("Failed to extract value, using default", e);
        return defaultValue;
    }
}
```

## 📈 **Usage Examples**

### **1. Frontend Integration:**
```javascript
// Download invoice via JavaScript
function downloadInvoice(name, email, externalId) {
    const url = `/api/invoices/generate?name=${encodeURIComponent(name)}&email=${encodeURIComponent(email)}&externalId=${externalId}`;
    
    fetch(url)
        .then(response => response.blob())
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `invoice_${externalId}.pdf`;
            a.click();
        });
}
```

### **2. Postman Collection:**
```json
{
    "name": "Generate Invoice",
    "request": {
        "method": "GET",
        "url": {
            "raw": "{{baseUrl}}/api/invoices/generate?name=John Doe&email=<EMAIL>&externalId=ORDER_12345",
            "query": [
                {"key": "name", "value": "John Doe"},
                {"key": "email", "value": "<EMAIL>"},
                {"key": "externalId", "value": "ORDER_12345"}
            ]
        }
    }
}
```

## 🚀 **Benefits**

### **✅ Flexible Invoice Generation:**
- **Multiple access methods** - by external ID or internal ID
- **Customer information** included in parameters
- **Professional PDF output** with proper formatting

### **✅ Robust Data Handling:**
- **Smart data extraction** from complex order structures
- **Fallback mechanisms** for missing data
- **Type-safe value conversion**

### **✅ Production Ready:**
- **Comprehensive error handling**
- **Detailed logging** for troubleshooting
- **Unit test coverage** for reliability

### **✅ Easy Integration:**
- **RESTful API design** with standard HTTP methods
- **Clear parameter structure** for easy consumption
- **Swagger documentation** for API exploration

## 🎯 **Result**

The Invoice API provides a complete solution for generating professional PDF invoices from purchase order data, with flexible access methods and robust error handling. Perfect for integration with frontend applications, automated systems, or manual invoice generation workflows.

**Generate professional invoices with just a simple API call!** 📄
