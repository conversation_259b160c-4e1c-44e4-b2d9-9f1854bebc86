FROM  harbor.indianic.com/library/java/maven:3.6.3-openjdk-17 as build

COPY src /java-catalog-service/src

COPY pom.xml /java-catalog-service

RUN mvn -f /java-catalog-service/pom.xml clean package


FROM harbor.indianic.com/library/java/openjdk:17-alpine

COPY --from=build  /java-catalog-service/target/ATP-product-service-0.0.1.jar /app/ATP-product-service-0.0.1.jar

EXPOSE 9002

CMD java -jar /app/ATP-product-service-0.0.1.jar

