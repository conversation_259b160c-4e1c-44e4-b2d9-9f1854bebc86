apiVersion: apps/v1
kind: Deployment
metadata:
  name: <PERSON><PERSON><PERSON>YMENT_NAME
  labels:
    app: DEPLOYMENT_NAME
spec:
  replicas: 1
  selector:
    matchLabels:
      app: DEPLOYMENT_NAME
  template:
    metadata:
      labels:
        app: DEPLOYMENT_NAME
    spec:
      imagePullSecrets:
      - name : creddocker
      containers:
        - name: DEPLOYMENT_NAME
          image: DOCKER_IMG:latest
          ports:
            - containerPort: PORT
          imagePullPolicy: Always
      dnsPolicy: "None"
      dnsConfig:
        nameservers:
          - ********             
---
apiVersion: v1
kind: Service
metadata:
  name: DEPLOYMENT_NAME
  labels:
    app: DEPLOYMENT_NAME
spec:
  selector:
    app: DEPLOYMENT_NAME
  ports:
    - port: PORT
      targetPort: PORT

---

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: DEPLOYMENT_NAME-ingress
spec:
  #tls:
    #- hosts:
      #- test.example.com
      #secretName: nginx-tls-secret
  rules:
  - host: HOST
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: DEPLOYMENT_NAME
            port:
              number: PORT
