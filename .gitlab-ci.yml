####### Branch Name as Variable
variables:
  DEV: "development"
  STAG: ""
  QA: ""

stages:
  - build_push
  - deploy_kube
  - deploy_vm
###################
build_push_img:
  stage: build_push
  tags:
    - docker-exec
  script:
    # # Build Image
    - case "$CI_COMMIT_REF_NAME" in
      "$DEV")  DOCKER_IMG=$DOCKER_DEV_IMG  ENV=$VAR_FILE_DEV ;;
      "$STAG") DOCKER_IMG=$DOCKER_STAG_IMG ENV=$VAR_FILE_STAG ;;
      "$QA")   DOCKER_IMG=$DOCKER_QA_IMG   ENV=$VAR_FILE_QA ;;
      "$UAT")  DOCKER_IMG=$DOCKER_UAT_IMG  ENV=$VAR_FILE_UAT ;;
      esac
    - echo "$ENV" > src/main/resources/application.yml
    - docker build -t $DOCKER_IMG:latest .

    # # Push Image
    - docker push $DOCKER_IMG:latest

    # # Clean Image
    - docker rmi  $DOCKER_IMG:latest
  only:
    - development
    - $staging
    - $QA
    - $UAT

deploy_kube:
  stage: deploy_kube
  tags:
    - kube-prod-exec
  script:
    - case "$CI_COMMIT_REF_NAME" in
      "$DEV")  DOCKER_IMG=$DOCKER_DEV_IMG  DEPLOYMENT_NAME=$DN_DEV  HOST=$URL_DEV  ;;
      "$STAG") DOCKER_IMG=$DOCKER_STAG_IMG DEPLOYMENT_NAME=$DN_STAG HOST=$URL_STAG ;;
      "$QA")   DOCKER_IMG=$DOCKER_QA_IMG   DEPLOYMENT_NAME=$DN_QA   HOST=$URL_QA;;
      "$UAT")  DOCKER_IMG=$DOCKER_UAT_IMG  DEPLOYMENT_NAME=$DN_UAT  HOST=$URL_UAT ;;
      esac

    - sed -i "s/DEPLOYMENT_NAME/${DEPLOYMENT_NAME}/g" Deployment_File.yaml
    - sed -i "s/HOST/${HOST}/g" Deployment_File.yaml
    - sed -i 's|DOCKER_IMG|'"$DOCKER_IMG"'|g' Deployment_File.yaml
    - sed -i "s/PORT/${PORT}/g" Deployment_File.yaml
    - kubectl apply -f Deployment_File.yaml -n $NS
    - kubectl rollout restart deployment $DEPLOYMENT_NAME -n $NS
    - cat Deployment_File.yaml
  only:
    - development
    - $staging
    - $QA
    - $UAT
