# PlaceOrder API Integration Documentation

## Overview
The `placeOrder` method in `ScheduledOrderServiceImpl` integrates with a third-party API to place orders using the original `KarmakPurchaseOrder` data. This method converts the internal order structure to the required API format and handles the HTTP communication.

## 🔧 **Method Signature**
```java
public boolean placeOrder(KarmakPurchaseOrder originalOrder)
```

## 📋 **API Integration Details**

### **Endpoint Configuration**
- **URL**: Configurable via `third-party.api.url` property
- **Method**: POST
- **Content-Type**: application/json
- **Authorization**: Bearer token via `third-party.api.authorization` property

### **Request Payload Structure**
The method converts `KarmakPurchaseOrder` to the following JSON structure:

```json
{
  "parentId": "CSTM_1185",
  "entityType": "DE",
  "workspaceId": "Maintenance",
  "datatypeDefinitionId": "PurchaseOrder",
  "simpleValues": {},
  "completeness": 0,
  "deleted": false,
  "systemEntity": false,
  "approvalStatus": "NOT_APPROVED",
  "externalId": "",
  "name": "",
  "Values": [
    {
      "attributeId": "ECM_OrderDate",
      "content": [{"value": "01/28/2025"}]
    },
    {
      "attributeId": "ECM_GrandTotal",
      "content": [{"value": 32.69}]
    },
    {
      "attributeId": "OrderStatus",
      "content": [{"value": "Order Placed/New Order"}]
    },
    {
      "attributeId": "BinLocation",
      "content": [{"value": ""}]
    },
    {
      "attributeId": "DeliveryMethod",
      "content": [{"value": "Store Pick Up"}]
    }
  ],
  "path": ["MainEntityRoot", "CustomersHierarchy", "CSTM_1185"],
  "ComplexValues": {
    "PurchaseOrders": [
      {
        "type": "PurchaseOrders",
        "name": "PurchaseOrders",
        "Values": {
          "OrderPartNumber": {
            "attributeId": "OrderPartNumber",
            "content": [{"value": "577.99501"}]
          },
          "OrderCost": {
            "attributeId": "OrderCost",
            "content": [{"value": 32.69}]
          },
          "OrderQuantity": {
            "attributeId": "OrderQuantity",
            "content": [{"value": 1}]
          },
          "OrderPrimaryURL": {
            "attributeId": "OrderPrimaryURL",
            "content": [{"value": ""}]
          },
          "OrderProductDetails": {
            "attributeId": "OrderProductDetails",
            "content": [{"value": "EATON SPEED SENSOR"}]
          },
          "OrderSupplierName": {
            "attributeId": "OrderSupplierName",
            "content": [{"value": ""}]
          }
        }
      }
    ]
  }
}
```

## ⚙️ **Configuration Properties**

### **Application Properties**
```properties
# Third-Party API Configuration
third-party.api.url=http://localhost:8080/server/api/data-entities/create
third-party.api.authorization=your-jwt-token-here
third-party.api.timeout=30
```

### **Environment-Specific Configuration**
```properties
# Development
third-party.api.url=http://localhost:8080/server/api/data-entities/create

# Staging
third-party.api.url=https://staging-api.example.com/api/data-entities/create

# Production
third-party.api.url=https://api.example.com/api/data-entities/create
```

## 🔄 **Data Transformation Process**

### **1. Basic Fields Mapping**
- Maps standard fields from `KarmakPurchaseOrder` to API format
- Provides default values for missing fields
- Handles null safety throughout the transformation

### **2. Values Array Construction**
- **ECM_OrderDate**: Current date in MM/dd/yyyy format
- **ECM_GrandTotal**: Calculated from complex values
- **OrderStatus**: Set to "Order Placed/New Order"
- **BinLocation**: Empty string (configurable)
- **DeliveryMethod**: Set to "Store Pick Up" (configurable)

### **3. Path Array Building**
- Uses original order path if available
- Falls back to default structure: `["MainEntityRoot", "CustomersHierarchy", "PARENT_ID"]`

### **4. ComplexValues Transformation**
- Converts each `PurchaseOrder` in complex values
- Maps all order attributes (part number, cost, quantity, etc.)
- Handles different data types (strings, numbers)
- Uses reflection for flexible attribute extraction

## 🚨 **Error Handling**

### **HTTP Response Codes**
- **200-299**: Success - returns `true`
- **400-499**: Client errors - returns `false`, logs error details
- **500-599**: Server errors - returns `false`, logs error details

### **Exception Handling**
- **IOException**: Network/connectivity issues - returns `false`
- **JSON Parsing Errors**: Malformed data - returns `false`
- **Reflection Errors**: Attribute extraction failures - logs warning, continues with empty values

### **Logging Levels**
```java
// Success
log.info("Order placed successfully for external ID: {}. Response code: {}", externalId, responseCode);

// API Failures
log.error("Failed to place order for external ID: {}. Response code: {}, Message: {}", externalId, responseCode, message);

// Network Errors
log.error("IO Exception while placing order for external ID: {}", externalId, exception);

// Data Extraction Warnings
log.warn("Failed to extract content from attribute {}: {}", attributeId, errorMessage);
```

## 🧪 **Testing**

### **Unit Tests**
- **Success scenarios**: Valid orders, successful API responses
- **Failure scenarios**: API errors, network failures, malformed data
- **Edge cases**: Null orders, missing attributes, empty complex values

### **Integration Tests**
```java
@Test
void testPlaceOrder_RealAPI() {
    // Test against actual API endpoint
    KarmakPurchaseOrder order = createTestOrder();
    boolean result = scheduledOrderService.placeOrder(order);
    assertTrue(result);
}
```

### **Mock Testing**
```java
@Test
void testPlaceOrder_MockedResponse() {
    // Test with mocked HTTP client
    when(response.isSuccessful()).thenReturn(true);
    boolean result = scheduledOrderService.placeOrder(order);
    assertTrue(result);
}
```

## 🔒 **Security Considerations**

### **Authentication**
- JWT token-based authentication
- Token should be stored securely (environment variables, secrets management)
- Token rotation and expiration handling

### **Data Protection**
- Sensitive order data transmitted over HTTPS
- Request/response logging should exclude sensitive information
- Audit trail for order placement activities

## 📊 **Performance Characteristics**

### **Execution Time**
- **Typical**: 100-500ms per order
- **Network dependent**: Varies based on API response time
- **Timeout configured**: 30 seconds default

### **Memory Usage**
- **JSON payload**: ~2-5KB per order
- **Object creation**: Minimal overhead with object pooling
- **HTTP client**: Reused connection pooling

### **Throughput**
- **Sequential**: ~2-10 orders per second
- **Parallel**: Can be increased with async processing
- **Rate limiting**: Respect API rate limits

## 🔧 **Troubleshooting**

### **Common Issues**

#### **1. Authentication Failures (401)**
```
Solution: Check authorization token validity and format
```

#### **2. Malformed Request (400)**
```
Solution: Verify JSON payload structure and required fields
```

#### **3. Network Timeouts**
```
Solution: Increase timeout configuration or check network connectivity
```

#### **4. Missing Attributes**
```
Solution: Ensure KarmakPurchaseOrder has complete data structure
```

### **Debug Mode**
Enable debug logging to see full request/response details:
```properties
logging.level.com.atp.product.service.impl.ScheduledOrderServiceImpl=DEBUG
```

## 🚀 **Usage Example**

```java
@Autowired
private ScheduledOrderService scheduledOrderService;

public void processOrder() {
    KarmakPurchaseOrder order = getOrderFromDatabase();
    
    boolean success = scheduledOrderService.placeOrder(order);
    
    if (success) {
        log.info("Order placed successfully");
        // Handle success scenario
    } else {
        log.error("Failed to place order");
        // Handle failure scenario
    }
}
```

## 📈 **Monitoring and Metrics**

### **Key Metrics to Track**
- **Success Rate**: Percentage of successful order placements
- **Response Time**: Average API response time
- **Error Rate**: Frequency of different error types
- **Throughput**: Orders processed per minute/hour

### **Alerting**
- **High Error Rate**: >5% failure rate
- **Slow Response**: >2 seconds average response time
- **Authentication Issues**: Multiple 401 responses

---

**Version**: 1.0.0  
**Last Updated**: 2025-01-28  
**Maintainer**: ATP Product Team
