# Customer Fields Update Summary

## ✅ **Successfully Updated Customer Fields**

The following changes have been made to the ScheduledOrder system:
- ✅ **Renamed** `customerId` → `karmakCustomerId`
- ✅ **Added** new field `customerCorrelationId`

## 🔄 **Files Modified:**

### **1. Model Layer**
#### **ScheduledOrder.java**
- ✅ Renamed `private String customerId;` → `private String karmakCustomerId;`
- ✅ Added `private String customerCorrelationId;`

### **2. DTO Layer**
#### **ScheduledOrderRequest.java**
- ✅ Renamed `customerId` → `karmakCustomerId` with validation
- ✅ Added `customerCorrelationId` field with validation
- ✅ Updated JSON property names and validation messages

#### **UpdateScheduleRequest.java**
- ✅ Renamed `customerId` → `karmakCustomerId`
- ✅ Added `customerCorrelationId` field

#### **ScheduledOrderResponse.java**
- ✅ Renamed `customerId` → `karmakCustomerId`
- ✅ Added `customerCorrelationId` field

### **3. Service Layer**
#### **ScheduledOrderService.java (Interface)**
- ✅ Updated method signatures:
  - `cancel(String scheduledOrderId, String karmakCustomerId)`
  - `getScheduledOrdersByCustomer(String karmakCustomerId)`
  - `getScheduledOrderById(String scheduledOrderId, String karmakCustomerId)`

#### **ScheduledOrderServiceImpl.java**
- ✅ Updated all method implementations to use `karmakCustomerId`
- ✅ Added `customerCorrelationId` mapping in subscribe and update methods
- ✅ Updated all database queries to use `karmakCustomerId`
- ✅ Updated all notification service calls to use `karmakCustomerId`
- ✅ Updated all logging statements to use `karmakCustomerId`

### **4. Controller Layer**
#### **ScheduledOrderController.java**
- ✅ Updated all endpoint parameter names:
  - `@RequestParam String karmakCustomerId`
  - `@PathVariable String karmakCustomerId`
- ✅ Updated all API documentation descriptions
- ✅ Updated all logging statements

## 📋 **API Changes:**

### **Request/Response Structure:**

#### **Before:**
```json
{
    "customerId": "CUST_123",
    "originalOrderId": "ORDER_456",
    "frequencyDays": 30,
    "notes": "Monthly order"
}
```

#### **After:**
```json
{
    "karmakCustomerId": "00205",
    "customerCorrelationId": "CSTM_1072", 
    "originalOrderId": "ORDER_456",
    "frequencyDays": 30,
    "notes": "Monthly order"
}
```

### **API Endpoint Changes:**

#### **Before:**
```
GET    /api/scheduled-orders/customer/{customerId}
DELETE /api/scheduled-orders/{id}?customerId=CUST_123
GET    /api/scheduled-orders/{id}?customerId=CUST_123
```

#### **After:**
```
GET    /api/scheduled-orders/customer/{karmakCustomerId}
DELETE /api/scheduled-orders/{id}?karmakCustomerId=00205
GET    /api/scheduled-orders/{id}?karmakCustomerId=00205
```

## 🎯 **Current ScheduledOrder Structure:**

### **✅ Updated Model:**
```java
@Document(collection = "scheduled_orders")
public class ScheduledOrder {
    private String id;
    private String karmakCustomerId;        // ✅ RENAMED from customerId
    private String customerCorrelationId;   // ✅ NEW FIELD
    private String originalOrderId;
    private LocalDate subscriptionDate;
    private int frequencyDays;
    private LocalDate nextRunDate;
    private boolean active = true;
    private String notes;
    private Integer ordersProcessed = 0;
    private LocalDateTime lastProcessedDate;
    private Boolean notifyOnOutOfStock = true;
    private String status = "ACTIVE"; // ACTIVE, CANCELLED
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
```

### **✅ Updated Request DTO:**
```java
public class ScheduledOrderRequest {
    @JsonProperty("karmakCustomerId")
    @NotBlank(message = "Karmak Customer ID is required")
    private String karmakCustomerId;

    @JsonProperty("customerCorrelationId")
    @NotBlank(message = "Customer Correlation ID is required")
    private String customerCorrelationId;

    @JsonProperty("originalOrderId")
    @NotBlank(message = "Original order ID is required")
    private String originalOrderId;
    
    // ... other fields
}
```

### **✅ Updated Response DTO:**
```json
{
    "id": "SCHED_789",
    "karmakCustomerId": "00205",
    "customerCorrelationId": "CSTM_1072",
    "originalOrderId": "ORDER_456",
    "subscriptionDate": "2025-01-28",
    "frequencyDays": 30,
    "nextRunDate": "2025-02-27",
    "active": true,
    "notes": "Monthly order",
    "ordersProcessed": 5,
    "lastProcessedDate": "2025-01-27T10:30:00",
    "notifyOnOutOfStock": true,
    "status": "ACTIVE",
    "createdAt": "2025-01-01T09:00:00",
    "updatedAt": "2025-01-27T10:30:00"
}
```

## 🔧 **Database Query Updates:**

### **Before:**
```java
Query query = new Query(Criteria.where("customerId").is(customerId));
```

### **After:**
```java
Query query = new Query(Criteria.where("karmakCustomerId").is(karmakCustomerId));
```

## 📊 **Service Method Updates:**

### **Before:**
```java
// Method signatures
boolean cancel(String scheduledOrderId, String customerId);
List<ScheduledOrderResponse> getScheduledOrdersByCustomer(String customerId);

// Notification calls
notificationService.sendPurchaseOrderSuccess(scheduledOrder.getCustomerId(), ...);
```

### **After:**
```java
// Method signatures
boolean cancel(String scheduledOrderId, String karmakCustomerId);
List<ScheduledOrderResponse> getScheduledOrdersByCustomer(String karmakCustomerId);

// Notification calls
notificationService.sendPurchaseOrderSuccess(scheduledOrder.getKarmakCustomerId(), ...);
```

## 🎯 **Migration Notes:**

### **For Existing Data:**
If there are existing scheduled orders in the database:
1. **Field mapping**: `customerId` data should be migrated to `karmakCustomerId`
2. **New field**: `customerCorrelationId` will need to be populated for existing records
3. **Queries**: All existing queries will need to use the new field names

### **For API Clients:**
- **Update request payloads** to use `karmakCustomerId` and `customerCorrelationId`
- **Update response parsing** to use new field names
- **Update URL parameters** in API calls
- **Update query parameters** for filtering endpoints

## ✅ **Benefits:**

### **✅ Clearer Field Names:**
- **`karmakCustomerId`** - Clearly indicates this is the Karmak system customer ID
- **`customerCorrelationId`** - Provides additional customer correlation/mapping capability

### **✅ Better Data Mapping:**
- **Dual customer identification** - Both Karmak ID and correlation ID
- **Flexible customer management** - Can handle different customer ID systems
- **Enhanced traceability** - Better customer data correlation

### **✅ Improved API Clarity:**
- **Self-documenting field names** - Clear what each ID represents
- **Consistent naming** - Follows naming conventions
- **Better validation messages** - More specific error messages

## 🚀 **Result:**

The ScheduledOrder system now has:
- **✅ Clearer customer identification** with `karmakCustomerId` and `customerCorrelationId`
- **✅ Updated API endpoints** with descriptive parameter names
- **✅ Enhanced data model** with dual customer ID support
- **✅ Consistent field naming** throughout the entire system

**The system now provides better customer identification and correlation capabilities!** 🎯
