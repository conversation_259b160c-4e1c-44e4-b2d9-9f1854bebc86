# ScheduledOrderService Documentation

## Overview
The `ScheduledOrderService` is a comprehensive service for managing scheduled orders and subscriptions. It provides functionality for creating, updating, canceling, and processing recurring orders automatically.

## 🚀 Key Features

### **1. Subscription Management**
- **Subscribe**: Create new scheduled order subscriptions
- **Cancel**: Cancel existing subscriptions
- **Update**: Modify scheduling parameters
- **Pause/Resume**: Temporarily pause and resume subscriptions

### **2. Order Processing**
- **Automated Processing**: Scheduled execution every hour
- **Parallel Processing**: Optimized for high-throughput scenarios
- **Availability Checking**: Real-time inventory validation
- **Error Handling**: Comprehensive error management and notifications

### **3. Notification System**
- **Email Notifications**: Configurable email alerts
- **Out of Stock Alerts**: Notify when items are unavailable
- **Success Notifications**: Confirm successful order processing
- **Failure Alerts**: Immediate notification of processing errors

## 📋 API Endpoints

### **Subscription Management**

#### **POST /api/scheduled-orders/subscribe**
Create a new scheduled order subscription.

**Request Body:**
```json
{
  "customerId": "customer123",
  "originalOrderId": "order456",
  "frequencyDays": 30,
  "startDate": "2024-01-15",
  "endDate": "2024-12-31",
  "notes": "Monthly office supplies",
  "autoRenew": true,
  "maxOrders": 12,
  "notifyOnOutOfStock": true,
  "notifyOnSuccess": true
}
```

**Response:**
```json
{
  "id": "scheduled_order_789",
  "customerId": "customer123",
  "originalOrderId": "order456",
  "subscriptionDate": "2024-01-01",
  "frequencyDays": 30,
  "nextRunDate": "2024-01-15",
  "endDate": "2024-12-31",
  "active": true,
  "paused": false,
  "status": "ACTIVE",
  "ordersProcessed": 0,
  "createdAt": "2024-01-01T10:00:00",
  "updatedAt": "2024-01-01T10:00:00"
}
```

#### **DELETE /api/scheduled-orders/{scheduledOrderId}**
Cancel a scheduled order subscription.

**Parameters:**
- `scheduledOrderId`: The ID of the scheduled order
- `customerId`: Customer ID for validation

#### **PUT /api/scheduled-orders/{scheduledOrderId}**
Update scheduling parameters.

**Request Body:**
```json
{
  "customerId": "customer123",
  "frequencyDays": 45,
  "nextRunDate": "2024-02-15",
  "notes": "Updated frequency",
  "autoRenew": false
}
```

### **Order Retrieval**

#### **GET /api/scheduled-orders/customer/{customerId}**
Get all scheduled orders for a customer.

#### **GET /api/scheduled-orders/{scheduledOrderId}**
Get a specific scheduled order by ID.

### **Order Control**

#### **PATCH /api/scheduled-orders/{scheduledOrderId}/pause**
Temporarily pause a scheduled order.

#### **PATCH /api/scheduled-orders/{scheduledOrderId}/resume**
Resume a paused scheduled order.

### **Admin Endpoints**

#### **GET /api/scheduled-orders/admin/active**
Get all active scheduled orders (admin only).

#### **POST /api/scheduled-orders/admin/process**
Manually trigger scheduled order processing (admin only).

## 🔧 Configuration

### **Application Properties**
```properties
# Notification settings
notification.email.enabled=true
notification.email.from=<EMAIL>
notification.email.support=<EMAIL>

# Scheduling settings
scheduled.orders.processing.rate=3600000  # 1 hour in milliseconds
scheduled.orders.parallel.threshold=10    # Use parallel processing for >10 orders
```

### **Database Configuration**
The service uses MongoDB with the `scheduled_orders` collection. Ensure proper indexing:

```javascript
// MongoDB indexes for optimal performance
db.scheduled_orders.createIndex({ "customerId": 1 })
db.scheduled_orders.createIndex({ "active": 1, "nextRunDate": 1 })
db.scheduled_orders.createIndex({ "originalOrderId": 1 })
db.scheduled_orders.createIndex({ "status": 1 })
```

## 🏗️ Architecture

### **Service Layer Structure**
```
ScheduledOrderService (Interface)
├── ScheduledOrderServiceImpl (Implementation)
├── NotificationService (Email notifications)
├── PurchaseOrderService (Order retrieval)
└── CommonServiceImpl (Order creation and placement)
```

### **Data Model**
```java
@Document(collection = "scheduled_orders")
public class ScheduledOrder {
    private String id;
    private String customerId;
    private String originalOrderId;
    private LocalDate subscriptionDate;
    private int frequencyDays;
    private LocalDate nextRunDate;
    private LocalDate endDate;
    private boolean active;
    private boolean paused;
    private String status; // ACTIVE, PAUSED, CANCELLED, COMPLETED
    private Integer ordersProcessed;
    private LocalDateTime lastProcessedDate;
    // ... additional fields
}
```

## ⚡ Performance Optimizations

### **1. Parallel Processing**
- Orders are processed in parallel using `CompletableFuture`
- Configurable threshold for parallel execution
- Improved throughput for large batches

### **2. Efficient Database Queries**
- Optimized queries with proper indexing
- Batch updates for better performance
- Field projection to reduce network overhead

### **3. Asynchronous Operations**
- Non-blocking email notifications
- Asynchronous inventory checking
- Parallel order validation

### **4. Error Handling**
- Graceful degradation on failures
- Retry mechanisms for transient errors
- Comprehensive logging and monitoring

## 🔍 Monitoring and Observability

### **Key Metrics**
- **Processing Rate**: Orders processed per hour
- **Success Rate**: Percentage of successful order placements
- **Error Rate**: Failed processing attempts
- **Notification Delivery**: Email notification success rate

### **Logging**
```java
// Example log entries
INFO  - Starting scheduled order processing...
INFO  - Found 25 scheduled orders due for processing
INFO  - Processing scheduled order: order123 for customer: customer456
INFO  - PO created successfully: PO789 for scheduled order: order123
WARN  - Part ABC123 is out of stock for order order123
ERROR - Failed to process scheduled order: order123
```

### **Health Checks**
Monitor the following for system health:
- Database connectivity
- Email service availability
- External API responsiveness
- Processing queue size

## 🧪 Testing

### **Unit Tests**
Comprehensive test coverage including:
- Subscription creation and validation
- Order processing logic
- Error handling scenarios
- Notification delivery

### **Integration Tests**
- End-to-end subscription workflows
- Database operations
- External service integrations
- Performance testing

### **Running Tests**
```bash
# Run all scheduled order service tests
mvn test -Dtest=ScheduledOrderServiceTest

# Run integration tests
mvn test -Dtest=ScheduledOrderServiceIntegrationTest

# Run performance tests
mvn test -Dtest=ScheduledOrderServicePerformanceTest
```

## 🚨 Error Handling

### **Common Error Scenarios**
1. **Original Order Not Found**: Returns 404 with appropriate message
2. **Inventory Unavailable**: Sends notification and reschedules
3. **Payment Processing Failure**: Notifies customer and logs error
4. **External API Timeout**: Implements retry logic with exponential backoff

### **Error Response Format**
```json
{
  "error": "ORIGINAL_ORDER_NOT_FOUND",
  "message": "Original order not found: order456",
  "timestamp": "2024-01-01T10:00:00Z",
  "path": "/api/scheduled-orders/subscribe"
}
```

## 🔐 Security Considerations

### **Authentication & Authorization**
- Customer ID validation for all operations
- Admin endpoints require elevated privileges
- API rate limiting to prevent abuse

### **Data Protection**
- Sensitive data encryption at rest
- Secure communication with external services
- Audit logging for compliance

## 📈 Scalability

### **Horizontal Scaling**
- Stateless service design
- Database connection pooling
- Load balancer compatible

### **Vertical Scaling**
- Configurable thread pools
- Memory-efficient processing
- CPU optimization for parallel operations

## 🔄 Migration from CommonServiceImpl

The `processScheduledOrders` method has been moved from `CommonServiceImpl` to `ScheduledOrderService` with the following improvements:

### **Enhancements Made**
1. **Better Error Handling**: Comprehensive exception management
2. **Parallel Processing**: Improved performance for large batches
3. **Enhanced Notifications**: Rich email notification system
4. **Status Management**: Detailed order status tracking
5. **Configurable Limits**: Max orders and end date support
6. **Pause/Resume**: Flexible order control

### **Migration Steps**
1. Deploy new `ScheduledOrderService`
2. Update existing scheduled orders with new fields
3. Disable old `processScheduledOrders` in `CommonServiceImpl`
4. Monitor new service performance
5. Remove old implementation after validation

## 📞 Support

For issues or questions regarding the ScheduledOrderService:
- Check logs for error details
- Review configuration settings
- Verify database connectivity
- Contact support team with relevant error messages and timestamps

---

**Version**: 1.0.0  
**Last Updated**: 2024-01-01  
**Maintainer**: ATP Product Team
