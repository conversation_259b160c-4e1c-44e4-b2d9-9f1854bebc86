# ecommerce-server


## Introduction

This is a Spring Boot application using Java 17 and MongoDB for the database. SonarQube is used for static code analysis to ensure code quality.

## Prerequisites

- Java 17
- MongoDB
- Maven
- SonarQube (optional, for code analysis)

## Getting Started

   mvn clean install

## Clone the repository

**************:ATOMM-XDM/ecomm-api-gateway-microservice.git
cd ecomm-api-gateway-microservice

## Configuration

# MongoDB configuration
spring.data.mongodb.uri=mongodb://localhost:27017/your-database-name

# Server configuration
server.port=8080

# Sonarqube Configuration
sonar.projectKey=your-project-key
sonar.host.url=http://localhost:9000
sonar.login=your-sonarqube-token

## Running the Application
Please replace below file name before running the application
path: src/main/resources
application.properties.sample --> application.properties

# Start Mongo
mongd

# Start spring boot api gateway service
mvn spring-boot:run

# Using SonarQube
Download Java Version 17 
Download SonarQuebe from https://www.sonarqube.org/downloads/ 
Please add below line at end of the file soanrqube-7.9.1 > conf > sonar.properties #sonar.host.url\=http://localhost:9000 Install sonarqube-scanner from https://www.npmjs.com/package/sonarqube-scanner 
Follow Steps given in this link https://yuriburger.net/2017/09/27/getting-started-with-sonarqube-and-typescript/ 
Except Rules Command to start sonarquebe serving on localhost:9000 $ sonarqube-7.9.1/bin/macosx-universal-64/sonar.sh start Start analysis of project with following command $ sonar-scanner

## Deployment with CI/CD and Docker file
coming soon 


