package com.atp.product.service.impl;

import com.atp.product.controller.dto.request.ScheduledOrderRequest;
import com.atp.product.controller.dto.request.UpdateScheduleRequest;
import com.atp.product.controller.dto.response.ScheduledOrderResponse;
import com.atp.product.karmak_responses.KarmakPurchaseOrder;
import com.atp.product.model.ScheduledOrder;
import com.atp.product.service.NotificationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive test class for ScheduledOrderService
 */
@ExtendWith(MockitoExtension.class)
class ScheduledOrderServiceTest {

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private PurchaseOrderService purchaseOrderService;

    @Mock
    private NotificationService notificationService;

    @Mock
    private CommonServiceImpl commonService;

    @InjectMocks
    private ScheduledOrderServiceImpl scheduledOrderService;

    private ScheduledOrderRequest validRequest;
    private ScheduledOrder sampleScheduledOrder;
    private KarmakPurchaseOrder samplePurchaseOrder;

    @BeforeEach
    void setUp() {
        validRequest = createValidScheduledOrderRequest();
        sampleScheduledOrder = createSampleScheduledOrder();
        samplePurchaseOrder = createSampleKarmakPurchaseOrder();
    }

    @Test
    void testSubscribe_Success() {
        // Given
        when(purchaseOrderService.getPurchaseOrderByExternalId(validRequest.getOriginalOrderId()))
                .thenReturn(samplePurchaseOrder);
        when(mongoTemplate.save(any(ScheduledOrder.class))).thenReturn(sampleScheduledOrder);

        // When
        ScheduledOrderResponse response = scheduledOrderService.subscribe(validRequest);

        // Then
        assertNotNull(response);
        assertEquals(validRequest.getCustomerId(), response.getCustomerId());
        assertEquals(validRequest.getOriginalOrderId(), response.getOriginalOrderId());
        assertEquals(validRequest.getFrequencyDays(), response.getFrequencyDays());
        assertTrue(response.getActive());
        assertEquals("ACTIVE", response.getStatus());

        verify(mongoTemplate).save(any(ScheduledOrder.class));
        verify(notificationService).sendScheduledOrderConfirmation(
                eq(validRequest.getCustomerId()), 
                anyString(), 
                anyString()
        );
    }

    @Test
    void testSubscribe_OriginalOrderNotFound() {
        // Given
        when(purchaseOrderService.getPurchaseOrderByExternalId(validRequest.getOriginalOrderId()))
                .thenReturn(null);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, 
                () -> scheduledOrderService.subscribe(validRequest));
        
        assertTrue(exception.getMessage().contains("Original order not found"));
        verify(mongoTemplate, never()).save(any(ScheduledOrder.class));
        verify(notificationService, never()).sendScheduledOrderConfirmation(anyString(), anyString(), anyString());
    }

    @Test
    void testCancel_Success() {
        // Given
        String scheduledOrderId = "order123";
        String customerId = "customer123";
        
        when(mongoTemplate.updateFirst(any(Query.class), any(Update.class), eq(ScheduledOrder.class)))
                .thenReturn(org.springframework.data.mongodb.core.query.UpdateResult.acknowledged(1, 1L, null));

        // When
        boolean result = scheduledOrderService.cancel(scheduledOrderId, customerId);

        // Then
        assertTrue(result);
        verify(mongoTemplate).updateFirst(any(Query.class), any(Update.class), eq(ScheduledOrder.class));
        verify(notificationService).sendScheduledOrderCancellation(customerId, scheduledOrderId);
    }

    @Test
    void testCancel_NotFound() {
        // Given
        String scheduledOrderId = "nonexistent";
        String customerId = "customer123";
        
        when(mongoTemplate.updateFirst(any(Query.class), any(Update.class), eq(ScheduledOrder.class)))
                .thenReturn(org.springframework.data.mongodb.core.query.UpdateResult.acknowledged(0, 0L, null));

        // When
        boolean result = scheduledOrderService.cancel(scheduledOrderId, customerId);

        // Then
        assertFalse(result);
        verify(notificationService, never()).sendScheduledOrderCancellation(anyString(), anyString());
    }

    @Test
    void testUpdateSchedule_Success() {
        // Given
        String scheduledOrderId = "order123";
        UpdateScheduleRequest updateRequest = createUpdateScheduleRequest();
        
        when(mongoTemplate.updateFirst(any(Query.class), any(Update.class), eq(ScheduledOrder.class)))
                .thenReturn(org.springframework.data.mongodb.core.query.UpdateResult.acknowledged(1, 1L, null));
        when(mongoTemplate.findOne(any(Query.class), eq(ScheduledOrder.class)))
                .thenReturn(sampleScheduledOrder);

        // When
        ScheduledOrderResponse response = scheduledOrderService.updateSchedule(scheduledOrderId, updateRequest);

        // Then
        assertNotNull(response);
        verify(mongoTemplate).updateFirst(any(Query.class), any(Update.class), eq(ScheduledOrder.class));
        verify(mongoTemplate).findOne(any(Query.class), eq(ScheduledOrder.class));
        verify(notificationService).sendScheduledOrderUpdate(
                eq(updateRequest.getCustomerId()), 
                eq(scheduledOrderId), 
                eq(updateRequest.getFrequencyDays())
        );
    }

    @Test
    void testGetScheduledOrdersByCustomer_Success() {
        // Given
        String customerId = "customer123";
        List<ScheduledOrder> orders = Arrays.asList(sampleScheduledOrder, createSampleScheduledOrder());
        
        when(mongoTemplate.find(any(Query.class), eq(ScheduledOrder.class))).thenReturn(orders);

        // When
        List<ScheduledOrderResponse> responses = scheduledOrderService.getScheduledOrdersByCustomer(customerId);

        // Then
        assertNotNull(responses);
        assertEquals(2, responses.size());
        verify(mongoTemplate).find(any(Query.class), eq(ScheduledOrder.class));
    }

    @Test
    void testGetScheduledOrderById_Success() {
        // Given
        String scheduledOrderId = "order123";
        String customerId = "customer123";
        
        when(mongoTemplate.findOne(any(Query.class), eq(ScheduledOrder.class)))
                .thenReturn(sampleScheduledOrder);

        // When
        ScheduledOrderResponse response = scheduledOrderService.getScheduledOrderById(scheduledOrderId, customerId);

        // Then
        assertNotNull(response);
        assertEquals(sampleScheduledOrder.getId(), response.getId());
        verify(mongoTemplate).findOne(any(Query.class), eq(ScheduledOrder.class));
    }

    @Test
    void testGetScheduledOrderById_NotFound() {
        // Given
        String scheduledOrderId = "nonexistent";
        String customerId = "customer123";
        
        when(mongoTemplate.findOne(any(Query.class), eq(ScheduledOrder.class)))
                .thenReturn(null);

        // When
        ScheduledOrderResponse response = scheduledOrderService.getScheduledOrderById(scheduledOrderId, customerId);

        // Then
        assertNull(response);
        verify(mongoTemplate).findOne(any(Query.class), eq(ScheduledOrder.class));
    }

    @Test
    void testPauseScheduledOrder_Success() {
        // Given
        String scheduledOrderId = "order123";
        String customerId = "customer123";
        
        when(mongoTemplate.updateFirst(any(Query.class), any(Update.class), eq(ScheduledOrder.class)))
                .thenReturn(org.springframework.data.mongodb.core.query.UpdateResult.acknowledged(1, 1L, null));

        // When
        boolean result = scheduledOrderService.pauseScheduledOrder(scheduledOrderId, customerId);

        // Then
        assertTrue(result);
        verify(mongoTemplate).updateFirst(any(Query.class), any(Update.class), eq(ScheduledOrder.class));
    }

    @Test
    void testResumeScheduledOrder_Success() {
        // Given
        String scheduledOrderId = "order123";
        String customerId = "customer123";
        
        when(mongoTemplate.updateFirst(any(Query.class), any(Update.class), eq(ScheduledOrder.class)))
                .thenReturn(org.springframework.data.mongodb.core.query.UpdateResult.acknowledged(1, 1L, null));

        // When
        boolean result = scheduledOrderService.resumeScheduledOrder(scheduledOrderId, customerId);

        // Then
        assertTrue(result);
        verify(mongoTemplate).updateFirst(any(Query.class), any(Update.class), eq(ScheduledOrder.class));
    }

    @Test
    void testGetAllActiveScheduledOrders_Success() {
        // Given
        List<ScheduledOrder> activeOrders = Arrays.asList(sampleScheduledOrder, createSampleScheduledOrder());
        
        when(mongoTemplate.find(any(Query.class), eq(ScheduledOrder.class))).thenReturn(activeOrders);

        // When
        List<ScheduledOrderResponse> responses = scheduledOrderService.getAllActiveScheduledOrders();

        // Then
        assertNotNull(responses);
        assertEquals(2, responses.size());
        verify(mongoTemplate).find(any(Query.class), eq(ScheduledOrder.class));
    }

    // Helper methods for creating test data
    private ScheduledOrderRequest createValidScheduledOrderRequest() {
        ScheduledOrderRequest request = new ScheduledOrderRequest();
        request.setCustomerId("customer123");
        request.setOriginalOrderId("order456");
        request.setFrequencyDays(30);
        request.setStartDate(LocalDate.now().plusDays(1));
        request.setEndDate(LocalDate.now().plusMonths(12));
        request.setNotes("Test scheduled order");
        request.setAutoRenew(true);
        request.setMaxOrders(12);
        request.setNotifyOnOutOfStock(true);
        request.setNotifyOnSuccess(true);
        return request;
    }

    private UpdateScheduleRequest createUpdateScheduleRequest() {
        UpdateScheduleRequest request = new UpdateScheduleRequest();
        request.setCustomerId("customer123");
        request.setFrequencyDays(45);
        request.setNextRunDate(LocalDate.now().plusDays(45));
        request.setNotes("Updated notes");
        request.setAutoRenew(false);
        request.setMaxOrders(6);
        return request;
    }

    private ScheduledOrder createSampleScheduledOrder() {
        ScheduledOrder order = new ScheduledOrder();
        order.setId("order123");
        order.setCustomerId("customer123");
        order.setOriginalOrderId("order456");
        order.setSubscriptionDate(LocalDate.now());
        order.setFrequencyDays(30);
        order.setNextRunDate(LocalDate.now().plusDays(30));
        order.setActive(true);
        order.setStatus("ACTIVE");
        order.setCreatedAt(LocalDateTime.now());
        order.setUpdatedAt(LocalDateTime.now());
        return order;
    }

    private KarmakPurchaseOrder createSampleKarmakPurchaseOrder() {
        KarmakPurchaseOrder order = new KarmakPurchaseOrder();
        order.setExternalId("order456");
        order.setName("Sample Purchase Order");
        return order;
    }
}
