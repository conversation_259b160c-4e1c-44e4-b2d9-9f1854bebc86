package com.atp.product.service.impl;

import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.data.mongodb.core.MongoTemplate;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

class BrandServiceImplTest {

    @Mock
    private MongoTemplate mongoTemplate;

    @InjectMocks
    private BrandServiceImpl brandServiceImpl;

    @InjectMocks
    private CommonServiceImpl commonServiceImpl;

    @Mock
    private ProductServiceImpl productServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /*@Test
    void testGetShopByBrands() {
        AggregationResults<BrandResponseHomePage> mockResults = mock(AggregationResults.class);
        when(mongoTemplate.aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(BrandResponseHomePage.class)))
                .thenReturn(mockResults);
        when(mockResults.getMappedResults()).thenReturn(Collections.emptyList());

        List<BrandResponseHomePage> result = brandServiceImpl.getShopByBrands();

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(mongoTemplate, times(1)).aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(BrandResponseHomePage.class));
    }*/
    /*@Test
    void testGetShopByBrands_Exception() {
        when(mongoTemplate.aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(BrandResponseHomePage.class)))
                .thenThrow(new RuntimeException("Aggregation error"));

        assertThrows(DomainException.class, () -> {
            brandServiceImpl.getShopByBrands();
        });

        verify(mongoTemplate, times(1))
                .aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(BrandResponseHomePage.class));
    }*/

   /* @Test
    void testGetBrandListByCategory() {
        AggregationResults<CategoryBrandResponse> mockResults = mock(AggregationResults.class);
        when(mongoTemplate.aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(CategoryBrandResponse.class)))
                .thenReturn(mockResults);
        when(mockResults.getMappedResults()).thenReturn(Collections.emptyList());

        List<CategoryBrandResponse> result = brandServiceImpl.getBrandListByCategory();

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(mongoTemplate, times(1)).aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(CategoryBrandResponse.class));
    }*/
    /*@Test
    void testGetBrandListByCategory_Exception() {
        when(mongoTemplate.aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(CategoryBrandResponse.class)))
                .thenThrow(new RuntimeException("Aggregation error"));

        assertThrows(DomainException.class, () -> {
            brandServiceImpl.getBrandListByCategory();
        });

        verify(mongoTemplate, times(1))
                .aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(CategoryBrandResponse.class));
    }*/

    /*@Test
    void testGetBrandPageByBrandId() throws IOException {
        //Arrange
        String requestJson= commonServiceImpl.readFile("src/test/resources/RequestJson/GetBrandPageByBrandId.json");
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String,String> properties = objectMapper.readValue(requestJson, Map.class);

        // Arrange
        String responseJson= commonServiceImpl.readFile("src/test/resources/ResponseJson/GetBrandPageByBrandIdCategoryResponse.json");
        objectMapper = new ObjectMapper();
        List<CategoryResponse> mockResults =objectMapper.readValue(responseJson, new TypeReference<List<CategoryResponse>>(){});

        Product product = new Product();
        product.setBrandName("MOOG Chassis Products");

        when(mongoTemplate.findOne(any(Query.class), eq(Product.class), eq(Constants.PRODUCT_COLLECTION_NAME)))
                .thenReturn(product);

        List<CategoryResponse> categoryList = mockResults;
        AggregationResults<BrandPageResponse> categoryAggregationResults = mock(AggregationResults.class);
        BrandPageResponse categoryResponse = new BrandPageResponse();
        categoryResponse.setCategoryList(categoryList);
        when(categoryAggregationResults.getUniqueMappedResult()).thenReturn(categoryResponse);

        when(mongoTemplate.aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(BrandPageResponse.class)))
                .thenReturn(categoryAggregationResults);

        // Arrange
        responseJson= commonServiceImpl.readFile("src/test/resources/ResponseJson/GetBrandPageByBrandIdNewProductResponse.json");
        objectMapper = new ObjectMapper();
        List<ProductResponseHomePage> mockResultsProduct =objectMapper.readValue(responseJson, new TypeReference<List<ProductResponseHomePage>>(){});

        List<ProductResponseHomePage> newProductList = mockResultsProduct;
        AggregationResults<ProductResponseHomePage> newProductAggregationResults = mock(AggregationResults.class);
        when(newProductAggregationResults.getMappedResults()).thenReturn(newProductList);

        when(mongoTemplate.aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(ProductResponseHomePage.class)))
                .thenReturn(newProductAggregationResults);

        BrandPageResponse result = brandServiceImpl.getBrandPageByBrandId(properties.get("brandId").toString());

        assertNotNull(result);
        assertEquals("MOOG Chassis Products", result.getBrandName());
        assertEquals(categoryList, result.getCategoryList());
        assertEquals(newProductList, result.getNewProductList());
        assertTrue(result.getPopularProductList().isEmpty());
        assertEquals(newProductList, result.getNewProductList());
    }*/
    /*@Test
    void testGetCategoryListByBrandId() throws IOException {
        //Arrange
        String requestJson= commonServiceImpl.readFile("src/test/resources/RequestJson/GetBrandPageByBrandId.json");
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String,String> properties = objectMapper.readValue(requestJson, Map.class);

        // Arrange
        String responseJson= commonServiceImpl.readFile("src/test/resources/ResponseJson/GetBrandPageByBrandIdCategoryResponse.json");
        objectMapper = new ObjectMapper();
        List<CategoryResponse> mockResults =objectMapper.readValue(responseJson, new TypeReference<List<CategoryResponse>>(){});

        List<CategoryResponse> categoryList = mockResults;
        AggregationResults<BrandPageResponse> aggregationResults = mock(AggregationResults.class);
        BrandPageResponse brandPageResponse = new BrandPageResponse();
        brandPageResponse.setCategoryList(categoryList);
        when(aggregationResults.getUniqueMappedResult()).thenReturn(brandPageResponse);

        when(mongoTemplate.aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(BrandPageResponse.class)))
                .thenReturn(aggregationResults);

        List<CategoryResponse> result = brandServiceImpl.getCategoryListByBrandId(properties.get("brandId").toString());

        assertEquals(categoryList, result);
    }*/

   /* @Test
    void testGetNewProductsByBrandId() throws IOException {
        //Arrange
        String requestJson= commonServiceImpl.readFile("src/test/resources/RequestJson/GetBrandPageByBrandId.json");
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String,String> properties = objectMapper.readValue(requestJson, Map.class);

        // Arrange
        String responseJson= commonServiceImpl.readFile("src/test/resources/ResponseJson/GetBrandPageByBrandIdNewProductResponse.json");
        objectMapper = new ObjectMapper();
        List<ProductResponseHomePage> mockResultsProduct =objectMapper.readValue(responseJson, new TypeReference<List<ProductResponseHomePage>>(){});

        List<ProductResponseHomePage> newProductList = mockResultsProduct;

        AggregationResults<ProductResponseHomePage> aggregationResults = mock(AggregationResults.class);
        when(aggregationResults.getMappedResults()).thenReturn(newProductList);

        when(mongoTemplate.aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(ProductResponseHomePage.class)))
                .thenReturn(aggregationResults);

        ProjectionOperation mockProjectionOperation = mock(ProjectionOperation.class);
        when(productServiceImpl.getProjectionOperation()).thenReturn(mockProjectionOperation);

        List<ProductResponseHomePage> result = brandServiceImpl.getNewProductsByBrandId(properties.get("brandId").toString());

        assertEquals(newProductList, result);
    }*/

}