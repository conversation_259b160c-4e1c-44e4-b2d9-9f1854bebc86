package com.atp.product.service.impl;

import okhttp3.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Test class for authentication functionality in ScheduledOrderService
 */
@ExtendWith(MockitoExtension.class)
class AuthenticationTest {

    @Mock
    private OkHttpClient httpClient;

    @Mock
    private Call call;

    @Mock
    private Response response;

    @Mock
    private ResponseBody responseBody;

    @InjectMocks
    private ScheduledOrderServiceImpl scheduledOrderService;

    @BeforeEach
    void setUp() {
        // Set up configuration properties
        ReflectionTestUtils.setField(scheduledOrderService, "loginApiUrl", 
                "http://localhost:8080/server/rest/auth/login");
        ReflectionTestUtils.setField(scheduledOrderService, "apiUsername", "IshaniPandya");
        ReflectionTestUtils.setField(scheduledOrderService, "apiPassword", "Indianic@123");
        ReflectionTestUtils.setField(scheduledOrderService, "httpClient", httpClient);
    }

    @Test
    void testGetAuthToken_Success() throws IOException {
        // Given
        String mockResponseBody = "{\n" +
                "    \"status\": 1,\n" +
                "    \"userId\": \"IshaniPandya\",\n" +
                "    \"tenantId\": \"ActionTruckDEV\",\n" +
                "    \"token\": \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.test.token\",\n" +
                "    \"roles\": [\"Customers\"],\n" +
                "    \"name\": \"Ishani Pandya\"\n" +
                "}";

        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(mockResponseBody);
        when(call.execute()).thenReturn(response);
        when(httpClient.newCall(any(Request.class))).thenReturn(call);

        // When
        String token = ReflectionTestUtils.invokeMethod(scheduledOrderService, "getAuthToken");

        // Then
        assertNotNull(token);
        assertEquals("eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.test.token", token);
        verify(httpClient).newCall(any(Request.class));
        verify(call).execute();
    }

    @Test
    void testGetAuthToken_AuthenticationFailure() throws IOException {
        // Given
        String mockResponseBody = "{\n" +
                "    \"status\": 0,\n" +
                "    \"message\": \"Invalid credentials\"\n" +
                "}";

        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(mockResponseBody);
        when(call.execute()).thenReturn(response);
        when(httpClient.newCall(any(Request.class))).thenReturn(call);

        // When
        String token = ReflectionTestUtils.invokeMethod(scheduledOrderService, "getAuthToken");

        // Then
        assertNull(token);
        verify(httpClient).newCall(any(Request.class));
        verify(call).execute();
    }

    @Test
    void testGetAuthToken_HttpFailure() throws IOException {
        // Given
        when(response.isSuccessful()).thenReturn(false);
        when(response.code()).thenReturn(401);
        when(response.message()).thenReturn("Unauthorized");
        when(call.execute()).thenReturn(response);
        when(httpClient.newCall(any(Request.class))).thenReturn(call);

        // When
        String token = ReflectionTestUtils.invokeMethod(scheduledOrderService, "getAuthToken");

        // Then
        assertNull(token);
        verify(httpClient).newCall(any(Request.class));
        verify(call).execute();
    }

    @Test
    void testGetAuthToken_NetworkException() throws IOException {
        // Given
        when(call.execute()).thenThrow(new IOException("Network error"));
        when(httpClient.newCall(any(Request.class))).thenReturn(call);

        // When
        String token = ReflectionTestUtils.invokeMethod(scheduledOrderService, "getAuthToken");

        // Then
        assertNull(token);
        verify(httpClient).newCall(any(Request.class));
        verify(call).execute();
    }

    @Test
    void testGetAuthToken_CachedToken() throws IOException {
        // Given - Set up a cached token
        ReflectionTestUtils.setField(scheduledOrderService, "cachedToken", "cached.jwt.token");
        ReflectionTestUtils.setField(scheduledOrderService, "tokenExpiryTime", 
                System.currentTimeMillis() + 60000); // 1 minute in future

        // When
        String token = ReflectionTestUtils.invokeMethod(scheduledOrderService, "getAuthToken");

        // Then
        assertEquals("cached.jwt.token", token);
        // Should not make HTTP call when using cached token
        verify(httpClient, never()).newCall(any(Request.class));
    }

    @Test
    void testGetAuthToken_ExpiredCachedToken() throws IOException {
        // Given - Set up an expired cached token
        ReflectionTestUtils.setField(scheduledOrderService, "cachedToken", "expired.jwt.token");
        ReflectionTestUtils.setField(scheduledOrderService, "tokenExpiryTime", 
                System.currentTimeMillis() - 60000); // 1 minute in past

        String mockResponseBody = "{\n" +
                "    \"status\": 1,\n" +
                "    \"token\": \"new.jwt.token\"\n" +
                "}";

        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn(mockResponseBody);
        when(call.execute()).thenReturn(response);
        when(httpClient.newCall(any(Request.class))).thenReturn(call);

        // When
        String token = ReflectionTestUtils.invokeMethod(scheduledOrderService, "getAuthToken");

        // Then
        assertEquals("new.jwt.token", token);
        // Should make HTTP call to get new token
        verify(httpClient).newCall(any(Request.class));
        verify(call).execute();
    }

    @Test
    void testAuthenticationRequestPayload() throws IOException {
        // Given
        when(response.isSuccessful()).thenReturn(true);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn("{\"status\": 1, \"token\": \"test.token\"}");
        when(call.execute()).thenReturn(response);
        when(httpClient.newCall(any(Request.class))).thenReturn(call);

        // When
        ReflectionTestUtils.invokeMethod(scheduledOrderService, "getAuthToken");

        // Then
        verify(httpClient).newCall(argThat(request -> {
            // Verify the request is properly formed
            assertEquals("POST", request.method());
            assertEquals("http://localhost:8080/server/rest/auth/login", request.url().toString());
            
            // Verify headers
            assertEquals("application/json", request.header("Content-Type"));
            
            return true;
        }));
    }
}
