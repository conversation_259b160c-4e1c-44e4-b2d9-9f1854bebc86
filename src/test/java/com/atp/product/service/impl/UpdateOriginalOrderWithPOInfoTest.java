package com.atp.product.service.impl;

import com.atp.product.karmak_responses.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for updateOriginalOrderWithPOInfo functionality
 */
class UpdateOriginalOrderWithPOInfoTest {

    private ScheduledOrderServiceImpl scheduledOrderService;
    private KarmakPurchaseOrder originalOrder;

    @BeforeEach
    void setUp() {
        scheduledOrderService = new ScheduledOrderServiceImpl(null, null, null, null);
        originalOrder = createSampleKarmakPurchaseOrder();
    }

    @Test
    void testUpdateOriginalOrderWithPOInfo_NewFields() {
        // Given
        String partsPurchaseOrderID = "12112";
        String poNumber = "200645";

        // When
        ReflectionTestUtils.invokeMethod(scheduledOrderService, "updateOriginalOrderWithPOInfo", 
                originalOrder, partsPurchaseOrderID, poNumber);

        // Then
        List<Value> values = originalOrder.getValues();
        assertNotNull(values);
        
        // Verify PartsPurchaseOrderID was added
        Value partsPOIDValue = values.stream()
                .filter(v -> "PartsPurchaseOrderID".equals(v.getAttributeId()))
                .findFirst()
                .orElse(null);
        assertNotNull(partsPOIDValue, "PartsPurchaseOrderID should be added");
        assertEquals("12112", partsPOIDValue.getContent().get(0).getValue());

        // Verify PONumber was added
        Value poNumberValue = values.stream()
                .filter(v -> "PONumber".equals(v.getAttributeId()))
                .findFirst()
                .orElse(null);
        assertNotNull(poNumberValue, "PONumber should be added");
        assertEquals("200645", poNumberValue.getContent().get(0).getValue());
    }

    @Test
    void testUpdateOriginalOrderWithPOInfo_UpdateExistingFields() {
        // Given - Add existing fields first
        String initialPartsPOID = "11111";
        String initialPONumber = "100001";
        
        addValueToOrder(originalOrder, "PartsPurchaseOrderID", initialPartsPOID);
        addValueToOrder(originalOrder, "PONumber", initialPONumber);

        // When - Update with new values
        String newPartsPurchaseOrderID = "12112";
        String newPONumber = "200645";
        
        ReflectionTestUtils.invokeMethod(scheduledOrderService, "updateOriginalOrderWithPOInfo", 
                originalOrder, newPartsPurchaseOrderID, newPONumber);

        // Then
        List<Value> values = originalOrder.getValues();
        
        // Verify PartsPurchaseOrderID was updated
        Value partsPOIDValue = values.stream()
                .filter(v -> "PartsPurchaseOrderID".equals(v.getAttributeId()))
                .findFirst()
                .orElse(null);
        assertNotNull(partsPOIDValue);
        assertEquals("12112", partsPOIDValue.getContent().get(0).getValue());

        // Verify PONumber was updated
        Value poNumberValue = values.stream()
                .filter(v -> "PONumber".equals(v.getAttributeId()))
                .findFirst()
                .orElse(null);
        assertNotNull(poNumberValue);
        assertEquals("200645", poNumberValue.getContent().get(0).getValue());

        // Verify no duplicate fields were created
        long partsPOIDCount = values.stream()
                .filter(v -> "PartsPurchaseOrderID".equals(v.getAttributeId()))
                .count();
        assertEquals(1, partsPOIDCount, "Should have only one PartsPurchaseOrderID field");

        long poNumberCount = values.stream()
                .filter(v -> "PONumber".equals(v.getAttributeId()))
                .count();
        assertEquals(1, poNumberCount, "Should have only one PONumber field");
    }

    @Test
    void testUpdateOriginalOrderWithPOInfo_NullValues() {
        // Given
        originalOrder.setValues(null);

        // When
        ReflectionTestUtils.invokeMethod(scheduledOrderService, "updateOriginalOrderWithPOInfo", 
                originalOrder, "12112", "200645");

        // Then
        List<Value> values = originalOrder.getValues();
        assertNotNull(values, "Values list should be created if null");
        assertEquals(2, values.size(), "Should have 2 new fields");
    }

    @Test
    void testUpdateOriginalOrderWithPOInfo_EmptyValues() {
        // Given
        originalOrder.setValues(new ArrayList<>());

        // When
        ReflectionTestUtils.invokeMethod(scheduledOrderService, "updateOriginalOrderWithPOInfo", 
                originalOrder, "12112", "200645");

        // Then
        List<Value> values = originalOrder.getValues();
        assertEquals(2, values.size(), "Should have 2 new fields");
    }

    @Test
    void testUpdateOriginalOrderWithPOInfo_PreservesExistingFields() {
        // Given - Order already has some fields
        int initialFieldCount = originalOrder.getValues().size();

        // When
        ReflectionTestUtils.invokeMethod(scheduledOrderService, "updateOriginalOrderWithPOInfo", 
                originalOrder, "12112", "200645");

        // Then
        List<Value> values = originalOrder.getValues();
        assertEquals(initialFieldCount + 2, values.size(), "Should preserve existing fields and add 2 new ones");

        // Verify existing fields are still there
        Value existingField = values.stream()
                .filter(v -> "ECM_OrderDate".equals(v.getAttributeId()))
                .findFirst()
                .orElse(null);
        assertNotNull(existingField, "Existing fields should be preserved");
    }

    @Test
    void testUpdateOrAddValueField_NewField() {
        // Given
        List<Value> values = new ArrayList<>();

        // When
        ReflectionTestUtils.invokeMethod(scheduledOrderService, "updateOrAddValueField", 
                values, "TestField", "TestValue");

        // Then
        assertEquals(1, values.size());
        assertEquals("TestField", values.get(0).getAttributeId());
        assertEquals("TestValue", values.get(0).getContent().get(0).getValue());
    }

    @Test
    void testUpdateOrAddValueField_UpdateExisting() {
        // Given
        List<Value> values = new ArrayList<>();
        addValueToList(values, "TestField", "OldValue");

        // When
        ReflectionTestUtils.invokeMethod(scheduledOrderService, "updateOrAddValueField", 
                values, "TestField", "NewValue");

        // Then
        assertEquals(1, values.size(), "Should not create duplicate fields");
        assertEquals("TestField", values.get(0).getAttributeId());
        assertEquals("NewValue", values.get(0).getContent().get(0).getValue());
    }

    // Helper methods
    private KarmakPurchaseOrder createSampleKarmakPurchaseOrder() {
        KarmakPurchaseOrder order = new KarmakPurchaseOrder();
        order.setExternalId("TEST_ORDER_001");
        order.setParentId("CSTM_1185");

        // Create sample Values
        List<Value> values = Arrays.asList(
            createValue("ECM_OrderDate", "07/10/2025"),
            createValue("OrderStatus", "Order Placed/New Order"),
            createValue("ECM_GrandTotal", 24.69)
        );
        order.setValues(values);

        return order;
    }

    private Value createValue(String attributeId, Object value) {
        Value val = new Value();
        val.setAttributeId(attributeId);
        Content content = new Content();
        content.setValue(value);
        val.setContent(Arrays.asList(content));
        return val;
    }

    private void addValueToOrder(KarmakPurchaseOrder order, String attributeId, String value) {
        List<Value> values = order.getValues();
        if (values == null) {
            values = new ArrayList<>();
            order.setValues(values);
        }
        addValueToList(values, attributeId, value);
    }

    private void addValueToList(List<Value> values, String attributeId, String value) {
        Value val = new Value();
        val.setAttributeId(attributeId);
        Content content = new Content();
        content.setValue(value);
        val.setContent(Arrays.asList(content));
        values.add(val);
    }
}
