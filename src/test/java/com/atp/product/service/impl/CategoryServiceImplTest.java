package com.atp.product.service.impl;

import com.atp.product.controller.dto.response.CategorySubcategoryResponse;
import com.atp.product.utils.Constants;
import com.atp.product.exception.DomainException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class CategoryServiceImplTest {

    @Autowired
    private ResourceLoader resourceLoader;

    @Mock
    private MongoTemplate mongoTemplate;

    @InjectMocks
    private CommonServiceImpl commonServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

   /* @Test
    void testGetCategorySubCategoryList() {
        AggregationResults<CategorySubcategoryResponse> mockResults = mock(AggregationResults.class);
        when(mongoTemplate.aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(CategorySubcategoryResponse.class)))
                .thenReturn(mockResults);
        when(mockResults.getMappedResults()).thenReturn(Collections.emptyList());

        List<CategorySubcategoryResponse> result = categoryServiceImpl.getCategorySubCategoryList();

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(mongoTemplate, times(1)).aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(CategorySubcategoryResponse.class));
    }

    @Test
    void testGetCategorySubCategoryListThrowsException() {
        when(mongoTemplate.aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(CategorySubcategoryResponse.class)))
                .thenThrow(new RuntimeException("Aggregation error"));

        assertThrows(DomainException.class, () -> {
            categoryServiceImpl.getCategorySubCategoryList();
        });

        verify(mongoTemplate, times(1))
                .aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(CategorySubcategoryResponse.class));
    }*/
}
