package com.atp.product.service.impl;

import com.atp.product.karmak_responses.KarmakPurchaseOrder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Performance test comparing different data retrieval methods for invoice generation
 */
@ExtendWith(MockitoExtension.class)
class InvoicePerformanceTest {

    @Mock
    private PurchaseOrderService purchaseOrderService;

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private AggregationResults<KarmakPurchaseOrder> aggregationResults;

    @InjectMocks
    private InvoiceServiceImpl invoiceService;

    private KarmakPurchaseOrder sampleOrder;

    @BeforeEach
    void setUp() {
        sampleOrder = createSampleOrder();
    }

    @Test
    void testOptimizedAggregationMethod() {
        // Given
        String externalId = "ORDER_12345";
        when(aggregationResults.getMappedResults()).thenReturn(Arrays.asList(sampleOrder));
        when(mongoTemplate.aggregate(any(Aggregation.class), eq("karmak_purchase_orders"), eq(KarmakPurchaseOrder.class)))
                .thenReturn(aggregationResults);

        // When
        long startTime = System.nanoTime();
        KarmakPurchaseOrder result = ReflectionTestUtils.invokeMethod(
                invoiceService, "getOrderByExternalIdOptimized", externalId);
        long endTime = System.nanoTime();

        // Then
        assertNotNull(result);
        assertEquals(sampleOrder.getExternalId(), result.getExternalId());
        
        // Verify aggregation was called (more efficient than service layer)
        verify(mongoTemplate).aggregate(any(Aggregation.class), eq("karmak_purchase_orders"), eq(KarmakPurchaseOrder.class));
        verify(purchaseOrderService, never()).getPurchaseOrderByExternalId(anyString());
        
        System.out.println("Optimized Aggregation Time: " + (endTime - startTime) / 1_000_000 + " ms");
    }

    @Test
    void testOptimizedDirectQueryMethod() {
        // Given
        String orderId = "ORDER_ID_123";
        when(mongoTemplate.findOne(any(Query.class), eq(KarmakPurchaseOrder.class), eq("karmak_purchase_orders")))
                .thenReturn(sampleOrder);

        // When
        long startTime = System.nanoTime();
        KarmakPurchaseOrder result = ReflectionTestUtils.invokeMethod(
                invoiceService, "getOrderByIdOptimized", orderId);
        long endTime = System.nanoTime();

        // Then
        assertNotNull(result);
        assertEquals(sampleOrder.getId(), result.getId());
        
        // Verify direct query was called
        verify(mongoTemplate).findOne(any(Query.class), eq(KarmakPurchaseOrder.class), eq("karmak_purchase_orders"));
        verify(purchaseOrderService, never()).getPurchaseOrderById(anyString());
        
        System.out.println("Optimized Direct Query Time: " + (endTime - startTime) / 1_000_000 + " ms");
    }

    @Test
    void testFallbackToServiceLayer() {
        // Given
        String externalId = "ORDER_12345";
        when(mongoTemplate.aggregate(any(Aggregation.class), anyString(), any(Class.class)))
                .thenThrow(new RuntimeException("Database error"));
        when(purchaseOrderService.getPurchaseOrderByExternalId(externalId))
                .thenReturn(sampleOrder);

        // When
        KarmakPurchaseOrder result = ReflectionTestUtils.invokeMethod(
                invoiceService, "getOrderByExternalIdOptimized", externalId);

        // Then
        assertNotNull(result);
        assertEquals(sampleOrder.getExternalId(), result.getExternalId());
        
        // Verify fallback to service layer
        verify(purchaseOrderService).getPurchaseOrderByExternalId(externalId);
    }

    @Test
    void testAggregationWithFieldProjection() {
        // Given
        String externalId = "ORDER_12345";
        when(aggregationResults.getMappedResults()).thenReturn(Arrays.asList(sampleOrder));
        when(mongoTemplate.aggregate(any(Aggregation.class), eq("karmak_purchase_orders"), eq(KarmakPurchaseOrder.class)))
                .thenReturn(aggregationResults);

        // When
        ReflectionTestUtils.invokeMethod(invoiceService, "getOrderByExternalIdOptimized", externalId);

        // Then
        verify(mongoTemplate).aggregate(argThat(aggregation -> {
            // Verify that the aggregation includes field projection
            String aggregationString = aggregation.toString();
            return aggregationString.contains("$match") && aggregationString.contains("$project");
        }), eq("karmak_purchase_orders"), eq(KarmakPurchaseOrder.class));
    }

    @Test
    void testDirectQueryWithFieldProjection() {
        // Given
        String orderId = "ORDER_ID_123";
        when(mongoTemplate.findOne(any(Query.class), eq(KarmakPurchaseOrder.class), eq("karmak_purchase_orders")))
                .thenReturn(sampleOrder);

        // When
        ReflectionTestUtils.invokeMethod(invoiceService, "getOrderByIdOptimized", orderId);

        // Then
        verify(mongoTemplate).findOne(argThat(query -> {
            // Verify that the query includes field projection
            return query.getFieldsObject() != null && !query.getFieldsObject().isEmpty();
        }), eq(KarmakPurchaseOrder.class), eq("karmak_purchase_orders"));
    }

    @Test
    void performanceComparison() {
        // This test demonstrates the performance difference between methods
        String externalId = "ORDER_12345";
        
        // Setup mocks for both methods
        when(aggregationResults.getMappedResults()).thenReturn(Arrays.asList(sampleOrder));
        when(mongoTemplate.aggregate(any(Aggregation.class), anyString(), any(Class.class)))
                .thenReturn(aggregationResults);
        when(purchaseOrderService.getPurchaseOrderByExternalId(externalId))
                .thenReturn(sampleOrder);

        // Test optimized method
        long optimizedStart = System.nanoTime();
        KarmakPurchaseOrder optimizedResult = ReflectionTestUtils.invokeMethod(
                invoiceService, "getOrderByExternalIdOptimized", externalId);
        long optimizedEnd = System.nanoTime();
        long optimizedTime = optimizedEnd - optimizedStart;

        // Test service layer method (simulate)
        long serviceStart = System.nanoTime();
        KarmakPurchaseOrder serviceResult = purchaseOrderService.getPurchaseOrderByExternalId(externalId);
        long serviceEnd = System.nanoTime();
        long serviceTime = serviceEnd - serviceStart;

        // Results
        assertNotNull(optimizedResult);
        assertNotNull(serviceResult);
        
        System.out.println("=== PERFORMANCE COMPARISON ===");
        System.out.println("Optimized Aggregation: " + optimizedTime / 1_000_000 + " ms");
        System.out.println("Service Layer: " + serviceTime / 1_000_000 + " ms");
        System.out.println("Performance Improvement: " + 
                          (serviceTime > optimizedTime ? "Optimized is faster" : "Service layer is faster"));
    }

    private KarmakPurchaseOrder createSampleOrder() {
        KarmakPurchaseOrder order = new KarmakPurchaseOrder();
        order.setId("ORDER_ID_123");
        order.setExternalId("ORDER_12345");
        return order;
    }
}
