package com.atp.product.service.impl;

import com.atp.product.karmak_responses.*;
import okhttp3.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Test class for the placeOrder method in ScheduledOrderService
 */
@ExtendWith(MockitoExtension.class)
class PlaceOrderTest {

    @Mock
    private OkHttpClient httpClient;

    @Mock
    private Call call;

    @Mock
    private Response response;

    @Mock
    private ResponseBody responseBody;

    @InjectMocks
    private ScheduledOrderServiceImpl scheduledOrderService;

    private KarmakPurchaseOrder sampleOrder;

    @BeforeEach
    void setUp() {
        // Set up configuration properties
        ReflectionTestUtils.setField(scheduledOrderService, "thirdPartyApiUrl", 
                "http://localhost:8080/server/api/data-entities/create");
        ReflectionTestUtils.setField(scheduledOrderService, "authorizationToken", 
                "test-auth-token");
        ReflectionTestUtils.setField(scheduledOrderService, "apiTimeoutSeconds", 30);

        // Create sample order
        sampleOrder = createSampleKarmakPurchaseOrder();
    }

    @Test
    void testPlaceOrder_Success() throws IOException {
        // Given
        when(response.isSuccessful()).thenReturn(true);
        when(response.code()).thenReturn(200);
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn("{\"status\":\"success\"}");
        when(call.execute()).thenReturn(response);
        when(httpClient.newCall(any(Request.class))).thenReturn(call);

        // Set the mocked httpClient
        ReflectionTestUtils.setField(scheduledOrderService, "httpClient", httpClient);

        // When
        boolean result = scheduledOrderService.placeOrder(sampleOrder);

        // Then
        assertTrue(result);
        verify(httpClient).newCall(any(Request.class));
        verify(call).execute();
        verify(response).isSuccessful();
    }

    @Test
    void testPlaceOrder_ApiFailure() throws IOException {
        // Given
        when(response.isSuccessful()).thenReturn(false);
        when(response.code()).thenReturn(400);
        when(response.message()).thenReturn("Bad Request");
        when(response.body()).thenReturn(responseBody);
        when(responseBody.string()).thenReturn("{\"error\":\"Invalid request\"}");
        when(call.execute()).thenReturn(response);
        when(httpClient.newCall(any(Request.class))).thenReturn(call);

        // Set the mocked httpClient
        ReflectionTestUtils.setField(scheduledOrderService, "httpClient", httpClient);

        // When
        boolean result = scheduledOrderService.placeOrder(sampleOrder);

        // Then
        assertFalse(result);
        verify(httpClient).newCall(any(Request.class));
        verify(call).execute();
        verify(response).isSuccessful();
    }

    @Test
    void testPlaceOrder_IOException() throws IOException {
        // Given
        when(call.execute()).thenThrow(new IOException("Network error"));
        when(httpClient.newCall(any(Request.class))).thenReturn(call);

        // Set the mocked httpClient
        ReflectionTestUtils.setField(scheduledOrderService, "httpClient", httpClient);

        // When
        boolean result = scheduledOrderService.placeOrder(sampleOrder);

        // Then
        assertFalse(result);
        verify(httpClient).newCall(any(Request.class));
        verify(call).execute();
    }

    @Test
    void testPlaceOrder_NullOrder() {
        // When & Then
        assertThrows(NullPointerException.class, () -> {
            scheduledOrderService.placeOrder(null);
        });
    }

    @Test
    void testBuildOrderPayload() {
        // This test would require making buildOrderPayload method package-private or public
        // For now, we test it indirectly through placeOrder
        
        // Given
        when(response.isSuccessful()).thenReturn(true);
        when(response.code()).thenReturn(200);
        when(call.execute()).thenReturn(response);
        when(httpClient.newCall(any(Request.class))).thenReturn(call);

        // Set the mocked httpClient
        ReflectionTestUtils.setField(scheduledOrderService, "httpClient", httpClient);

        // When
        boolean result = scheduledOrderService.placeOrder(sampleOrder);

        // Then
        assertTrue(result);
        
        // Verify that the request was made with proper JSON payload
        verify(httpClient).newCall(argThat(request -> {
            RequestBody body = request.body();
            return body != null && body.contentType() != null && 
                   body.contentType().toString().contains("application/json");
        }));
    }

    private KarmakPurchaseOrder createSampleKarmakPurchaseOrder() {
        KarmakPurchaseOrder order = new KarmakPurchaseOrder();
        order.setExternalId("TEST_ORDER_001");
        order.setParentId("CSTM_1185");
        order.setEntityType("DE");
        order.setWorkspaceId("Maintenance");
        order.setDatatypeDefinitionId("PurchaseOrder");
        order.setName("Test Purchase Order");
        order.setCompleteness(100L);
        order.setDeleted(false);
        order.setSystemEntity(false);
        order.setApprovalStatus("NOT_APPROVED");
        order.setPath(Arrays.asList("MainEntityRoot", "CustomersHierarchy", "CSTM_1185"));

        // Create complex values
        ComplexValues complexValues = new ComplexValues();
        PurchaseOrder purchaseOrder = new PurchaseOrder();
        purchaseOrder.setType("PurchaseOrders");
        purchaseOrder.setName("PurchaseOrders");

        // Create values
        Values values = new Values();
        
        // OrderPartNumber
        OrderPartNumber orderPartNumber = new OrderPartNumber();
        orderPartNumber.setAttributeId("OrderPartNumber");
        Content partNumberContent = new Content();
        partNumberContent.setValue("577.99501");
        orderPartNumber.setContent(Arrays.asList(partNumberContent));
        values.setOrderPartNumber(orderPartNumber);

        // OrderCost
        OrderCost orderCost = new OrderCost();
        orderCost.setAttributeId("OrderCost");
        Content costContent = new Content();
        costContent.setValue("32.69");
        orderCost.setContent(Arrays.asList(costContent));
        values.setOrderCost(orderCost);

        // OrderQuantity
        OrderQuantity orderQuantity = new OrderQuantity();
        orderQuantity.setAttributeId("OrderQuantity");
        Content quantityContent = new Content();
        quantityContent.setValue("1");
        orderQuantity.setContent(Arrays.asList(quantityContent));
        values.setOrderQuantity(orderQuantity);

        // OrderProductDetails
        OrderProductDetails orderProductDetails = new OrderProductDetails();
        orderProductDetails.setAttributeId("OrderProductDetails");
        Content productDetailsContent = new Content();
        productDetailsContent.setValue("EATON SPEED SENSOR");
        orderProductDetails.setContent(Arrays.asList(productDetailsContent));
        values.setOrderProductDetails(orderProductDetails);

        purchaseOrder.setValues(values);
        complexValues.setPurchaseOrders(Arrays.asList(purchaseOrder));
        order.setComplexValues(complexValues);

        return order;
    }
}
