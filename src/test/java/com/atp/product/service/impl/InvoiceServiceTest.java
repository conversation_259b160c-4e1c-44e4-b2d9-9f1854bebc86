package com.atp.product.service.impl;

import com.atp.product.karmak_responses.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.ByteArrayResource;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Test class for InvoiceService
 */
@ExtendWith(MockitoExtension.class)
class InvoiceServiceTest {

    @Mock
    private PurchaseOrderService purchaseOrderService;

    @InjectMocks
    private InvoiceServiceImpl invoiceService;

    private KarmakPurchaseOrder sampleOrder;

    @BeforeEach
    void setUp() {
        sampleOrder = createSampleKarmakPurchaseOrder();
    }

    @Test
    void testGenerateInvoice_Success() {
        // Given
        String name = "<PERSON>";
        String email = "<EMAIL>";
        String externalId = "ORDER_12345";

        when(purchaseOrderService.getPurchaseOrderByExternalId(externalId))
                .thenReturn(sampleOrder);

        // When
        ByteArrayResource result = invoiceService.generateInvoice(name, email, externalId);

        // Then
        assertNotNull(result);
        assertTrue(result.contentLength() > 0);
        verify(purchaseOrderService).getPurchaseOrderByExternalId(externalId);
    }

    @Test
    void testGenerateInvoice_OrderNotFound() {
        // Given
        String name = "John Doe";
        String email = "<EMAIL>";
        String externalId = "NONEXISTENT_ORDER";

        when(purchaseOrderService.getPurchaseOrderByExternalId(externalId))
                .thenReturn(null);

        // When
        ByteArrayResource result = invoiceService.generateInvoice(name, email, externalId);

        // Then
        assertNull(result);
        verify(purchaseOrderService).getPurchaseOrderByExternalId(externalId);
    }

    @Test
    void testGenerateInvoiceById_Success() {
        // Given
        String orderId = "ORDER_ID_123";
        String name = "Jane Smith";
        String email = "<EMAIL>";

        when(purchaseOrderService.getPurchaseOrderById(orderId))
                .thenReturn(sampleOrder);

        // When
        ByteArrayResource result = invoiceService.generateInvoiceById(orderId, name, email);

        // Then
        assertNotNull(result);
        assertTrue(result.contentLength() > 0);
        verify(purchaseOrderService).getPurchaseOrderById(orderId);
    }

    @Test
    void testGenerateInvoiceById_OrderNotFound() {
        // Given
        String orderId = "NONEXISTENT_ORDER_ID";
        String name = "Jane Smith";
        String email = "<EMAIL>";

        when(purchaseOrderService.getPurchaseOrderById(orderId))
                .thenReturn(null);

        // When
        ByteArrayResource result = invoiceService.generateInvoiceById(orderId, name, email);

        // Then
        assertNull(result);
        verify(purchaseOrderService).getPurchaseOrderById(orderId);
    }

    @Test
    void testGenerateInvoice_WithComplexValues() {
        // Given
        String name = "Test Customer";
        String email = "<EMAIL>";
        String externalId = "ORDER_WITH_ITEMS";

        // Create order with complex values
        KarmakPurchaseOrder orderWithItems = createOrderWithComplexValues();
        when(purchaseOrderService.getPurchaseOrderByExternalId(externalId))
                .thenReturn(orderWithItems);

        // When
        ByteArrayResource result = invoiceService.generateInvoice(name, email, externalId);

        // Then
        assertNotNull(result);
        assertTrue(result.contentLength() > 0);
    }

    @Test
    void testGenerateInvoice_ServiceException() {
        // Given
        String name = "John Doe";
        String email = "<EMAIL>";
        String externalId = "ORDER_12345";

        when(purchaseOrderService.getPurchaseOrderByExternalId(externalId))
                .thenThrow(new RuntimeException("Database error"));

        // When & Then
        assertThrows(RuntimeException.class, () -> {
            invoiceService.generateInvoice(name, email, externalId);
        });
    }

    // Helper methods
    private KarmakPurchaseOrder createSampleKarmakPurchaseOrder() {
        KarmakPurchaseOrder order = new KarmakPurchaseOrder();
        order.setId("ORDER_123");
        order.setExternalId("EXT_ORDER_456");

        // Create sample Values with grand total
        List<com.atp.product.karmak_responses.Value> values = Arrays.asList(
            createValue("ECM_GrandTotal", 150.75),
            createValue("ECM_OrderDate", "07/10/2025")
        );
        order.setValues(values);

        return order;
    }

    private KarmakPurchaseOrder createOrderWithComplexValues() {
        KarmakPurchaseOrder order = createSampleKarmakPurchaseOrder();

        // Create ComplexValues with purchase order items
        ComplexValues complexValues = new ComplexValues();
        PurchaseOrder purchaseOrder = new PurchaseOrder();
        purchaseOrder.setType("PurchaseOrders");
        purchaseOrder.setName("Purchase Orders");

        Values poValues = new Values();
        poValues.setOrderPartNumber(createOrderPartNumber("OrderPartNumber", "PART_001"));
        poValues.setOrderProductDetails(createOrderProductDetails("OrderProductDetails", "Test Product"));
        poValues.setOrderQuantity(createOrderQuantity("OrderQuantity", 2));
        poValues.setOrderCost(createOrderCost("OrderCost", 75.50));

        purchaseOrder.setValues(poValues);
        complexValues.setPurchaseOrders(Arrays.asList(purchaseOrder));
        order.setComplexValues(complexValues);

        return order;
    }

    private com.atp.product.karmak_responses.Value createValue(String attributeId, Object value) {
        com.atp.product.karmak_responses.Value val = new com.atp.product.karmak_responses.Value();
        val.setAttributeId(attributeId);
        Content content = new Content();
        content.setValue(value);
        val.setContent(Arrays.asList(content));
        return val;
    }

    private OrderPartNumber createOrderPartNumber(String attributeId, String value) {
        OrderPartNumber attr = new OrderPartNumber();
        attr.setAttributeId(attributeId);
        Content content = new Content();
        content.setValue(value);
        attr.setContent(Arrays.asList(content));
        return attr;
    }

    private OrderProductDetails createOrderProductDetails(String attributeId, String value) {
        OrderProductDetails attr = new OrderProductDetails();
        attr.setAttributeId(attributeId);
        Content content = new Content();
        content.setValue(value);
        attr.setContent(Arrays.asList(content));
        return attr;
    }

    private OrderQuantity createOrderQuantity(String attributeId, int value) {
        OrderQuantity attr = new OrderQuantity();
        attr.setAttributeId(attributeId);
        Content content = new Content();
        content.setValue(value);
        attr.setContent(Arrays.asList(content));
        return attr;
    }

    private OrderCost createOrderCost(String attributeId, double value) {
        OrderCost attr = new OrderCost();
        attr.setAttributeId(attributeId);
        Content content = new Content();
        content.setValue(value);
        attr.setContent(Arrays.asList(content));
        return attr;
    }
}
