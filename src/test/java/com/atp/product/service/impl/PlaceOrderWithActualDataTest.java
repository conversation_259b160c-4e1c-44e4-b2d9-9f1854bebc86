package com.atp.product.service.impl;

import com.atp.product.karmak_responses.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to verify placeOrder method uses exactly the data provided in originalOrder
 */
class PlaceOrderWithActualDataTest {

    private ScheduledOrderServiceImpl scheduledOrderService;
    private ObjectMapper objectMapper;
    private KarmakPurchaseOrder actualDataOrder;

    @BeforeEach
    void setUp() {
        scheduledOrderService = new ScheduledOrderServiceImpl(null, null, null, null);
        objectMapper = new ObjectMapper();
        
        // Set the objectMapper field
        ReflectionTestUtils.setField(scheduledOrderService, "objectMapper", objectMapper);
        
        // Create order with your actual data structure
        actualDataOrder = createOrderWithActualData();
    }

    @Test
    void testBuildOrderPayload_UsesExactDataFromOriginalOrder() throws Exception {
        // When
        Object payload = ReflectionTestUtils.invokeMethod(scheduledOrderService, "buildOrderPayload", actualDataOrder);
        
        // Convert to JsonNode for easy verification
        JsonNode payloadNode = objectMapper.valueToTree(payload);
        
        // Then - verify exact values from your data are used
        assertEquals("CSTM_1185", payloadNode.get("parentId").asText());
        assertEquals("DE", payloadNode.get("entityType").asText());
        assertEquals("Maintenance", payloadNode.get("workspaceId").asText());
        assertEquals("PurchaseOrder", payloadNode.get("datatypeDefinitionId").asText());
        assertEquals(0, payloadNode.get("completeness").asInt());
        assertEquals(false, payloadNode.get("deleted").asBoolean());
        assertEquals(false, payloadNode.get("systemEntity").asBoolean());
        assertEquals("NOT_APPROVED", payloadNode.get("approvalStatus").asText());
        assertEquals("", payloadNode.get("externalId").asText());
        assertEquals("", payloadNode.get("name").asText());
        
        // Verify Values array uses exact data
        JsonNode valuesArray = payloadNode.get("Values");
        assertNotNull(valuesArray);
        assertTrue(valuesArray.isArray());
        assertEquals(5, valuesArray.size()); // Should have 5 values from your data
        
        // Verify specific values from your data
        boolean foundOrderDate = false;
        boolean foundGrandTotal = false;
        boolean foundBinLocation = false;
        
        for (JsonNode value : valuesArray) {
            String attributeId = value.get("attributeId").asText();
            switch (attributeId) {
                case "ECM_OrderDate":
                    foundOrderDate = true;
                    assertEquals("07/10/2025", value.get("content").get(0).get("value").asText());
                    break;
                case "ECM_GrandTotal":
                    foundGrandTotal = true;
                    assertEquals(24.69, value.get("content").get(0).get("value").asDouble(), 0.01);
                    break;
                case "BinLocation":
                    foundBinLocation = true;
                    assertEquals("Rockdale", value.get("content").get(0).get("value").asText());
                    break;
            }
        }
        
        assertTrue(foundOrderDate, "Should find ECM_OrderDate with exact value");
        assertTrue(foundGrandTotal, "Should find ECM_GrandTotal with exact value");
        assertTrue(foundBinLocation, "Should find BinLocation with exact value 'Rockdale'");
        
        // Verify path array uses exact data
        JsonNode pathArray = payloadNode.get("path");
        assertNotNull(pathArray);
        assertTrue(pathArray.isArray());
        assertEquals(4, pathArray.size()); // Should have 4 elements from your data
        assertEquals("MainEntityRoot", pathArray.get(0).asText());
        assertEquals("CustomersHierarchy", pathArray.get(1).asText());
        assertEquals("IDX_M", pathArray.get(2).asText());
        assertEquals("CSTM_1185", pathArray.get(3).asText());
        
        // Verify ComplexValues uses exact data
        JsonNode complexValues = payloadNode.get("ComplexValues");
        assertNotNull(complexValues);
        JsonNode purchaseOrders = complexValues.get("PurchaseOrders");
        assertNotNull(purchaseOrders);
        assertTrue(purchaseOrders.isArray());
        assertEquals(1, purchaseOrders.size());
        
        JsonNode firstPO = purchaseOrders.get(0);
        assertEquals("PurchaseOrders", firstPO.get("type").asText());
        assertEquals("Purchase Orders", firstPO.get("name").asText());
        
        // Verify specific values in ComplexValues
        JsonNode poValues = firstPO.get("Values");
        assertNotNull(poValues);
        
        // Check OrderPartNumber
        JsonNode partNumber = poValues.get("OrderPartNumber");
        assertNotNull(partNumber);
        assertEquals("53252", partNumber.get("content").get(0).get("value").asText());
        
        // Check OrderPartNumberSlug
        JsonNode partNumberSlug = poValues.get("OrderPartNumberSlug");
        assertNotNull(partNumberSlug);
        assertEquals("53252", partNumberSlug.get("content").get(0).get("value").asText());
        
        // Check OrderCost
        JsonNode orderCost = poValues.get("OrderCost");
        assertNotNull(orderCost);
        assertEquals(24.69, orderCost.get("content").get(0).get("value").asDouble(), 0.01);
        
        // Check OrderSupplierName
        JsonNode supplierName = poValues.get("OrderSupplierName");
        assertNotNull(supplierName);
        assertEquals("Grote Industries", supplierName.get("content").get(0).get("value").asText());
    }

    private KarmakPurchaseOrder createOrderWithActualData() {
        KarmakPurchaseOrder order = new KarmakPurchaseOrder();
        
        // Set basic fields exactly as in your data
        order.setParentId("CSTM_1185");
        order.setEntityType("DE");
        order.setWorkspaceId("Maintenance");
        order.setDatatypeDefinitionId("PurchaseOrder");
        order.setCompleteness(0L);
        order.setDeleted(false);
        order.setSystemEntity(false);
        order.setApprovalStatus("NOT_APPROVED");
        order.setExternalId("");
        order.setName("");
        
        // Set path exactly as in your data
        order.setPath(Arrays.asList("MainEntityRoot", "CustomersHierarchy", "IDX_M", "CSTM_1185"));
        
        // Create Values array exactly as in your data
        List<Value> values = Arrays.asList(
            createValue("ECM_OrderDate", "07/10/2025"),
            createValue("OrderStatus", "Order Placed/New Order"),
            createValue("DeliveryMethod", "Store Pick Up"),
            createValueWithNumber("ECM_GrandTotal", 24.69),
            createValue("BinLocation", "Rockdale")
        );
        order.setValues(values);
        
        // Create ComplexValues exactly as in your data
        ComplexValues complexValues = new ComplexValues();
        PurchaseOrder purchaseOrder = new PurchaseOrder();
        purchaseOrder.setType("PurchaseOrders");
        purchaseOrder.setName("Purchase Orders");
        
        Values poValues = new Values();
        poValues.setOrderPartNumber(createOrderAttribute("OrderPartNumber", "53252"));
        poValues.setOrderPartNumberSlug(createOrderAttribute("OrderPartNumberSlug", "53252"));
        poValues.setOrderCost(createOrderAttributeWithNumber("OrderCost", 24.69));
        poValues.setOrderQuantity(createOrderAttributeWithNumber("OrderQuantity", 1));
        poValues.setOrderPrimaryURL(createOrderAttribute("OrderPrimaryURL", "https://atommxdm-prod.s3.amazonaws.com/..."));
        poValues.setOrderProductDetails(createOrderAttribute("OrderProductDetails", "STT LMP,4\"RED,SNOVALED,10DIODE GRMT MNT"));
        poValues.setOrderSupplierName(createOrderAttribute("OrderSupplierName", "Grote Industries"));
        
        purchaseOrder.setValues(poValues);
        complexValues.setPurchaseOrders(Arrays.asList(purchaseOrder));
        order.setComplexValues(complexValues);
        
        return order;
    }
    
    private Value createValue(String attributeId, String value) {
        Value val = new Value();
        val.setAttributeId(attributeId);
        Content content = new Content();
        content.setValue(value);
        val.setContent(Arrays.asList(content));
        return val;
    }
    
    private Value createValueWithNumber(String attributeId, double value) {
        Value val = new Value();
        val.setAttributeId(attributeId);
        Content content = new Content();
        content.setValue(value);
        val.setContent(Arrays.asList(content));
        return val;
    }
    
    private OrderPartNumber createOrderAttribute(String attributeId, String value) {
        OrderPartNumber attr = new OrderPartNumber();
        attr.setAttributeId(attributeId);
        Content content = new Content();
        content.setValue(value);
        attr.setContent(Arrays.asList(content));
        return attr;
    }
    
    private OrderCost createOrderAttributeWithNumber(String attributeId, double value) {
        OrderCost attr = new OrderCost();
        attr.setAttributeId(attributeId);
        Content content = new Content();
        content.setValue(value);
        attr.setContent(Arrays.asList(content));
        return attr;
    }
}
