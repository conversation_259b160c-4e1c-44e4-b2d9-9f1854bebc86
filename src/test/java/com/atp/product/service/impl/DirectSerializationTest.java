package com.atp.product.service.impl;

import com.atp.product.karmak_responses.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test to verify direct serialization of KarmakPurchaseOrder works correctly
 */
class DirectSerializationTest {

    private ObjectMapper objectMapper;
    private KarmakPurchaseOrder actualDataOrder;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        actualDataOrder = createOrderWithActualData();
    }

    @Test
    void testDirectSerialization_ProducesCorrectJSON() throws Exception {
        // When - Direct serialization like in placeOrder method
        String jsonPayload = objectMapper.writeValueAsString(actualDataOrder);
        
        // Parse back to verify structure
        JsonNode jsonNode = objectMapper.readTree(jsonPayload);
        
        // Then - verify all your exact data is preserved
        assertEquals("CSTM_1185", jsonNode.get("parentId").asText());
        assertEquals("DE", jsonNode.get("entityType").asText());
        assertEquals("Maintenance", jsonNode.get("workspaceId").asText());
        assertEquals("PurchaseOrder", jsonNode.get("datatypeDefinitionId").asText());
        
        // Verify Values array
        JsonNode valuesArray = jsonNode.get("values");
        assertNotNull(valuesArray);
        assertTrue(valuesArray.isArray());
        assertEquals(5, valuesArray.size());
        
        // Verify specific values
        boolean foundOrderDate = false;
        boolean foundBinLocation = false;
        boolean foundGrandTotal = false;
        
        for (JsonNode value : valuesArray) {
            String attributeId = value.get("attributeId").asText();
            switch (attributeId) {
                case "ECM_OrderDate":
                    foundOrderDate = true;
                    assertEquals("07/10/2025", value.get("content").get(0).get("value").asText());
                    break;
                case "BinLocation":
                    foundBinLocation = true;
                    assertEquals("Rockdale", value.get("content").get(0).get("value").asText());
                    break;
                case "ECM_GrandTotal":
                    foundGrandTotal = true;
                    assertEquals(24.69, value.get("content").get(0).get("value").asDouble(), 0.01);
                    break;
            }
        }
        
        assertTrue(foundOrderDate, "Should preserve ECM_OrderDate");
        assertTrue(foundBinLocation, "Should preserve BinLocation");
        assertTrue(foundGrandTotal, "Should preserve ECM_GrandTotal");
        
        // Verify path array
        JsonNode pathArray = jsonNode.get("path");
        assertNotNull(pathArray);
        assertEquals(4, pathArray.size());
        assertEquals("MainEntityRoot", pathArray.get(0).asText());
        assertEquals("CustomersHierarchy", pathArray.get(1).asText());
        assertEquals("IDX_M", pathArray.get(2).asText());
        assertEquals("CSTM_1185", pathArray.get(3).asText());
        
        // Verify ComplexValues
        JsonNode complexValues = jsonNode.get("complexValues");
        assertNotNull(complexValues);
        JsonNode purchaseOrders = complexValues.get("purchaseOrders");
        assertNotNull(purchaseOrders);
        assertEquals(1, purchaseOrders.size());
        
        JsonNode firstPO = purchaseOrders.get(0);
        assertEquals("PurchaseOrders", firstPO.get("type").asText());
        assertEquals("Purchase Orders", firstPO.get("name").asText());
        
        // Verify nested values
        JsonNode poValues = firstPO.get("values");
        assertNotNull(poValues);
        
        JsonNode partNumber = poValues.get("orderPartNumber");
        assertNotNull(partNumber);
        assertEquals("53252", partNumber.get("content").get(0).get("value").asText());
        
        JsonNode supplierName = poValues.get("orderSupplierName");
        assertNotNull(supplierName);
        assertEquals("Grote Industries", supplierName.get("content").get(0).get("value").asText());
        
        // Print the JSON for manual verification
        System.out.println("Generated JSON payload:");
        System.out.println(objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(actualDataOrder));
    }

    @Test
    void testDirectSerialization_PreservesAllFields() throws Exception {
        // When
        String jsonPayload = objectMapper.writeValueAsString(actualDataOrder);
        JsonNode jsonNode = objectMapper.readTree(jsonPayload);
        
        // Then - verify all fields are present
        assertTrue(jsonNode.has("parentId"));
        assertTrue(jsonNode.has("entityType"));
        assertTrue(jsonNode.has("workspaceId"));
        assertTrue(jsonNode.has("datatypeDefinitionId"));
        assertTrue(jsonNode.has("completeness"));
        assertTrue(jsonNode.has("deleted"));
        assertTrue(jsonNode.has("systemEntity"));
        assertTrue(jsonNode.has("approvalStatus"));
        assertTrue(jsonNode.has("externalId"));
        assertTrue(jsonNode.has("name"));
        assertTrue(jsonNode.has("values"));
        assertTrue(jsonNode.has("path"));
        assertTrue(jsonNode.has("complexValues"));
    }

    private KarmakPurchaseOrder createOrderWithActualData() {
        KarmakPurchaseOrder order = new KarmakPurchaseOrder();
        
        // Set basic fields exactly as in your data
        order.setParentId("CSTM_1185");
        order.setEntityType("DE");
        order.setWorkspaceId("Maintenance");
        order.setDatatypeDefinitionId("PurchaseOrder");
        order.setCompleteness(0L);
        order.setDeleted(false);
        order.setSystemEntity(false);
        order.setApprovalStatus("NOT_APPROVED");
        order.setExternalId("");
        order.setName("");
        
        // Set path exactly as in your data
        order.setPath(Arrays.asList("MainEntityRoot", "CustomersHierarchy", "IDX_M", "CSTM_1185"));
        
        // Create Values array exactly as in your data
        List<Value> values = Arrays.asList(
            createValue("ECM_OrderDate", "07/10/2025"),
            createValue("OrderStatus", "Order Placed/New Order"),
            createValue("DeliveryMethod", "Store Pick Up"),
            createValueWithNumber("ECM_GrandTotal", 24.69),
            createValue("BinLocation", "Rockdale")
        );
        order.setValues(values);
        
        // Create ComplexValues exactly as in your data
        ComplexValues complexValues = new ComplexValues();
        PurchaseOrder purchaseOrder = new PurchaseOrder();
        purchaseOrder.setType("PurchaseOrders");
        purchaseOrder.setName("Purchase Orders");
        
        Values poValues = new Values();
        poValues.setOrderPartNumber(createOrderPartNumber("OrderPartNumber", "53252"));
        poValues.setOrderPartNumberSlug(createOrderPartNumberSlug("OrderPartNumberSlug", "53252"));
        poValues.setOrderCost(createOrderCost("OrderCost", 24.69));
        poValues.setOrderQuantity(createOrderQuantity("OrderQuantity", 1));
        poValues.setOrderPrimaryURL(createOrderPrimaryURL("OrderPrimaryURL", "https://example.com/image.jpg"));
        poValues.setOrderProductDetails(createOrderProductDetails("OrderProductDetails", "STT LMP,4\"RED,SNOVALED,10DIODE GRMT MNT"));
        poValues.setOrderSupplierName(createOrderSupplierName("OrderSupplierName", "Grote Industries"));
        
        purchaseOrder.setValues(poValues);
        complexValues.setPurchaseOrders(Arrays.asList(purchaseOrder));
        order.setComplexValues(complexValues);
        
        return order;
    }
    
    private Value createValue(String attributeId, String value) {
        Value val = new Value();
        val.setAttributeId(attributeId);
        Content content = new Content();
        content.setValue(value);
        val.setContent(Arrays.asList(content));
        return val;
    }
    
    private Value createValueWithNumber(String attributeId, double value) {
        Value val = new Value();
        val.setAttributeId(attributeId);
        Content content = new Content();
        content.setValue(value);
        val.setContent(Arrays.asList(content));
        return val;
    }
    
    private OrderPartNumber createOrderPartNumber(String attributeId, String value) {
        OrderPartNumber attr = new OrderPartNumber();
        attr.setAttributeId(attributeId);
        Content content = new Content();
        content.setValue(value);
        attr.setContent(Arrays.asList(content));
        return attr;
    }
    
    private OrderPartNumberSlug createOrderPartNumberSlug(String attributeId, String value) {
        OrderPartNumberSlug attr = new OrderPartNumberSlug();
        attr.setAttributeId(attributeId);
        Content content = new Content();
        content.setValue(value);
        attr.setContent(Arrays.asList(content));
        return attr;
    }
    
    private OrderCost createOrderCost(String attributeId, double value) {
        OrderCost attr = new OrderCost();
        attr.setAttributeId(attributeId);
        Content content = new Content();
        content.setValue(value);
        attr.setContent(Arrays.asList(content));
        return attr;
    }
    
    private OrderQuantity createOrderQuantity(String attributeId, int value) {
        OrderQuantity attr = new OrderQuantity();
        attr.setAttributeId(attributeId);
        Content content = new Content();
        content.setValue(value);
        attr.setContent(Arrays.asList(content));
        return attr;
    }
    
    private OrderPrimaryURL createOrderPrimaryURL(String attributeId, String value) {
        OrderPrimaryURL attr = new OrderPrimaryURL();
        attr.setAttributeId(attributeId);
        Content content = new Content();
        content.setValue(value);
        attr.setContent(Arrays.asList(content));
        return attr;
    }
    
    private OrderProductDetails createOrderProductDetails(String attributeId, String value) {
        OrderProductDetails attr = new OrderProductDetails();
        attr.setAttributeId(attributeId);
        Content content = new Content();
        content.setValue(value);
        attr.setContent(Arrays.asList(content));
        return attr;
    }
    
    private OrderSupplierName createOrderSupplierName(String attributeId, String value) {
        OrderSupplierName attr = new OrderSupplierName();
        attr.setAttributeId(attributeId);
        Content content = new Content();
        content.setValue(value);
        attr.setContent(Arrays.asList(content));
        return attr;
    }
}
