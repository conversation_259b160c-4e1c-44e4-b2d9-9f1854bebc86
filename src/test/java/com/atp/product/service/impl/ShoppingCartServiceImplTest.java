package com.atp.product.service.impl;

import com.atp.product.controller.dto.request.ShoppingCartRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

@ExtendWith(MockitoExtension.class)
class ShoppingCartServiceTest {

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private CommonServiceImpl commonServiceImpl;

    @InjectMocks
    private ShoppingCartServiceImpl shoppingCartService;

    private static final String CUSTOMER_CORRELATION_ID = "CSTM_1074";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        shoppingCartRequest = new ShoppingCartRequest();
        shoppingCartRequest.setCustomerCorrelationId("12345");
        shoppingCartRequest.setPartNumber("ABC123");
        shoppingCartRequest.setProductQuantity(2);
    }
    private ShoppingCartRequest shoppingCartRequest;

   /* @Test
    void testGetShoppingCartList_Success() {
        // Arrange: Mock the shopping cart object returned by findOne
        ShoppingCart mockCart = new ShoppingCart();
        mockCart.setCustomerCorrelationId(CUSTOMER_CORRELATION_ID);
        mockCart.setProductDetails(new ArrayList<>());

        when(mongoTemplate.findOne(any(Query.class), eq(ShoppingCart.class)))
                .thenReturn(mockCart);

        // Arrange: Mock the aggregation result
        ShoppingCartListResponse mockResponse = new ShoppingCartListResponse();
        mockResponse.setCustomerCorrelationId(CUSTOMER_CORRELATION_ID);
        mockResponse.setProductDetails(new ArrayList<>());
        mockResponse.setTotalProductCount(1);

        AggregationResults<ShoppingCartListResponse> mockAggregationResults = mock(AggregationResults.class);
        when(mockAggregationResults.getUniqueMappedResult()).thenReturn(mockResponse);
        when(mongoTemplate.aggregate(any(Aggregation.class), eq(Constants.SHOPPING_CART_COLLECTION_NAME), eq(ShoppingCartListResponse.class)))
                .thenReturn(mockAggregationResults);

        // Arrange: Mock the image resizing service
        when(commonServiceImpl.resizeImages(anyList())).thenReturn(new ArrayList<>());

        // Act: Call the method
        ShoppingCartListResponse result = shoppingCartService.getShoppingCartList(CUSTOMER_CORRELATION_ID);

        // Assert: Verify the results
        assertNotNull(result);
        assertEquals(CUSTOMER_CORRELATION_ID, result.getCustomerCorrelationId());
        verify(mongoTemplate, times(1)).findOne(any(Query.class), eq(ShoppingCart.class));
        verify(mongoTemplate, times(1)).aggregate(any(Aggregation.class), eq(Constants.SHOPPING_CART_COLLECTION_NAME), eq(ShoppingCartListResponse.class));
        verify(commonServiceImpl, times(1)).resizeImages(anyList());
    }*/

    /*@Test
    void testGetShoppingCartList_NotFound() {
        // Mock no shopping cart found
        when(mongoTemplate.findOne(any(Query.class), eq(ShoppingCart.class)))
                .thenReturn(null);

        // Call the method
        ShoppingCartListResponse result = shoppingCartService.getShoppingCartList(CUSTOMER_CORRELATION_ID);

        // Verify the results
        assertNotNull(result);
        assertEquals(CUSTOMER_CORRELATION_ID, result.getCustomerCorrelationId());
        assertEquals(0, result.getTotalProductCount());
        assertEquals(0.0, result.getGrandTotal());
        assertTrue(result.getProductDetails().isEmpty());
    }*/

   /* @Test
    void testGetShoppingCartList_Exception() {
        // Mock an exception thrown by mongoTemplate
        when(mongoTemplate.findOne(any(Query.class), eq(ShoppingCart.class)))
                .thenThrow(new RuntimeException("Database error"));

        // Expect a DomainException
        Exception exception = assertThrows(DomainException.class, () ->
                shoppingCartService.getShoppingCartList(CUSTOMER_CORRELATION_ID));

        // Verify the exception message
        assertTrue(exception.getMessage().contains("An error occurred while retrieving shopping cart list"));
    }*/

   /* @Test
    public void testSaveShoppingCartDetails_CreateNewCart() {
        // Scenario where the customer correlation ID does not exist in the collection

        when(mongoTemplate.findOne(any(Query.class), eq(ShoppingCart.class))).thenReturn(null);
        when(mongoTemplate.findOne(any(Query.class), eq(Wishlist.class))).thenReturn(null);

        ShoppingCart cart = new ShoppingCart();
        cart.setCustomerCorrelationId(shoppingCartRequest.getCustomerCorrelationId());
        cart.setProductDetails(List.of(new ShoppingCart.ProductDetail("ABC123", 2)));
        cart.setTotalProductCount(1);

        // Simulate saving a new shopping cart
        doAnswer(invocation -> {
            ShoppingCart newCart = invocation.getArgument(0);
            assertEquals("12345", newCart.getCustomerCorrelationId());
            assertEquals(1, newCart.getTotalProductCount());
            assertEquals(1, newCart.getProductDetails().size());
            assertEquals("ABC123", newCart.getProductDetails().get(0).getPartNumber());
            return null;
        }).when(mongoTemplate).save(any(ShoppingCart.class));

        int totalProductCount = shoppingCartService.saveShoppingCartDetails(shoppingCartRequest);

        assertEquals(1, totalProductCount);
        verify(mongoTemplate, times(1)).save(any(ShoppingCart.class));
    }*/

}