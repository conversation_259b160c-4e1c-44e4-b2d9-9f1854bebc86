package com.atp.product.service.impl;

import com.atp.product.karmak_responses.KarmakPurchaseOrder;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Query;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * Comprehensive test class for optimized PurchaseOrderService
 */
@ExtendWith(MockitoExtension.class)
class PurchaseOrderServiceOptimizedTest {

    @Mock
    private MongoTemplate mongoTemplate;

    @InjectMocks
    private PurchaseOrderService purchaseOrderService;

    private Document sampleDocument;

    @BeforeEach
    void setUp() {
        sampleDocument = createSampleDocument();
    }

    @Test
    void testGetPurchaseOrderByExternalId_Success() {
        // Given
        String externalId = "TEST_ID_001";
        when(mongoTemplate.findOne(any(Query.class), eq(Document.class), eq("entity")))
                .thenReturn(sampleDocument);

        // When
        KarmakPurchaseOrder result = purchaseOrderService.getPurchaseOrderByExternalId(externalId);

        // Then
        assertNotNull(result);
        assertEquals("TEST_ID_001", result.getExternalId());
        assertEquals("Test Purchase Order", result.getName());
        assertEquals("WORKSPACE_001", result.getWorkspaceId());
    }

    @Test
    void testGetPurchaseOrderByExternalId_NotFound() {
        // Given
        String externalId = "NON_EXISTENT_ID";
        when(mongoTemplate.findOne(any(Query.class), eq(Document.class), eq("entity")))
                .thenReturn(null);

        // When
        KarmakPurchaseOrder result = purchaseOrderService.getPurchaseOrderByExternalId(externalId);

        // Then
        assertNull(result);
    }

    @Test
    void testGetPurchaseOrdersByExternalIds_BatchProcessing() {
        // Given
        List<String> externalIds = Arrays.asList("ID_001", "ID_002", "ID_003");
        List<Document> documents = Arrays.asList(
                createSampleDocument("ID_001"),
                createSampleDocument("ID_002"),
                createSampleDocument("ID_003")
        );
        
        when(mongoTemplate.find(any(Query.class), eq(Document.class), eq("entity")))
                .thenReturn(documents);

        // When
        List<KarmakPurchaseOrder> results = purchaseOrderService.getPurchaseOrdersByExternalIds(externalIds);

        // Then
        assertNotNull(results);
        assertEquals(3, results.size());
        assertEquals("ID_001", results.get(0).getExternalId());
        assertEquals("ID_002", results.get(1).getExternalId());
        assertEquals("ID_003", results.get(2).getExternalId());
    }

    @Test
    void testGetPurchaseOrdersByExternalIds_EmptyList() {
        // Given
        List<String> emptyList = Arrays.asList();

        // When
        List<KarmakPurchaseOrder> results = purchaseOrderService.getPurchaseOrdersByExternalIds(emptyList);

        // Then
        assertNotNull(results);
        assertTrue(results.isEmpty());
    }

    @Test
    void testPerformanceWithLargeDataset() {
        // Given
        List<String> largeIdList = generateLargeIdList(1000);
        List<Document> largeDocumentList = largeIdList.stream()
                .map(this::createSampleDocument)
                .toList();
        
        when(mongoTemplate.find(any(Query.class), eq(Document.class), eq("entity")))
                .thenReturn(largeDocumentList);

        // When
        long startTime = System.currentTimeMillis();
        List<KarmakPurchaseOrder> results = purchaseOrderService.getPurchaseOrdersByExternalIds(largeIdList);
        long executionTime = System.currentTimeMillis() - startTime;

        // Then
        assertNotNull(results);
        assertEquals(1000, results.size());
        
        // Performance assertion - should complete within reasonable time
        assertTrue(executionTime < 5000, "Batch processing should complete within 5 seconds");
        System.out.println("Processed 1000 records in " + executionTime + " ms");
    }

    private Document createSampleDocument() {
        return createSampleDocument("TEST_ID_001");
    }

    private Document createSampleDocument(String externalId) {
        Document doc = new Document();
        doc.put("externalId", externalId);
        doc.put("parentId", "PARENT_001");
        doc.put("entityType", "PurchaseOrder");
        doc.put("workspaceId", "WORKSPACE_001");
        doc.put("datatypeDefinitionId", "DATATYPE_001");
        doc.put("name", "Test Purchase Order");
        doc.put("completeness", 100L);
        doc.put("isSystemEntity", false);
        doc.put("ancestors", Arrays.asList("root", "parent"));

        // Add complex values
        Document complexValues = new Document();
        Document purchaseOrder = new Document();
        purchaseOrder.put("type", "ORDER");
        purchaseOrder.put("name", "Sample Order");
        
        Document values = new Document();
        Document orderCost = new Document();
        Document english = new Document();
        Document content = new Document();
        content.put("text", "100.50");
        english.put("content", Arrays.asList(content));
        orderCost.put("English", english);
        values.put("OrderCost", orderCost);
        purchaseOrder.put("values", values);
        
        complexValues.put("PurchaseOrders", Arrays.asList(purchaseOrder));
        doc.put("complexValues", complexValues);

        // Add flattened values
        Document flatValues = new Document();
        Document grandTotal = new Document();
        Document grandTotalEnglish = new Document();
        Document grandTotalContent = new Document();
        grandTotalContent.put("text", new Document("$numberDecimal", "250.75"));
        grandTotalEnglish.put("content", Arrays.asList(grandTotalContent));
        grandTotal.put("English", grandTotalEnglish);
        flatValues.put("ECM_GrandTotal", grandTotal);
        doc.put("values", flatValues);

        return doc;
    }

    private List<String> generateLargeIdList(int size) {
        return java.util.stream.IntStream.range(0, size)
                .mapToObj(i -> "ID_" + String.format("%04d", i))
                .toList();
    }
}
