package com.atp.product.service.impl;

import static org.mockito.ArgumentMatchers.eq;

import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;

@ExtendWith(MockitoExtension.class)
class ProductServiceImplTest {

    @Mock
    private MongoTemplate mongoTemplate;

    @InjectMocks
    private ProductServiceImpl productService;

    @InjectMocks
    private CommonServiceImpl commonServiceImpl;

    /*@Test
    void testGetFeaturedProductsSuccess() throws IOException {
        // Arrange
        String json = commonServiceImpl.readFile("src/test/resources/ResponseJson/GetFeaturedProducts.json");
        ObjectMapper objectMapper = new ObjectMapper();
        // Expected Result
        List<ProductResponseHomePage> expectedProducts = objectMapper.readValue(json, new TypeReference<List<ProductResponseHomePage>>() {
        });
        //AggregationResult
        Document rawResults = new Document();  // Create an empty Document
        AggregationResults<ProductResponseHomePage> aggregationResults =
                new AggregationResults<>(expectedProducts, rawResults);
        when(mongoTemplate.aggregate(any(Aggregation.class), any(String.class), any(Class.class)))
                .thenReturn(aggregationResults);
        // Actual results
        List<ProductResponseHomePage> actualProducts = productService.getFeaturedProducts();
        // Assert
        assertEquals(expectedProducts, actualProducts);
    }
    @Test
    void testGetFeaturedProductsException() {
        when(mongoTemplate.aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(ProductResponseHomePage.class)))
                .thenThrow(new RuntimeException("Aggregation error"));

        assertThrows(DomainException.class, () -> {
            productService.getFeaturedProducts();
        });

        verify(mongoTemplate, times(1))
                .aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(ProductResponseHomePage.class));
    }

    @Test
    void testGetNewProductsSuccess() throws IOException {
        // Arrange
        String json = commonServiceImpl.readFile("src/test/resources/ResponseJson/GetNewProducts.json");
        ObjectMapper objectMapper = new ObjectMapper();
        //Expected result
        List<ProductResponseHomePage> expectedProducts = objectMapper.readValue(json, new TypeReference<List<ProductResponseHomePage>>() {
        });
        //AggregationResult
        Document rawResults = new Document();  // Create an empty Document
        AggregationResults<ProductResponseHomePage> aggregationResults =
                new AggregationResults<>(expectedProducts, rawResults);
        when(mongoTemplate.aggregate(any(Aggregation.class), any(String.class), any(Class.class)))
                .thenReturn(aggregationResults);
        List<ProductResponseHomePage> actualProducts = productService.getNewProducts();
        // Assert
        assertEquals(expectedProducts, actualProducts);
    }
    @Test
    void testGetNewProductsException() {
        when(mongoTemplate.aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(ProductResponseHomePage.class)))
                .thenThrow(new RuntimeException("Aggregation error"));

        assertThrows(DomainException.class, () -> {
            productService.getNewProducts();
        });

        verify(mongoTemplate, times(1))
                .aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(ProductResponseHomePage.class));
    }

    @Test
    void testGetProductsDetailsByProductId_Success() throws IOException {
        // Arrange
        String requestJson = commonServiceImpl.readFile("src/test/resources/RequestJson/GetProductsDetailsByProductId.json");
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, String> properties = objectMapper.readValue(requestJson, Map.class);
        String responseJson = commonServiceImpl.readFile("src/test/resources/ResponseJson/GetProductsDetailsByProductId.json");
        // Expect results
        ProductDetailsResponse expectedResults = objectMapper.readValue(responseJson, ProductDetailsResponse.class);
        //AggregationResult
        Document rawResults = new Document();
        AggregationResults<ProductDetailsResponse> aggregationResults = new AggregationResults<>(List.of(expectedResults), rawResults);
        when(mongoTemplate.aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(ProductDetailsResponse.class)))
                .thenReturn(aggregationResults);
        // Actual results
        ProductDetailsResponse actualResults = productService.getProductDetailsByPartNumberSlug(properties.get("partNumberSlug").toString());
        // Assert
        assertNotNull(actualResults);
        assertEquals(expectedResults, actualResults);
        verify(mongoTemplate, times(1)).aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(ProductDetailsResponse.class));
    }

    @Test
    void testGetProductsDetailsByProductId_Exception() throws IOException {
        //Arrange
        String requestJson = commonServiceImpl.readFile("src/test/resources/RequestJson/GetProductsDetailsByProductId.json");
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, String> properties = objectMapper.readValue(requestJson, Map.class);
        //AggregateResult
        when(mongoTemplate.aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(ProductDetailsResponse.class)))
                .thenThrow(new RuntimeException("Aggregation error"));
        //Assert
        assertThrows(Exception.class, () -> {
            productService.getProductDetailsByPartNumberSlug(properties.get("partNumberSlug").toString());
        });

        verify(mongoTemplate, times(1))
                .aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(ProductDetailsResponse.class));
    }
    @Test
    void testGetSimilarProductListByBrandId() throws IOException {
        // Arrange
        String requestJson = commonServiceImpl.readFile("src/test/resources/RequestJson/GetSimilarProductListByBrandId.json");
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, String> properties = objectMapper.readValue(requestJson, Map.class);
        String responseJson = commonServiceImpl.readFile("src/test/resources/ResponseJson/GetSimilarProductListByBrandId.json");
        //Expected result
        List<ProductResponseHomePage> expectedResults = objectMapper.readValue(responseJson, new TypeReference<List<ProductResponseHomePage>>() {
        });
        AggregationResults<ProductResponseHomePage> aggregationResults = mock(AggregationResults.class);
        when(aggregationResults.getMappedResults()).thenReturn(expectedResults);
        //Aggregation
        when(mongoTemplate.aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(ProductResponseHomePage.class)))
                .thenReturn(aggregationResults);

        // Actual results
        List<ProductResponseHomePage> actualResults = productService.getSimilarProductListByBrandCategorySlug(properties.get("brandSlug").toString(),properties.get("categorySlug").toString());

        // Asserts
        assertNotNull(actualResults);
        assertEquals(expectedResults.size(), actualResults.size());
        verify(mongoTemplate, times(1)).aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(ProductResponseHomePage.class));
    }

    @Test
    void testGetSimilarProductListByBrandIdThrowsException() throws IOException {
        //Arrange
        String requestJson = commonServiceImpl.readFile("src/test/resources/RequestJson/GetSimilarProductListByBrandId.json");
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, String> properties = objectMapper.readValue(requestJson, Map.class);
        //AggregateResult
        when(mongoTemplate.aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(ProductResponseHomePage.class)))
                .thenThrow(new RuntimeException("Aggregation error"));
        //Assert
        assertThrows(Exception.class, () -> {
            productService.getSimilarProductListByBrandCategorySlug(properties.get("brandSlug").toString(),properties.get("categorySlug").toString());
        });

        verify(mongoTemplate, times(1))
                .aggregate(any(Aggregation.class), eq(Constants.PRODUCT_COLLECTION_NAME), eq(ProductResponseHomePage.class));
    }
*/
}
