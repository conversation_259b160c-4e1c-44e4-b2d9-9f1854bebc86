spring:
  application:
    name: ATP-product-service
  data:
    mongodb:
#      uri: mongodb+srv://atommxdm:<EMAIL>/?retryWrites=true&w=majority
#      port: 27017
#      database: admindev
#      username: ssapp
#      password: "!ss@app*"
      uri: mongodb://localhost:27017
      port: 27017
      database: atommprod
      username:
      password:
server:
  port: 8081
  servlet:
    context-path: /product-service
aws:
  s3:
    bucket:
      name: atommxdm-prod
    region: us-east-1
  access:
    key: ********************
  secret:
    key: CRcqfITI2hUkKniy37iry6Um7wWUWMOYHHCegMpg
assets:
  root: ssAppArea/assets
logging:
  level:
    org.springframework: WARN
    org.springframework.data.mongodb: WARN
Karmak:
  partSearchURL: https://api.karmak.io/ops/partssalesorder/PartsSearch/Search
  customerURL: https://api.karmak.io/ops/helper/customers?CustomerKey=
  purchaseOrderURL: https://api.karmak.io/ops/partspurchaseorder/PartsPurchaseOrder/Create
  inventoryUpdateUrl: https://api.karmak.io/ops/parts/PartsInventory/UpdatePartQuantity
  AccountNumber: 27487
  purchaseOrderSubscriptionKey: 99d0d58ffce045c1be9df18d062e4936
Ocp-Apim-Subscription-Key: ad684660f3ed48c18cfce369bc1e3a98

