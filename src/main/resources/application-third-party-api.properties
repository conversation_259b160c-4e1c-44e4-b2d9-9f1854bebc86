## Third-Party API Configuration for Order Placement
## Configuration for the external API used to place orders
#
## API Endpoint Configuration
#third-party.api.url=http://localhost:8080/server/api/data-entities/create
#third-party.api.login.url=http://localhost:8080/server/rest/auth/login
#third-party.api.timeout=30
#
## Authentication Configuration (Dynamic Token Generation)
#third-party.api.username=IshaniPandya
#third-party.api.password=Indianic@123
#
## Environment-specific configurations
## Development environment
#spring.profiles.active=dev
#
## Production environment (uncomment for production)
## third-party.api.url=https://production-api.example.com/api/data-entities/create
## third-party.api.username=your-production-username
## third-party.api.password=your-production-password
## third-party.api.timeout=60
