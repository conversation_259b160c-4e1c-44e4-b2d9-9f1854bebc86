# Third-Party API Configuration for Order Placement
# Configuration for the external API used to place orders

# API Endpoint Configuration
third-party.api.url=http://localhost:8080/server/api/data-entities/create
third-party.api.timeout=30

# Authorization Configuration
# Replace with your actual authorization token
third-party.api.authorization=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJhdWQiOiJhZG1pbmRldiIsImlzcyI6ImFkbWluZGV2IiwidGVuYW50SWQiOiJBY3Rpb25UcnVja0RFViIsImV4cCI6MTczODA4OTI2MiwiaWF0IjoxNzM4MDQ2MDYyLCJ1c2VybmFtZSI6InR0ZWVsIn0.3oMEDG-ZvmSACbehqNLQ_UcxKj-8WCaLBXw6Tm4xJee_zDH3HuOelmzHc777Rd65GlK3kgN8xlKMr8PdGvZuJw

# Environment-specific configurations
# Development environment
spring.profiles.active=dev

# Production environment (uncomment for production)
# third-party.api.url=https://production-api.example.com/api/data-entities/create
# third-party.api.authorization=your-production-token-here
# third-party.api.timeout=60
