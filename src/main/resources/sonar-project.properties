# Unique project identifier in SonarQube
sonar.projectKey=Action-Truck-Parts

# Project name displayed in the SonarQube UI
sonar.projectName='Action Truck Parts'

# Project version
sonar.projectVersion=1.0

# Source encoding
sonar.sourceEncoding=UTF-8

# Path to the source code
sonar.sources=src/main/java

# Path to the test source code
sonar.tests=src/test/java

# Path to the compiled bytecode
sonar.java.binaries=target/classes

# Path to the test compiled bytecode
sonar.junit.reportPaths=target/test-classes

# Exclude specific files or directories
sonar.exclusions=**/generated/**,**/*.xml

# URL of the SonarQube server
sonar.host.url=http://localhost:9000

# Authentication token generated from the SonarQube UI
#sonar.login=sqp_9a9a3a7740f7a95fdb53c00f1775ae6ee17e9e3c
sonar.password=indianic
sonar.login=admin

#sonar.token=sqp_e0aa4a47800393de617a8a896dadce102cc76c46



