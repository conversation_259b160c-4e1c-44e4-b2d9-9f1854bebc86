package com.atp.product.config;// src/main/java/com/example/security/JwtAuthenticationFilter.java
import com.atp.product.service.impl.CommonServiceImpl;
import com.atp.product.utils.JwtUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import java.io.IOException;
import java.util.Map;

@Component
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final CommonServiceImpl commonService;

    public JwtAuthenticationFilter(CommonServiceImpl commonService) {
        this.commonService = commonService;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull FilterChain chain)
            throws ServletException, IOException {
        final String authorizationToken = request.getHeader("Authorization");
        final String userName = request.getHeader("Username");
        String tokenUserName;
        if (authorizationToken != null && !authorizationToken.isBlank() && !authorizationToken.isEmpty()
                && userName != null && !userName.isBlank() && !userName.isEmpty()) {
            tokenUserName = JwtUtil.extractUser(authorizationToken);
            if (tokenUserName != null) {
                if(userName.equals(tokenUserName)){
                    if(!JwtUtil.isTokenExpired(authorizationToken)) {
                        /*Map<String, String> customerIdByCustomerKey = commonService.getCustomerIdByCustomerKey();
                        String customerID = customerIdByCustomerKey.get("CustomerID");
                        // Set the customerId in the response header
                        response.addHeader("Karmakcustomerid", customerID);*/
                        chain.doFilter(request, response);
                    }
                    else {
                        response.addHeader("Access-Control-Allow-Origin", "*");
                        response.sendError(HttpServletResponse.SC_FORBIDDEN, "Unable to determine Authorized User.");
                        response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                        return;
                    }
                }
                else {
                    response.addHeader("Access-Control-Allow-Origin", "*");
                    response.sendError(HttpServletResponse.SC_FORBIDDEN, "Unable to determine Authorized User.");
                    response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                    return;
                }
            }
            else {
                response.addHeader("Access-Control-Allow-Origin", "*");
                response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Invalid username/password");
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return;
            }
        }
        else
            chain.doFilter(request, response);
    }
}
