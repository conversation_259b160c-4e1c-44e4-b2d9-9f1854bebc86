package com.atp.product.config;

import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;

@Configuration
public class MongoIndexConfig {

    @Autowired
    private MongoTemplate mongoTemplate;

    @PostConstruct
    public void initIndexes() {
        // Other indexes...
        
        // Recently viewed indexes
        initRecentlyViewedIndexes();
    }
    
    private void initRecentlyViewedIndexes() {
        // Create index on customerCorrelationId for faster lookups
        mongoTemplate.indexOps("recently_viewed")
                .ensureIndex(new Index().on("customerCorrelationId", Sort.Direction.ASC).unique());
        
        // Create compound index on customerCorrelationId and products.partNumberSlug for faster queries
        mongoTemplate.indexOps("recently_viewed")
                .ensureIndex(new Index().on("customerCorrelationId", Sort.Direction.ASC)
                        .on("products.partNumberSlug", Sort.Direction.ASC));
        
        // Create index on products.viewedAt for sorting
        mongoTemplate.indexOps("recently_viewed")
                .ensureIndex(new Index().on("products.viewedAt", Sort.Direction.DESC));
    }
}