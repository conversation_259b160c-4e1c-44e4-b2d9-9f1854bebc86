package com.atp.product.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Cache configuration for optimizing PurchaseOrderService performance
 */
@Configuration
@EnableCaching
public class CacheConfig {

    @Bean
    public CacheManager cacheManager() {
        // Pass cache names directly to constructor
        ConcurrentMapCacheManager cacheManager = new ConcurrentMapCacheManager("purchaseOrders");
        return cacheManager;
    }
}
