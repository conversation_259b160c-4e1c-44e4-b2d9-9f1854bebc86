package com.atp.product.mapper;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Mapping {
    private Map<String,Integer> subCategoryList;
    private Map<String,Integer> brandList;
    private Map<String,Integer> yearList;
    private Map<String,Integer> makeList;
    private Map<String,Integer> modelList;
    private Map<String,Integer> vendorList;
    private Map<String,Integer> totalProductCount;
}
