package com.atp.product.utils;

public class Constants {
    // Collection names
    public static final String PRODUCT_COLLECTION_NAME = "category_and_products";
    public static final String SHOPPING_CART_COLLECTION_NAME = "shopping_cart";
    public static final String WISHLIST_COLLECTION_NAME = "wishlist";
    // General constants
    public static final String CATALOG_ID = "catalogId";
    public static final String PARENT_ID = "parentId";
    public static final String CATEGORIES = "categories";
    public static final String SUBCATEGORIES = "subcategories";
    public static final String PRODUCT = "PRODUCT";
    public static final String DTD_WEB_HIERARCHY_ROOT = "WebHierarchyRoot";
    public static final String ACTIVE = "active";
    public static final String IS_DELETED = "isDeleted";
    public static final String BRAND_NAME = "brandName";
    public static final String BRAND_ID = "brandId";
    public static final String BRANDS = "brands";
    public static final String CATEGORY_NAME = "categoryName";
    public static final String CATALOG_TYPE = "catalogType";
    public static final String CATEGORY = "category";
    public static final String SUB_CATEGORY = "subCategory";
    public static final String NAME = "name";
    public static final String PART_NUMBER = "partNumber";
    public static final String PRICE = "price";
    public static final String PRIMARY_IMAGE = "primaryImage";
    public static final String LAST_MODIFIED_DATE = "lastModifiedDate";
    public static final String PART_NUMBER_SLUG = "partNumberSlug";
    public static final String BRAND_SLUG = "brandSlug";
    public static final String CATEGORY_SLUG = "categorySlug";
    public static final String ASSET_MAP = "assetMap";
    public static final String QUANTITY = "quantity";
    public static final String DESCRIPTION = "description";
    public static final String CUSTOMER_CORRELATION_ID = "customerCorrelationId";
    public static final String WISHLIST_FLAG = "isInWishlist";
    public static final String SUB_CATEGORY_SLUG = "subCategorySlug";
    public static final String CROSS_REFERENCE_ID = "crossReferenceId";

    //ERROR CONSTANTS
    public static final String ERROR_CODE = "errorCode";
    public static final String ERROR_MESSAGE = "errorMessage";
    public static final String INVALID_ARGUMENT_PROVIDED = "Invalid argument provided";
    public static final String INPUT_PARAMETER_NOT_FOUND = "Input parameter not found";
    public static final String ERROR_RESOURCE_NOT_FOUND = "ERROR_RESOURCE_NOT_FOUND";
    public static final String ERROR_DUPLICATE_RESOURCE = "DUPLICATE_RESOURCE"; // New constant for this case


    //Category Service Constants
    public static final String MAKE_LIST = "makeList";
    public static final String MODEL_LIST = "modelList";
    public static final String BRAND_LIST = "brandList";
    public static final String SUB_CATEGORY_LIST = "subCategoryList";
    public static final String VENDOR_LIST = "vendorList";
    public static final String YEAR_LIST = "yearList";
    public static final String CATEGORY_LIST = "categoryList";
    public static final String MAKE = "make";
    public static final String MODEL = "model";
    public static final String YEAR = "year";
    public static final String VENDOR_NAME = "vendorName";
    public static final String ID = "_id";
    public static final String ARRAY_TO_OBJECT = "$arrayToObject";
    public static final String KEY ="k";
    public static final String VALUE ="v";
    public static final String COUNT = "count";
    public static final String ITEM = "item";
    public static final String AS = "as";
    public static final String IN ="in";
    public static final String INPUT ="input";
    public static final String MAP ="map";
    public static final String PROJECT ="project";

    //Atlas Search Parameters
    public static final int MAX_TYPE_AHEAD_RESULTS = 10;
    public static final String AUTOCOMPLETE = "autocomplete";
    public static final String PATH = "path";
    public static final String PATTERN = "pattern";
    public static final String QUERY ="query";
    public static final String FUZZY = "fuzzy";
    public static final String MAX_EDITS = "maxEdits";
    public static final String PREFIX_LENGTH = "prefixLength";
    public static final String FILTER = "filter";
    public static final String COND = "cond";
    public static final String INITIAL_VALUE = "initialValue";
    public static final String PARTS_INVENTORY_DETAIL_ID = "partsInventoryDetailId";
    public static final String BRANCH_CODE = "branch";


    // Private constructor to prevent instantiation
    private Constants() {
        throw new UnsupportedOperationException("Constants class cannot be instantiated");
    }
}