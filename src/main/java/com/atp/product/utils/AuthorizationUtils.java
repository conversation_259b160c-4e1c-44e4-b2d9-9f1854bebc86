package com.atp.product.utils;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Objects;

public class AuthorizationUtils {
    private AuthorizationUtils() {
        // Prevent instantiation
    }
    /**
 * Checks if the Authorization header is present, non-blank, and non-empty.
 *
 * @param request The HttpServletRequest object containing the request headers.
 * @return true if the Authorization header is present, non-blank, and non-empty; false otherwise.
 */
    public static boolean isAuthorizationHeaderValid(HttpServletRequest request) {
        String authorizationHeader = request.getHeader("Authorization");
        return Objects.nonNull(authorizationHeader) && !authorizationHeader.isBlank();
    }

    public static String getCustomerCorrelationId(HttpServletRequest request) {
        return request.getHeader("Customercorrelationid");
    }

    public static String getKarmakCustomerId(HttpServletRequest request) {
        return request.getHeader("Karmakcustomerid");
    }

    public static void setCustomerIdInHeader(HttpServletResponse response, String customerID) {
            response.setHeader("Karmakcustomerid", customerID);

    }

    public static String getKarmakCustomerKey(HttpServletRequest request) {
        return request.getHeader("Karmakcustomerkey");
    }

}
