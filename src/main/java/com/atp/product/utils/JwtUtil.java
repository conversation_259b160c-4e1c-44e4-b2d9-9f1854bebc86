package com.atp.product.utils;

import com.atp.product.exception.DomainException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import io.jsonwebtoken.io.IOException;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Base64;
import java.util.Map;

@Component
public class JwtUtil {

    private JwtUtil() {
        // Prevent instantiation
    }
    public static String extractUser(String token) {
        String username;
        String[] chunks = token.split("\\.");
        String payload = new String(Base64.getUrlDecoder().decode(chunks[1]));
        Gson gson = new Gson();
        Map<String, String> map = gson.fromJson(payload, Map.class);
        username = map.get("username");
        return username;
    }

    public static boolean isTokenExpired(String token) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            String[] chunks = token.split("\\.");
            String payload = new String(Base64.getUrlDecoder().decode(chunks[1]));
            JsonNode jsonNode = objectMapper.readTree(payload);
            long exp = jsonNode.get("exp").asLong();
            long currentTime = Instant.now().getEpochSecond();
            return currentTime > exp;
        } catch (IOException e) {
            e.printStackTrace();
            return true;
        }  catch (JsonProcessingException e) {
            throw new DomainException(e);
        }
    }
}
