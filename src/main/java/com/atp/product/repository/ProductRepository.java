package com.atp.product.repository;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.data.mongodb.repository.MongoRepository;
import com.atp.product.model.Product;

import java.util.List;
import java.util.Map;

public interface ProductRepository extends MongoRepository<Product, String> {

    @Aggregation(pipeline = {
            "{ '$facet': { 'brands': [ { '$match': { 'category': 'Suspension', 'isDeleted': false, 'active': true } }, { '$group': { '_id': '$brandName', 'count': { '$sum': 1 } } }, { '$project': { '_id': 0, 'brandName': '$_id', 'count': 1 } } ], 'allBrands': [ { '$group': { '_id': null, 'allBrands': { '$addToSet': '$brandName' } } } ] } }",
            "{ '$unwind': '$allBrands' }",
            "{ '$project': { '_id': 0, 'brandList': { '$arrayToObject': { '$map': { 'input': '$allBrands.allBrands', 'as': 'brand', 'in': { 'k': '$$brand', 'v': { '$cond': { 'if': { '$isArray': '$brands' }, 'then': { '$reduce': { 'input': '$brands', 'initialValue': 0, 'in': { '$add': ['$$value', { '$cond': { 'if': { '$eq': ['$$this.brandName', '$$brand'] }, 'then': '$$this.count', 'else': 0 } }] } } }, 'else': 0 } } } } } } } }"
    })
    List<Map<String, Object>> getBrandCounts(Criteria matchCriteria);

    boolean existsByPartNumber(String partNumber);

    Product findByPartNumber(String partNumber);
}
