package com.atp.product.exception;

import java.util.ResourceBundle;

public class DomainException extends RuntimeException{
    private static final long serialVersionUID = 1L;
    private static ResourceBundle validationErrors;
    private String localizedMessage;

    public DomainException(String message) {
        super(message);
    }
    public DomainException(String message, Throwable exception) {
        super(message, exception);
    }

    public DomainException(Throwable exception) {
        super(exception);
    }

    public DomainException(String key, String extraMessage) {
        try {
            localizedMessage = validationErrors.getString(key);
            localizedMessage += extraMessage != null ? " " + extraMessage : "";
        } catch (Exception e) {
            localizedMessage = key + " " + extraMessage;
        }
    }
    @Override
    public String getLocalizedMessage() {
        return getMessage();
    }
    @Override
    public String getMessage() {
        if (localizedMessage != null) {
            return localizedMessage;
        } else {
            return super.getMessage();
        }
    }
}

