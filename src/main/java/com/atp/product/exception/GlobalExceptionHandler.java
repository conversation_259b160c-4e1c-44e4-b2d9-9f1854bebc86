package com.atp.product.exception;

import com.atp.product.controller.dto.response.ErrorResponse;
import com.atp.product.exception.bad_request.InvalidInputException;
import com.atp.product.exception.bad_request.KarmakApiException;
import com.atp.product.exception.bad_request.PurchaseOrderCreationException;
import com.atp.product.exception.bad_request.ScheduledOrderAlreadyExistsException;
import com.atp.product.exception.not_found.ResourceNotFoundException;
import com.atp.product.utils.Constants;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@ControllerAdvice
public class GlobalExceptionHandler {

    // Handle missing request parameters
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<ErrorResponse> handleMissingParams(MissingServletRequestParameterException ex, WebRequest request) {
        Map<String, Object> paramDetails = new HashMap<>();
        paramDetails.put(Constants.NAME, ex.getParameterName());
        paramDetails.put(Constants.ERROR_CODE, "ERROR_PARAM_NOT_PRESENT");
        paramDetails.put(Constants.ERROR_MESSAGE, ex.getMessage());

        ErrorResponse errorResponse = new ErrorResponse(
                LocalDateTime.now(),
                HttpStatus.BAD_REQUEST.value(),
                "Bad Request",
                "Input param(s) are not valid.",
                request.getDescription(false).replace("uri=", ""),
                "ERROR_PARAMETERS_VALIDATION",
                paramDetails
        );

        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(InvalidInputException.class)
    public ResponseEntity<ErrorResponse> handleInvalidInputException(InvalidInputException ex, WebRequest request) {
        Map<String, Object> paramDetails = new HashMap<>();
        paramDetails.put(Constants.ERROR_CODE, "ERROR_INVALID_INPUT");
        paramDetails.put(Constants.ERROR_MESSAGE, ex.getMessage());

        ErrorResponse errorResponse = new ErrorResponse(
                LocalDateTime.now(),
                HttpStatus.BAD_REQUEST.value(),
                "Bad Request",
                "Input parameter error.",
                request.getDescription(false).replace("uri=", ""),
                "ERROR_INVALID_INPUT",
                paramDetails
        );

        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(ResourceNotFoundException.class)
    public ResponseEntity<ErrorResponse> handleResourceNotFoundException(ResourceNotFoundException ex, WebRequest request) {
        Map<String, Object> paramDetails = new HashMap<>();
        paramDetails.put(Constants.ERROR_CODE, Constants.ERROR_RESOURCE_NOT_FOUND);
        paramDetails.put(Constants.ERROR_MESSAGE, ex.getMessage());

        ErrorResponse errorResponse = new ErrorResponse(
                LocalDateTime.now(),
                HttpStatus.NOT_FOUND.value(),
                "Not Found",
                "Resource not found.",
                request.getDescription(false).replace("uri=", ""),
                Constants.ERROR_RESOURCE_NOT_FOUND,
                paramDetails
        );

        return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(DomainException.class)
    public ResponseEntity<ErrorResponse> handleGenericException(Exception ex, WebRequest request) {
        ErrorResponse errorResponse = new ErrorResponse(
                LocalDateTime.now(),
                HttpStatus.INTERNAL_SERVER_ERROR.value(),
                "Internal Server Error",
                "An unexpected error occurred.",
                request.getDescription(false).replace("uri=", ""),
                "ERROR_INTERNAL_SERVER",
                null
        );

        return new ResponseEntity<ErrorResponse>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler(KarmakApiException.class)
    public ResponseEntity<ErrorResponse> handleKarmakApiException(KarmakApiException ex, WebRequest request) {
        Map<String, Object> details = new HashMap<>();
        details.put(Constants.ERROR_MESSAGE, ex.getMessage());

        ErrorResponse errorResponse = new ErrorResponse(
                LocalDateTime.now(),
                HttpStatus.BAD_GATEWAY.value(),
                "External API Error",
                ex.getMessage(),
                request.getDescription(false).replace("uri=", ""),
                "ERROR_KARMAK_API_FAILURE",
                details
        );

        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_GATEWAY);
    }

    @ExceptionHandler(PurchaseOrderCreationException.class)
    public ResponseEntity<ErrorResponse> handlePurchaseOrderCreationException(PurchaseOrderCreationException ex, WebRequest request) {
        Map<String, Object> details = new HashMap<>();
        details.put(Constants.ERROR_MESSAGE, ex.getMessage());

        ErrorResponse errorResponse = new ErrorResponse(
                LocalDateTime.now(),
                HttpStatus.BAD_GATEWAY.value(),
                "External API Error",
                ex.getMessage(),
                request.getDescription(false).replace("uri=", ""),
                "ERROR_PO_CREATION_FAILED",
                details
        );

        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_GATEWAY);
    }

    // Handler for ScheduledOrderAlreadyExistsException
    @ExceptionHandler(ScheduledOrderAlreadyExistsException.class)
    public ResponseEntity<ErrorResponse> handleScheduledOrderAlreadyExistsException(
            ScheduledOrderAlreadyExistsException ex,
            WebRequest request) {

        Map<String, Object> paramDetails = new HashMap<>();
        paramDetails.put(Constants.ERROR_CODE, Constants.ERROR_DUPLICATE_RESOURCE);
        paramDetails.put(Constants.ERROR_MESSAGE, ex.getMessage());

        ErrorResponse errorResponse = new ErrorResponse(
                LocalDateTime.now(),
                HttpStatus.OK.value(),                        // ✅ Still 200 in body
                "OK",                                         // ✅ Matches 200
                "A scheduled order already exists for the given original order ID.",
                request.getDescription(false).replace("uri=", ""),
                Constants.ERROR_DUPLICATE_RESOURCE,
                paramDetails
        );

        return ResponseEntity.ok(errorResponse);             // ✅ Return 200 OK
    }


}