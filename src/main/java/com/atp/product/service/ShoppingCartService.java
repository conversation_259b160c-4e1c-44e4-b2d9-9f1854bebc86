package com.atp.product.service;

import com.atp.product.controller.dto.request.ShoppingCartRequest;
import com.atp.product.controller.dto.response.ShoppingCartListResponse;

public interface ShoppingCartService {
    /**
     * Get Shopping cart list
     */
    ShoppingCartListResponse getShoppingCartList(String customerCorrelationId);
    /**
     * Save Shopping cart details
     */
    Integer saveShoppingCartDetails(ShoppingCartRequest shoppingCartRequest);
    /**
     * Update Shopping cart details
     */
    boolean updateShoppingCartDetails(ShoppingCartRequest shoppingCartRequest);
    /**
     * Delete all shopping cart details
     */
    boolean deleteShoppingCartByCustomerCorrelationId(String customerCorrelationId);
    /**
     * Delete particular one product in the shopping cart details
     */
    boolean deleteShoppingCartDetailsByPartNumber(ShoppingCartRequest shoppingCartRequest);
    /**
     * Get total product count in the shopping cart
     */
    Integer getTotalProductCount(String customerCorrelationId);
}
