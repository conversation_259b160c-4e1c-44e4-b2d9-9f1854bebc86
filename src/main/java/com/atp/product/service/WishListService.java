package com.atp.product.service;

import com.atp.product.controller.dto.request.WishlistRequest;
import com.atp.product.controller.dto.response.WishlistResponse;

public interface WishListService {
    /**
     * Get Wishlist list
     */
    WishlistResponse getWishlist(String customerCorrelationId);
    /**
     * Save Wishlist details
     */
    Integer saveWishlistDetails(WishlistRequest wishlistRequest);
    /**
     * Update Wishlist details
     */
    boolean updateWishlistDetails(WishlistRequest wishlistRequest);
    /**
     * Delete Wishlist details
     */
    boolean deleteWishlistByCustomerCorrelationId(String customerCorrelationId);
    /**
     * Delete particular one product in the Wishlist details
     */
    boolean deleteWishlistDetailsByPartNumber(WishlistRequest wishlistRequest);
}
