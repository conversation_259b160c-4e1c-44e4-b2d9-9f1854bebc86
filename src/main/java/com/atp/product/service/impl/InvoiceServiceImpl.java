package com.atp.product.service.impl;

import com.atp.product.karmak_responses.*;
import com.atp.product.service.InvoiceService;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.layout.properties.UnitValue;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.kernel.geom.PageSize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

/**
 * Service implementation for invoice generation
 */
@Service
@Slf4j
public class InvoiceServiceImpl implements InvoiceService {

    private final PurchaseOrderService purchaseOrderService;
    private final MongoTemplate mongoTemplate;

    @Autowired
    public InvoiceServiceImpl(PurchaseOrderService purchaseOrderService, MongoTemplate mongoTemplate) {
        this.purchaseOrderService = purchaseOrderService;
        this.mongoTemplate = mongoTemplate;
    }

    @Override
    public ByteArrayResource generateInvoice(String name, String email, String externalId) {
        log.info("Generating invoice for customer: {}, email: {}, externalId: {}", name, email, externalId);

        try {
            // OPTIMIZED: Use direct MongoDB aggregation for faster data retrieval
            KarmakPurchaseOrder order = getOrderByExternalIdOptimized(externalId);

            if (order == null) {
                log.warn("Purchase order not found for externalId: {}", externalId);
                return null;
            }

            return generatePdfInvoice(order, name, email);

        } catch (Exception e) {
            log.error("Error generating invoice for externalId: {}", externalId, e);
            throw new RuntimeException("Failed to generate invoice", e);
        }
    }

    @Override
    public ByteArrayResource generateInvoiceById(String orderId, String name, String email) {
        log.info("Generating invoice for orderId: {}, customer: {}, email: {}", orderId, name, email);

        try {
            // OPTIMIZED: Use direct MongoDB aggregation for faster data retrieval
            KarmakPurchaseOrder order = getOrderByIdOptimized(orderId);

            if (order == null) {
                log.warn("Purchase order not found for orderId: {}", orderId);
                return null;
            }

            return generatePdfInvoice(order, name, email);

        } catch (Exception e) {
            log.error("Error generating invoice for orderId: {}", orderId, e);
            throw new RuntimeException("Failed to generate invoice", e);
        }
    }

    /**
     * Generate PDF invoice from purchase order data
     */
    private ByteArrayResource generatePdfInvoice(KarmakPurchaseOrder order, String customerName, String customerEmail) {
        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            PdfWriter writer = new PdfWriter(out);
            PdfDocument pdfDocument = new PdfDocument(writer);
            Document document = new Document(pdfDocument, PageSize.A4);
            document.setMargins(30, 30, 30, 30);

            // Get current date for invoice
            SimpleDateFormat dateFormat = new SimpleDateFormat("dd MMM yyyy, HH:mm a");
            String invoiceDate = dateFormat.format(new Date());

            // Title: INVOICE
            document.add(new Paragraph("INVOICE")
                    .setBold()
                    .setFontSize(20)
                    .setTextAlignment(TextAlignment.CENTER));
            document.add(new Paragraph(" "));

            // Invoice Info
            Table invoiceDetails = new Table(UnitValue.createPercentArray(new float[]{50, 50}))
                    .useAllAvailableWidth();
            
            String invoiceNumber = order.getExternalId() != null ? order.getExternalId() : 
                                 (order.getExternalId() != null ? order.getExternalId() : "N/A");
            
            invoiceDetails.addCell(new Cell().add(new Paragraph("Invoice No: #" + invoiceNumber))
                    .setBorder(Border.NO_BORDER).setBold());
            invoiceDetails.addCell(new Cell().add(new Paragraph("Invoice Date: " + invoiceDate))
                    .setTextAlignment(TextAlignment.RIGHT).setBold()
                    .setBorder(Border.NO_BORDER));
            document.add(invoiceDetails);
            document.add(new Paragraph(" "));

            // Customer and Company Info
            Table infoTable = new Table(UnitValue.createPercentArray(new float[]{50, 50}))
                    .useAllAvailableWidth();

            String customerInfo = customerName + "\n" +
                    "Email: " + customerEmail;

            infoTable.addCell(new Cell().add(new Paragraph()
                            .add(new Text("Billed To:\n").setBold().setUnderline())
                            .add(customerInfo))
                    .setBorder(Border.NO_BORDER));

            infoTable.addCell(new Cell().add(new Paragraph()
                            .add(new Text("Sold By:\n").setBold().setUnderline())
                            .add("ATP Parts Company\nEmail: <EMAIL>"))
                    .setTextAlignment(TextAlignment.RIGHT)
                    .setBorder(Border.NO_BORDER));

            document.add(infoTable);
            document.add(new Paragraph(" "));

            // Items Table
            Table table = new Table(UnitValue.createPercentArray(new float[]{40, 15, 20, 25}))
                    .useAllAvailableWidth();

            table.addHeaderCell(new Cell().add(new Paragraph("Item").setBold()));
            table.addHeaderCell(new Cell().add(new Paragraph("Qty").setBold()));
            table.addHeaderCell(new Cell().add(new Paragraph("Unit Price").setBold()));
            table.addHeaderCell(new Cell().add(new Paragraph("Total").setBold()));

            double grandTotal = 0;

            // Extract items from ComplexValues
            if (order.getComplexValues() != null && 
                order.getComplexValues().getPurchaseOrders() != null) {
                
                List<PurchaseOrder> purchaseOrders = order.getComplexValues().getPurchaseOrders();
                
                for (PurchaseOrder po : purchaseOrders) {
                    if (po.getValues() != null) {
                        Values values = po.getValues();
                        
                        // Extract item details
                        String itemName = extractStringValue(values.getOrderProductDetails(), "Product Details");
                        String partNumber = extractStringValue(values.getOrderPartNumber(), "Part Number");
                        double quantity = extractDoubleValue(values.getOrderQuantity(), 1.0);
                        double cost = extractDoubleValue(values.getOrderCost(), 0.0);
                        double itemTotal = quantity * cost;
                        
                        grandTotal += itemTotal;

                        // Add item to table
                        String displayName = itemName + "\n(Part #: " + partNumber + ")";
                        table.addCell(new Cell().add(new Paragraph(displayName)));
                        table.addCell(new Cell().add(new Paragraph(String.valueOf((int)quantity)))
                                .setTextAlignment(TextAlignment.RIGHT));
                        table.addCell(new Cell().add(new Paragraph(String.format("$ %.2f", cost)))
                                .setTextAlignment(TextAlignment.RIGHT));
                        table.addCell(new Cell().add(new Paragraph(String.format("$ %.2f", itemTotal)))
                                .setTextAlignment(TextAlignment.RIGHT));
                    }
                }
            }

            // If no items found in ComplexValues, try to get total from Values
            if (grandTotal == 0) {
                grandTotal = extractGrandTotalFromValues(order.getValues());
                
                // Add a generic item row
                table.addCell(new Cell().add(new Paragraph("Purchase Order Items")));
                table.addCell(new Cell().add(new Paragraph("1"))
                        .setTextAlignment(TextAlignment.RIGHT));
                table.addCell(new Cell().add(new Paragraph(String.format("$ %.2f", grandTotal)))
                        .setTextAlignment(TextAlignment.RIGHT));
                table.addCell(new Cell().add(new Paragraph(String.format("$ %.2f", grandTotal)))
                        .setTextAlignment(TextAlignment.RIGHT));
            }

            document.add(table);
            document.add(new Paragraph(" "));

            // Total Amount
            Table totalTable = new Table(UnitValue.createPercentArray(new float[]{40, 15, 20, 25}))
                    .useAllAvailableWidth();

            totalTable.addCell(new Cell(0, 3).add(new Paragraph("Grand Total").setBold()));
            totalTable.addCell(new Cell().add(new Paragraph(String.format("$ %.2f", grandTotal)).setBold())
                    .setTextAlignment(TextAlignment.RIGHT));

            document.add(totalTable);
            document.add(new Paragraph(" "));

            // Footer
            document.add(new Paragraph("Payment Method: Purchase Order")
                    .setBold()
                    .setFontSize(10)
                    .setUnderline());

            document.add(new Paragraph("\nThank you for your business!\nThis is a system-generated invoice and does not require a signature.")
                    .setFontSize(9)
                    .setTextAlignment(TextAlignment.CENTER));

            document.close();

            return new ByteArrayResource(out.toByteArray());

        } catch (Exception e) {
            log.error("Error generating PDF invoice", e);
            throw new RuntimeException("Failed to generate PDF invoice", e);
        }
    }

    /**
     * Extract string value from order attribute
     */
    private String extractStringValue(Object attribute, String defaultValue) {
        if (attribute == null) return defaultValue;
        
        try {
            // Use reflection to get content
            java.lang.reflect.Method getContentMethod = attribute.getClass().getMethod("getContent");
            @SuppressWarnings("unchecked")
            List<Content> contentList = (List<Content>) getContentMethod.invoke(attribute);
            
            if (contentList != null && !contentList.isEmpty()) {
                Object value = contentList.get(0).getValue();
                return value != null ? value.toString() : defaultValue;
            }
        } catch (Exception e) {
            log.debug("Failed to extract string value from attribute", e);
        }
        
        return defaultValue;
    }

    /**
     * Extract double value from order attribute
     */
    private double extractDoubleValue(Object attribute, double defaultValue) {
        if (attribute == null) return defaultValue;
        
        try {
            // Use reflection to get content
            java.lang.reflect.Method getContentMethod = attribute.getClass().getMethod("getContent");
            @SuppressWarnings("unchecked")
            List<Content> contentList = (List<Content>) getContentMethod.invoke(attribute);
            
            if (contentList != null && !contentList.isEmpty()) {
                Object value = contentList.get(0).getValue();
                if (value instanceof Number) {
                    return ((Number) value).doubleValue();
                } else if (value instanceof String) {
                    return Double.parseDouble((String) value);
                }
            }
        } catch (Exception e) {
            log.debug("Failed to extract double value from attribute", e);
        }
        
        return defaultValue;
    }

    /**
     * Extract grand total from Values array
     */
    private double extractGrandTotalFromValues(List<com.atp.product.karmak_responses.Value> values) {
        if (values == null) return 0.0;
        
        for (com.atp.product.karmak_responses.Value value : values) {
            if ("ECM_GrandTotal".equals(value.getAttributeId())) {
                List<Content> content = value.getContent();
                if (content != null && !content.isEmpty()) {
                    Object val = content.get(0).getValue();
                    if (val instanceof Number) {
                        return ((Number) val).doubleValue();
                    } else if (val instanceof String) {
                        try {
                            return Double.parseDouble((String) val);
                        } catch (NumberFormatException e) {
                            log.debug("Failed to parse grand total: {}", val);
                        }
                    }
                }
            }
        }
        
        return 0.0;
    }

    // ==================== OPTIMIZED DATABASE ACCESS METHODS ====================

    /**
     * OPTIMIZED: Get order by external ID using MongoDB aggregation with field projection
     * This is faster than service layer as it only fetches required fields
     */
    private KarmakPurchaseOrder getOrderByExternalIdOptimized(String externalId) {
        log.debug("Fetching order with optimized aggregation for externalId: {}", externalId);

        try {
            // MongoDB Aggregation with field projection for better performance
            Aggregation aggregation = Aggregation.newAggregation(
                // Match the document by externalId
                Aggregation.match(Criteria.where("externalId").is(externalId)),
                // Project only the fields needed for invoice generation
                Aggregation.project()
                    .and("_id").as("id")
                    .and("externalId").as("externalId")
                    .and("values").as("values")
                    .and("complexValues.purchaseOrders").as("complexValues.purchaseOrders")
                    .and("createdAt").as("createdAt")
                    .and("updatedAt").as("updatedAt")
            );

            AggregationResults<KarmakPurchaseOrder> results = mongoTemplate.aggregate(
                aggregation, "karmak_purchase_orders", KarmakPurchaseOrder.class);

            List<KarmakPurchaseOrder> orders = results.getMappedResults();

            if (orders.isEmpty()) {
                log.debug("No order found for externalId: {}", externalId);
                return null;
            }

            log.debug("Successfully fetched order using optimized aggregation for externalId: {}", externalId);
            return orders.get(0);

        } catch (Exception e) {
            log.warn("Optimized aggregation failed for externalId: {}, falling back to service layer", externalId, e);
            // Fallback to service layer if aggregation fails
            return purchaseOrderService.getPurchaseOrderByExternalId(externalId);
        }
    }

    /**
     * OPTIMIZED: Get order by ID using direct MongoDB query with field projection
     * This is faster than service layer as it only fetches required fields
     */
    private KarmakPurchaseOrder getOrderByIdOptimized(String orderId) {
        log.debug("Fetching order with optimized query for orderId: {}", orderId);

        try {
            // Direct MongoDB query with field projection
            Query query = new Query(Criteria.where("_id").is(orderId));

            // Include only fields needed for invoice generation
            query.fields()
                .include("_id")
                .include("externalId")
                .include("values")
                .include("complexValues.purchaseOrders")
                .include("createdAt")
                .include("updatedAt");

            KarmakPurchaseOrder order = mongoTemplate.findOne(query, KarmakPurchaseOrder.class, "karmak_purchase_orders");

            if (order != null) {
                log.debug("Successfully fetched order using optimized query for orderId: {}", orderId);
            } else {
                log.debug("No order found for orderId: {}", orderId);
            }

            return order;

        } catch (Exception e) {
            log.warn("Optimized query failed for orderId: {}, falling back to service layer", orderId, e);
            // Fallback to service layer if direct query fails
            return purchaseOrderService.getPurchaseOrderByExternalId(orderId);
        }
    }

    /**
     * ALTERNATIVE: Ultra-fast aggregation that only gets essential invoice data
     * Use this if you want maximum performance and don't mind custom data structure
     */
    private InvoiceData getInvoiceDataOptimized(String externalId) {
        log.debug("Fetching minimal invoice data for externalId: {}", externalId);

        try {
            Aggregation aggregation = Aggregation.newAggregation(
                // Match the document
                Aggregation.match(Criteria.where("externalId").is(externalId)),
                // Project only essential invoice fields
                Aggregation.project()
                    .and("externalId").as("invoiceNumber")
                    .and("values").as("values")
                    .and("complexValues.purchaseOrders.values.orderPartNumber").as("partNumbers")
                    .and("complexValues.purchaseOrders.values.orderProductDetails").as("productDetails")
                    .and("complexValues.purchaseOrders.values.orderQuantity").as("quantities")
                    .and("complexValues.purchaseOrders.values.orderCost").as("costs")
            );

            // This would return a custom InvoiceData object with only needed fields
            // Fastest option but requires custom data structure

            return null; // Placeholder - implement if needed

        } catch (Exception e) {
            log.error("Failed to fetch optimized invoice data", e);
            return null;
        }
    }

    /**
     * Custom data structure for ultra-fast invoice data retrieval
     */
    private static class InvoiceData {
        private String invoiceNumber;
        private List<InvoiceItem> items;
        private double grandTotal;

        // Getters and setters...
    }

    private static class InvoiceItem {
        private String partNumber;
        private String description;
        private int quantity;
        private double unitPrice;
        private double total;

        // Getters and setters...
    }
}
