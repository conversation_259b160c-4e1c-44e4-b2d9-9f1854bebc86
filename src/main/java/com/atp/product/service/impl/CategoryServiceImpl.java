package com.atp.product.service.impl;

import com.atp.product.controller.dto.request.FilterRequest;
import com.atp.product.controller.dto.response.*;
import com.atp.product.model.Product;
import com.atp.product.model.SortType;
import com.atp.product.repository.ProductRepository;
import com.atp.product.service.CategoryService;
import com.atp.product.service.ProductService;
import com.atp.product.utils.AuthorizationUtils;
import com.atp.product.utils.Constants;
import com.atp.product.exception.DomainException;
import com.mongodb.BasicDBObject;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Stream;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;

@Service
@Slf4j
public class CategoryServiceImpl implements CategoryService {

    @Value("${spring.data.mongodb.uri}")
    private String mongoUri;
    private final MongoTemplate mongoTemplate;
    private final ProductServiceImpl productServiceImpl;
    private final CommonServiceImpl commonServiceImpl;
    private final HttpServletRequest request;
    private final ProductService productService;
    private final ProductRepository productRepository;

    public CategoryServiceImpl(MongoTemplate mongoTemplate, ProductServiceImpl productServiceImpl, CommonServiceImpl commonServiceImpl, HttpServletRequest request, ProductService productService, ProductRepository productRepository) {
        this.mongoTemplate = mongoTemplate;
        this.productServiceImpl = productServiceImpl;
        this.commonServiceImpl = commonServiceImpl;
        this.request = request;
        this.productService = productService;
        this.productRepository = productRepository;
    }
    @Override
    public List<CategorySubcategoryResponse> getCategorySubCategoryList() {
        log.info("Inside call to getCategorySubCategoryList() service");
        AggregationResults<CategorySubcategoryResponse> results;
        try {
            LookupOperation lookupOperation = LookupOperation.newLookup()
                    .from(Constants.PRODUCT_COLLECTION_NAME)
                    .localField(Constants.CATALOG_ID)
                    .foreignField(Constants.PARENT_ID)
                    .as(Constants.SUB_CATEGORY_LIST);
            MatchOperation matchOperation = match(new Criteria().orOperator(
                    //Criteria.where(Constants.SUB_CATEGORY_LIST).elemMatch(Criteria.where(Constants.CATALOG_TYPE).ne(Constants.PRODUCT)),
                    Criteria.where(Constants.PARENT_ID).is(Constants.DTD_WEB_HIERARCHY_ROOT)
            ).andOperator(
                    Criteria.where(Constants.ACTIVE).is(true),
                    Criteria.where(Constants.IS_DELETED).is(false)
            ));
            ProjectionOperation projectionOperation = project(Constants.CATALOG_ID, Constants.NAME, Constants.CATEGORY_SLUG)
                    .and(Constants.SUB_CATEGORY_LIST).as(Constants.SUB_CATEGORY_LIST);

            Aggregation aggregation = newAggregation( lookupOperation, matchOperation, projectionOperation);
            log.info("Aggregation pipeline getCategorySubCategoryList(): {}", aggregation);

            results = mongoTemplate.aggregate(aggregation, Constants.PRODUCT_COLLECTION_NAME, CategorySubcategoryResponse.class);
            log.info("Aggregation pipeline results getCategorySubCategoryList(): {}", results.getMappedResults());

            return results.getMappedResults();
        } catch (IllegalArgumentException e) {
            throw new DomainException(Constants.INVALID_ARGUMENT_PROVIDED, e.getMessage());
        } catch (Exception e) {
            throw new DomainException("An error occurred while retrieving category and subcategory list", e.getMessage());
        }
    }

    @Override
    public FilterResponse getAllFilterListForProductCatalog(FilterRequest filterRequest) {
        log.info("Inside call to getAllFilterListForProductCatalog() service with filterRequest: {}", filterRequest);
        FilterResponse response = new FilterResponse();
        try {
            boolean exists = productRepository.existsByPartNumber(filterRequest.getQueryString());
            List<String> crossReferenceIds = new ArrayList<>();
            if(exists){
                Product product = productRepository.findByPartNumber(filterRequest.getQueryString());
                if(product != null){
                    crossReferenceIds.add(product.getCatalogId());
                    //crossReferenceIds.addAll(product.getCrossReferenceId());
                    if (product.getCrossReferenceId() != null) {
                        crossReferenceIds.addAll(product.getCrossReferenceId());
                    }

                    List<ProductResponseHomePage> similarProducts = productService.getSimilarProductListByCrossReferenceId(crossReferenceIds);
                    response.setProductList(similarProducts);
                }
                filterRequest.setCategoryList(crossReferenceIds);
            }
            else {
                response.setProductList(getProductListByFilter(filterRequest));
            }

            List<Map<String, Object>> processedBrandList = processMapAggregation(filterRequest,Constants.BRAND_NAME,exists);
            if(!processedBrandList.isEmpty()) {
                Map<String, Object> brandMap = processedBrandList.get(0);
                response.setBrandList((Map<String, Integer>) brandMap.get(Constants.BRAND_NAME));
            }
            else
                response.setBrandList(new HashMap<>());

            List<Map<String, Object>> processedVendorList = processMapAggregation(filterRequest,Constants.VENDOR_NAME,exists);
            if(!processedVendorList.isEmpty()){
                Map<String, Object> vendorMap = processedVendorList.get(0);
                response.setVendorList((Map<String, Integer>) vendorMap.get(Constants.VENDOR_NAME));
            }
            else
                response.setVendorList(new HashMap<>());

            List<Map<String, Object>> processedSubCategoryList = processMapAggregation(filterRequest,Constants.SUB_CATEGORY,exists);
            if(!processedSubCategoryList.isEmpty()){
                Map<String, Object> subCategoryMap = processedSubCategoryList.get(0);
                response.setSubCategoryList((Map<String, Integer>) subCategoryMap.get(Constants.SUB_CATEGORY));
            }
            else
                response.setSubCategoryList(new HashMap<>());

            List<Map<String, Object>> processedYearList = processMapAggregation(filterRequest,Constants.YEAR,exists);
            if(!processedYearList.isEmpty()) {
                Map<String, Object> yearMap = processedYearList.get(0);
                response.setYearList((Map<String, Integer>) yearMap.get(Constants.YEAR));
            }
            else
                response.setYearList(new HashMap<>());

            List<Map<String, Object>> processedCategoryList = processMapAggregation(filterRequest,Constants.CATEGORY,exists);
            if(!processedCategoryList.isEmpty()) {
                Map<String, Object> categoryMap = processedCategoryList.get(0);
                response.setCategoryList((Map<String, Integer>) categoryMap.get(Constants.CATEGORY));
            }
            else
                response.setCategoryList(new HashMap<>());

            List<Map<String, Object>> processedModelList = processArrayToMapAggregation(filterRequest,Constants.MODEL, exists);
            if(!processedModelList.isEmpty()){
                Map<String, Object> modelMap = processedModelList.get(0);
                response.setModelList((Map<String, Integer>) modelMap.get(Constants.MODEL));
            }
            else
                response.setModelList(new HashMap<>());

            List<Map<String, Object>> processedMakeList = processArrayToMapAggregation(filterRequest,Constants.MAKE, exists);
            if(!processedMakeList.isEmpty()){
                Map<String, Object> makeMap = processedMakeList.get(0);
                response.setMakeList((Map<String, Integer>) makeMap.get(Constants.MAKE));
            }
            else
                response.setMakeList(new HashMap<>());

            log.info("results: {}", response);
            //Get sub category list for filter product catalog
            response.setSubCategoryList(getListForProductCatalog(filterRequest, Constants.SUB_CATEGORY, Constants.SUBCATEGORIES, Constants.BRAND_LIST, response.getSubCategoryList(), exists));
            //Get brand list for filter product catalog
            response.setBrandList(getListForProductCatalog(filterRequest, Constants.BRAND_NAME, "brands", Constants.BRAND_LIST, response.getBrandList(), exists));
            //Get vendor list for filter product catalog
            response.setVendorList(getListForProductCatalog(filterRequest, Constants.VENDOR_NAME, "vendors", Constants.VENDOR_LIST, response.getVendorList(), exists));
            //Get make list for filter product catalog
            response.setMakeList(getListForProductCatalog(filterRequest, Constants.MAKE, "makes", Constants.MAKE_LIST, response.getMakeList(), exists));
            //Get model list for filter product catalog
            response.setModelList(getListForProductCatalog(filterRequest, Constants.MODEL, "models", Constants.MODEL_LIST, response.getModelList(), exists));
            //Get year list for filter product catalog
            response.setYearList(getListForProductCatalog(filterRequest, Constants.YEAR, "years", Constants.YEAR_LIST, response.getYearList(), exists));
            //Get category list for filter product catalog
            response.setCategoryList(getListForProductCatalog(filterRequest, Constants.CATEGORY, "categories", Constants.CATEGORY_LIST, response.getCategoryList(), exists));
            //Get total product count for filter product catalog
            response.setTotalProductCount(getTotalProductCountForProductCatalog(filterRequest, exists));
           // response.setTotalProductCount(response.getProductList().size());
            log.info("Aggregation pipeline results getAllFilterListForProductCatalog(): {}", response);
            return response;
        } catch (IllegalArgumentException e) {
            throw new DomainException(Constants.INVALID_ARGUMENT_PROVIDED, e.getMessage());
        } catch (Exception e) {
            throw new DomainException("An error occurred while retrieving filter list for product catalog: ", e.getMessage());
        }
    }

    private Map<String, Integer> getListForProductCatalog(FilterRequest filterRequest, String constantKey, String collectionName, String listKey, Map<String, Integer> list, boolean exists) {
        try {
            Aggregation aggregation = getAggregationPipelineForProductCatalog(constantKey, collectionName, listKey, filterRequest, exists);
            @SuppressWarnings("rawtypes")
            AggregationResults<Map> results = mongoTemplate.aggregate(aggregation, Constants.PRODUCT_COLLECTION_NAME, Map.class);
            if (!results.getMappedResults().isEmpty()) {
                Map<String, Integer> resultList = (Map<String, Integer>) results.getMappedResults().get(0).get(listKey);
                list.putAll(resultList);
            }
        } catch (Exception e) {
            throw new DomainException("An error occurred while getting " + listKey + " list for product catalog: ", e.getMessage());
        }
        return list;
    }

    public List<Map<String,Object>> processMapAggregation(FilterRequest filterRequest,String attribute, boolean exists) {
        try{
            List<AggregationOperation> operations = new ArrayList<>();
            if(Objects.nonNull(filterRequest.getQueryString()) && !filterRequest.getQueryString().isEmpty() && !exists)
                operations.addAll(getCriteria(filterRequest));
            else if(!exists) {
                AggregationOperation match = match(
                        Criteria.where(Constants.CATEGORY).in(filterRequest.getCategoryList())
                                .and(Constants.IS_DELETED).is(false)
                                .and(Constants.ACTIVE).is(true)
                );
                operations.add(match);
            }
            else if(exists){
                AggregationOperation match = match(
                        Criteria.where(Constants.CATALOG_ID).in(filterRequest.getCategoryList())
                                .and(Constants.IS_DELETED).is(false)
                                .and(Constants.ACTIVE).is(true)
                );
                operations.add(match);
            }
            AggregationOperation unwind = null;
            if(attribute.equals(Constants.YEAR)) {
                unwind = unwind(Constants.YEAR);
                operations.add(unwind);
            }

            AggregationOperation group = group(Constants.CATEGORY)
                    .addToSet(attribute).as(attribute);
            operations.add(group);

            AggregationOperation project = context -> new Document("$"+Constants.PROJECT, new Document()
                    .append(attribute, new Document(Constants.ARRAY_TO_OBJECT, new Document("$"+Constants.MAP, new Document()
                            .append(Constants.INPUT, "$"+attribute)
                            .append(Constants.AS, Constants.ITEM)
                            .append(Constants.IN, new Document(Constants.KEY, "$$"+Constants.ITEM).append(Constants.VALUE, 0)))))
            );
            operations.add(project);
            Aggregation aggregation;
            aggregation = newAggregation(operations);

            @SuppressWarnings("rawtypes")
            AggregationResults<Map> results = mongoTemplate.aggregate(aggregation, Constants.PRODUCT_COLLECTION_NAME, Map.class);
            return  convertListMap( results.getMappedResults().isEmpty() ? new ArrayList<>() : results.getMappedResults());
        } catch (Exception e) {
            throw new DomainException("An error occurred while process map aggregation: ", e.getMessage());
        }
    }

    public List<Map<String,Object>> processArrayToMapAggregation(FilterRequest filterRequest, String attribute, boolean exists) {
        try{
            List<AggregationOperation> operations = new ArrayList<>();
            if(Objects.nonNull(filterRequest.getQueryString()) && !filterRequest.getQueryString().isEmpty() && !exists)
                operations.addAll(getCriteria(filterRequest));
            else if(!exists){
                AggregationOperation match = match(
                        Criteria.where(Constants.CATEGORY).in(filterRequest.getCategoryList())
                                .and(Constants.IS_DELETED).is(false)
                                .and(Constants.ACTIVE).is(true)
                );
                operations.add(match);
            }
            else if(exists){
                AggregationOperation match = match(
                        Criteria.where(Constants.CATALOG_ID).in(filterRequest.getCategoryList())
                                .and(Constants.IS_DELETED).is(false)
                                .and(Constants.ACTIVE).is(true)
                );
                operations.add(match);
            }

            AggregationOperation group = group(Constants.CATEGORY)
                    .addToSet(attribute).as(attribute);
            operations.add(group);

            AggregationOperation project = context -> new Document("$"+Constants.PROJECT, new Document()
                    .append(attribute, new Document(Constants.ARRAY_TO_OBJECT, new Document("$"+Constants.MAP, new Document()
                            //.append(INPUT, "$"+MODEL)
                            .append(Constants.INPUT, new Document("$filter", new Document()
                                    .append(Constants.INPUT, "$" + attribute)
                                    .append(Constants.AS, Constants.ITEM)
                                    .append("cond", new Document("$and", List.of(
                                                    new Document("$isArray", "$$"+Constants.ITEM),
                                                    new Document("$eq", List.of(new Document("$size", "$$"+Constants.ITEM), 2))
                                            ))
                                    )))
                            .append(Constants.AS, Constants.ITEM)
                            .append(Constants.IN, new Document(Constants.KEY, new Document("$arrayElemAt", List.of("$$"+Constants.ITEM, 0))).append(Constants.VALUE, 0))

                    )))
            );
            operations.add(project);
            Aggregation aggregation = newAggregation(operations);
            @SuppressWarnings("rawtypes")
            AggregationResults<Map> results = mongoTemplate.aggregate(aggregation, Constants.PRODUCT_COLLECTION_NAME, Map.class);
            return  convertListMap( results.getMappedResults().isEmpty() ? new ArrayList<>() : results.getMappedResults());
        } catch (Exception e) {
            throw new DomainException("An error occurred while process array to map aggregation: ", e.getMessage());
        }
    }

    // Utility method to handle array to object transformation
    private Document createArrayToObject(String fieldName) {
        return new Document(Constants.ARRAY_TO_OBJECT, new Document("$" + Constants.MAP, new Document()
                .append(Constants.INPUT, "$" + fieldName)
                .append(Constants.AS, Constants.ITEM)
                .append(Constants.IN, new Document(Constants.KEY, "$$" + Constants.ITEM).append(Constants.VALUE, 0))
        ));
    }

    // Utility method to flatten arrays and convert them into objects
    private Document createFlattenedArrayToObject(String fieldName) {
        return new Document(Constants.ARRAY_TO_OBJECT, new Document("$" + Constants.MAP, new Document()
                .append(Constants.INPUT, new Document("$reduce", new Document()
                        .append(Constants.INPUT, "$" + fieldName)
                        .append(Constants.INITIAL_VALUE, new ArrayList<>())
                        .append(Constants.IN, new Document("$concatArrays", List.of("$$value", "$$this")))))
                .append(Constants.AS, Constants.ITEM)
                .append(Constants.IN, new Document(Constants.KEY, "$$" + Constants.ITEM).append(Constants.VALUE, 0))
        ));
    }

    private Integer getTotalProductCountForProductCatalog(FilterRequest filterRequest, boolean exists) {
        log.info("Inside call to getTotalProductCountForProductCatalog() service");
        int totalProductCount = 0;
        try {
            List<AggregationOperation> operations = new ArrayList<>();
            if(!exists){
                operations = getCriteria(filterRequest);
            }
            else{
                AggregationOperation match = match(
                        Criteria.where(Constants.CATALOG_ID).in(filterRequest.getCategoryList())
                                .and(Constants.IS_DELETED).is(false)
                                .and(Constants.ACTIVE).is(true)
                );
                operations.add(match);
            }
            MatchOperation matchOperation = Aggregation.match(Criteria.where(Constants.CATALOG_TYPE).is(Constants.PRODUCT));
            operations.add(matchOperation);
            Aggregation countStage = newAggregation(
                    Stream.concat(operations.stream(), Stream.of(count().as("totalProductCount")))
                            .toArray(AggregationOperation[]::new)
            );
            @SuppressWarnings("rawtypes")
            AggregationResults<Map> countResult = mongoTemplate.aggregate(countStage, Constants.PRODUCT_COLLECTION_NAME, Map.class);
            if (!countResult.getMappedResults().isEmpty()) {
                Map<String, Object> resultMap = countResult.getMappedResults().get(0);
                totalProductCount = ((Number) resultMap.get("totalProductCount")).intValue();
            }
        } catch (IllegalArgumentException e) {
            throw new DomainException(Constants.INVALID_ARGUMENT_PROVIDED, e.getMessage());
        } catch (Exception e) {
            throw new DomainException("An error occurred while retrieving count of product list by filter", e.getMessage());
        }
        return totalProductCount;
    }
    @SuppressWarnings("rawtypes")
    public static List<Map<String, Object>> convertListMap(List<Map> list) {
        return list.stream()
                .map(map -> (Map<String, Object>) map)
                .toList();
    }

    public List<AggregationOperation> getCriteria(FilterRequest filterRequest) {
        Criteria criteria;
        List<AggregationOperation> operations = new ArrayList<>();
        try{
            if(Objects.nonNull(filterRequest.getQueryString()) && !filterRequest.getQueryString().isEmpty()){

                //atlas search functionality
               /* String db = mongoUri;
                boolean isAtlasSearch = StringUtils.contains(db, "mongodb+srv");
                if(isAtlasSearch){
                    List<Document> list;
                    Document categoryDoc = new Document(Constants.AUTOCOMPLETE,new Document(Constants.QUERY, filterRequest.getQueryString()).append(Constants.PATH, Constants.CATEGORY)
                            .append(Constants.FUZZY, new Document(Constants.MAX_EDITS, 2)).append(Constants.FUZZY,new Document(Constants.PREFIX_LENGTH, 2)));
                    Document subCategoryDoc = new Document(Constants.AUTOCOMPLETE,new Document(Constants.QUERY, filterRequest.getQueryString()).append(Constants.PATH, Constants.SUB_CATEGORY)
                            .append(Constants.FUZZY, new Document(Constants.MAX_EDITS, 2)).append(Constants.FUZZY,new Document(Constants.PREFIX_LENGTH, 2)));
                    Document partNumberDoc = new Document(Constants.AUTOCOMPLETE,new Document(Constants.QUERY, filterRequest.getQueryString()).append(Constants.PATH, Constants.PART_NUMBER)
                            .append(Constants.FUZZY, new Document(Constants.MAX_EDITS, 2)).append(Constants.FUZZY,new Document(Constants.PREFIX_LENGTH, 2)));
                    Document shortDescriptionDoc = new Document(Constants.AUTOCOMPLETE,new Document(Constants.QUERY, filterRequest.getQueryString()).append(Constants.PATH, Constants.DESCRIPTION)
                            .append(Constants.FUZZY, new Document(Constants.MAX_EDITS, 2)).append(Constants.FUZZY,new Document(Constants.PREFIX_LENGTH, 2)));
                    Document nameDoc = new Document(Constants.AUTOCOMPLETE,new Document(Constants.QUERY, filterRequest.getQueryString()).append(Constants.PATH, Constants.NAME)
                            .append(Constants.FUZZY, new Document(Constants.MAX_EDITS, 2)).append(Constants.FUZZY,new Document(Constants.PREFIX_LENGTH, 2)));

                    list = Arrays.asList(categoryDoc,subCategoryDoc,partNumberDoc,nameDoc,shortDescriptionDoc);
                    Document textSearch = new Document("$search", new Document("index", "attribute_search").append("compound",  new Document("should", list)));
                    operations.add(commonServiceImpl.documentToAggregationOperation(textSearch));
                }
                else{
                    //include  criteria regex expression in else if you are using atlas search
                }*/
                    criteria = new Criteria();
                    Pattern pattern = Pattern.compile(filterRequest.getQueryString(), Pattern.CASE_INSENSITIVE);
                    criteria.orOperator(Criteria.where(Constants.CATEGORY).regex(pattern),
                            Criteria.where(Constants.SUB_CATEGORY).regex(pattern),
                            Criteria.where(Constants.PART_NUMBER).regex(pattern),
                            Criteria.where(Constants.DESCRIPTION).regex(pattern),
                            Criteria.where(Constants.NAME).regex(pattern));
                    criteria.andOperator(Criteria.where(Constants.CATALOG_TYPE).is(Constants.PRODUCT));
                    MatchOperation textMatchOperation = Aggregation.match(criteria);
                    operations.add(textMatchOperation);

            }
            MatchOperation matchOperation;

            criteria = Criteria.where(Constants.IS_DELETED).is(false)
                    .and(Constants.ACTIVE).is(true);

            if(!filterRequest.getCategoryList().isEmpty()){
                criteria = criteria.and(Constants.CATEGORY).in(filterRequest.getCategoryList());
            }

            if (!filterRequest.getMakeList().isEmpty()) {
                criteria = criteria.and(Constants.MAKE).in(filterRequest.getMakeList());
            }

            if (!filterRequest.getModelList().isEmpty()) {
                criteria = criteria.and(Constants.MODEL).in(filterRequest.getModelList());
            }

            if (!filterRequest.getYearList().isEmpty()) {
                criteria = criteria.and(Constants.YEAR).in(filterRequest.getYearList());
            }

            if (!filterRequest.getVendorList().isEmpty()) {
                criteria = criteria.and(Constants.VENDOR_NAME).in(filterRequest.getVendorList());
            }

            if (!filterRequest.getBrandList().isEmpty()) {
                criteria = criteria.and(Constants.BRAND_NAME).in(filterRequest.getBrandList());
            }

            if (!filterRequest.getSubCategoryList().isEmpty()) {
                criteria = criteria.and(Constants.SUB_CATEGORY).in(filterRequest.getSubCategoryList());
            }
            matchOperation = Aggregation.match(criteria);
            operations.add(matchOperation);
        }
        catch (Exception e) {
            throw new DomainException("An error occurred while getting criteria for filter product list: ", e.getMessage());
        }
        return operations;
    }

    private Aggregation getAggregationPipelineForProductCatalog(String attribute1, String attribute2, String attribute3,FilterRequest filterRequest, boolean exists) {
        try{
            List<AggregationOperation> matchOperations = new ArrayList<>();
            if(!exists) {
                matchOperations = getCriteria(filterRequest);
            }
            else{
                AggregationOperation match = match(
                        Criteria.where(Constants.CATALOG_ID).in(filterRequest.getCategoryList())
                                .and(Constants.IS_DELETED).is(false)
                                .and(Constants.ACTIVE).is(true)
                );
                matchOperations.add(match);
            }

            List<AggregationOperation> aggregationOperations = new ArrayList<>(matchOperations);
            aggregationOperations.add(unwind(attribute1));
            aggregationOperations.add(group(attribute1).count().as(Constants.COUNT));
            aggregationOperations.add(project()
                    .andExclude(Constants.ID)
                    .and(Constants.ID).as(Constants.KEY)
                    .and(Constants.COUNT).as(Constants.VALUE));
            aggregationOperations.add(group()
                    .push(new BasicDBObject(Constants.KEY, "$" + Constants.KEY).append(Constants.VALUE, "$" + Constants.VALUE)).as(attribute2));
            aggregationOperations.add(project()
                    .andExclude(Constants.ID)
                    .andExpression("{$arrayToObject: '$" + attribute2 + "'}").as(attribute3));

            return newAggregation(aggregationOperations);

        }
        catch (Exception e){
            throw new DomainException("An error occurred while getAggregationPipelineForProductCatalog() {}", e.getMessage());
        }
    }

    public List<ProductResponseHomePage> getProductListByFilter(FilterRequest filterRequest) {
        log.info("Inside call to getProductListByFilter() service");
        List<ProductResponseHomePage> results;
        try {

            Map<String, SortType> sortTypeMap = new HashMap<>();
            sortTypeMap.put("HL", SortType.HIGH_TO_LOW);
            sortTypeMap.put("ASC", SortType.ASC);
            sortTypeMap.put("DESC", SortType.DESC);
            sortTypeMap.put("LH", SortType.LOW_TO_HIGH);

            String sortTypeStr = filterRequest.getSort().toUpperCase();
            SortType sortType = sortTypeMap.get(sortTypeStr);

            List<AggregationOperation> operations = new ArrayList<>();

            operations.addAll(getCriteria(filterRequest));

            MatchOperation matchOperation = Aggregation.match(Criteria.where(Constants.CATALOG_TYPE).is(Constants.PRODUCT));
            operations.add(matchOperation);

            if(AuthorizationUtils.isAuthorizationHeaderValid(request)){
                String customerCorrelationId = AuthorizationUtils.getCustomerCorrelationId(request);
                operations.add(productServiceImpl.getLookupOperation(customerCorrelationId));
                operations.add(productServiceImpl.getAddFieldsOperation());
            }
            int pageSize = filterRequest.getPageSize();
            int pageNumber = filterRequest.getSize() - 1; // Adjust page number to start from 1
            SkipOperation skipOperation = skip((long) pageSize * pageNumber);
            operations.add(skipOperation);

            LimitOperation limitOperation = limit(pageSize);
            operations.add(limitOperation);

            ProjectionOperation projectionOperation = productServiceImpl.getProjectionOperation();
            operations.add(projectionOperation);

            SortOperation sortOperation = null;
            if (sortType.equals(SortType.HIGH_TO_LOW))
                sortOperation = new SortOperation(Sort.by(Sort.Direction.DESC, Constants.PRICE));
            else if(sortType.equals(SortType.LOW_TO_HIGH))
                sortOperation = new SortOperation(Sort.by(Sort.Direction.ASC, Constants.PRICE));
            if(Objects.nonNull(sortOperation))
                operations.add(sortOperation);

            Aggregation aggregation = newAggregation(operations);

            log.info("Aggregation pipeline getProductListByFilter(): {}", aggregation);

            results = mongoTemplate.aggregate(aggregation, Constants.PRODUCT_COLLECTION_NAME, ProductResponseHomePage.class).getMappedResults();


            if(!results.isEmpty()){
                List<ProductResponseHomePage> modifiedResults = commonServiceImpl.resizeImages(results);
                if(sortType.equals(SortType.ASC)){
                    modifiedResults = modifiedResults.stream()
                            .sorted(Comparator.comparing(ProductResponseHomePage::getProductName))
                            .toList();
                }
                else if(sortType.equals(SortType.DESC)) {
                    modifiedResults = modifiedResults.stream()
                            .sorted(Comparator.comparing(ProductResponseHomePage::getProductName).reversed())
                            .toList();
                }
                results = modifiedResults;
            }
            log.info("Aggregation pipeline results getProductListByFilter(): {}", results);

        } catch (IllegalArgumentException e) {
            throw new DomainException(Constants.INVALID_ARGUMENT_PROVIDED, e.getMessage());
        } catch (Exception e) {
            throw new DomainException("An error occurred while retrieving product list by filter", e.getMessage());
        }
        return results;
    }

    @Override
    public List<BrandCategoryResponse> getCategoryListByBrand() {
        log.info("Inside call to getCategoryListByBrand() service");
        AggregationResults<BrandCategoryResponse> results;
        try {
            MatchOperation matchStage = match(
                    Criteria.where(Constants.BRAND_NAME).exists(true)
                            .ne(null)
                            .and(Constants.IS_DELETED).is(false)
                            .and(Constants.ACTIVE).is(true)
            );
            GroupOperation groupStage = group(Constants.BRAND_NAME)
                    .first(Constants.BRAND_ID).as(Constants.BRAND_ID)
                    .first(Constants.BRAND_SLUG).as(Constants.BRAND_SLUG)
                    .addToSet(new BasicDBObject(Constants.CATEGORY, "$"+Constants.CATEGORY).append(Constants.CATEGORY_SLUG, "$"+Constants.CATEGORY_SLUG)).as(Constants.CATEGORIES);
            ProjectionOperation projectStage = project(Constants.CATEGORIES)
                    .and(Constants.ID).as(Constants.BRAND_NAME)
                    .and(Constants.BRAND_ID).as(Constants.BRAND_ID)
                    .and(Constants.BRAND_SLUG).as(Constants.BRAND_SLUG)
                    .andExclude(Constants.ID);
            Aggregation aggregation = newAggregation(matchStage, groupStage, projectStage);

            log.info("Aggregation pipeline getCategoryListByBrand(): {}", aggregation);
            results =mongoTemplate.aggregate(aggregation, Constants.PRODUCT_COLLECTION_NAME, BrandCategoryResponse.class);

            log.info("Aggregation pipeline results getCategoryListByBrand(): {}", results.getMappedResults());
            return results.getMappedResults();
        } catch (IllegalArgumentException e) {
            throw new DomainException(Constants.INVALID_ARGUMENT_PROVIDED, e);
        } catch (Exception e) {
            throw new DomainException("An error occurred while retrieving category list by brand", e);
        }
    }
}
