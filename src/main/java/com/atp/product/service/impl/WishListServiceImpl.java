package com.atp.product.service.impl;

import com.atp.product.controller.dto.request.WishlistRequest;
import com.atp.product.controller.dto.response.ProductResponseHomePage;
import com.atp.product.controller.dto.response.WishlistResponse;
import com.atp.product.model.Wishlist;
import com.atp.product.service.WishListService;
import com.atp.product.utils.Constants;
import com.atp.product.exception.DomainException;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import jakarta.servlet.http.HttpServletRequest;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class WishListServiceImpl extends ApplicationServiceImpl<Wishlist> implements WishListService {
    public static final Logger logger = LoggerFactory.getLogger(WishListServiceImpl.class);
    private final MongoTemplate mongoTemplate;
    private final CommonServiceImpl commonServiceImpl;
    public static final String PRODUCT_DETAILS = "productDetails";
    //public static final String TOTAL_PRODUCT_COUNT = "totalProductCount";

    public WishListServiceImpl(MongoTemplate mongoTemplate, CommonServiceImpl commonServiceImpl) {
        super(mongoTemplate);
        this.mongoTemplate = mongoTemplate;
        this.commonServiceImpl = commonServiceImpl;
    }

    @Override
    public WishlistResponse getWishlist(String customerCorrelationId) {
        logger.info("Inside call to getWishlist() method with customerCorrelationId: {}", customerCorrelationId);
        WishlistResponse results;
        WishlistResponse modifiedResults = new WishlistResponse();
        try {
            Query query = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID).is(customerCorrelationId));
            Optional<Wishlist> optionalWishlist = Optional.ofNullable(mongoTemplate.findOne(query, Wishlist.class));

            if(optionalWishlist.isPresent() && optionalWishlist.get().getProductDetails() != null && !optionalWishlist.get().getProductDetails().isEmpty()){
                MatchOperation match = Aggregation.match(Criteria.where(Constants.CUSTOMER_CORRELATION_ID).is(customerCorrelationId));
                UnwindOperation unwindProductDetails = Aggregation.unwind(PRODUCT_DETAILS);
                LookupOperation lookupProductInfo = Aggregation.lookup(
                        Constants.PRODUCT_COLLECTION_NAME,
                        "productDetails."+Constants.PART_NUMBER,
                        Constants.PART_NUMBER,
                        "productInfo"
                );
                UnwindOperation unwindProductInfo = Aggregation.unwind("productInfo");
                AddFieldsOperation addFields = Aggregation.addFields()
                        .addField("productDetails.productId").withValueOf(Fields.field("productInfo.catalogId"))
                        .addField("productDetails.productName").withValueOf(Fields.field("productInfo.name"))
                        .addField("productDetails.categoryName").withValueOf(Fields.field("productInfo.category"))
                        .addField("productDetails.primaryImage").withValueOf(Fields.field("productInfo.primaryImage"))
                        .addField("productDetails.partNumberSlug").withValueOf(Fields.field("productInfo.partNumberSlug"))
                        .addField("productDetails.assetMap").withValueOf(Fields.field("productInfo.assetMap"))
                        .addField("productDetails.description").withValueOf(Fields.field("productInfo.description"))
                        .build();
                GroupOperation groupByCustomerCorrelationId = Aggregation.group(Constants.CUSTOMER_CORRELATION_ID)
                        .push(PRODUCT_DETAILS).as(PRODUCT_DETAILS);
                ProjectionOperation project = Aggregation.project()
                        .and("_id").as(Constants.CUSTOMER_CORRELATION_ID)
                        .andInclude(PRODUCT_DETAILS)
                        .andExclude("_id");
                Aggregation aggregation = Aggregation.newAggregation(
                        match,
                        unwindProductDetails,
                        lookupProductInfo,
                        unwindProductInfo,
                        addFields,
                        groupByCustomerCorrelationId,
                        project
                );
                logger.info("Aggregation pipeline getWishlist(): {}", aggregation);
                results = mongoTemplate.aggregate(aggregation, Constants.WISHLIST_COLLECTION_NAME, WishlistResponse.class).getUniqueMappedResult();
                modifiedResults = results;
                if (modifiedResults != null && modifiedResults.getProductDetails() != null && !modifiedResults.getProductDetails().isEmpty()) {
                    List<ProductResponseHomePage> modifiedProductDetailsResults = commonServiceImpl.resizeImages(modifiedResults.getProductDetails());
                    modifiedResults.setProductDetails(modifiedProductDetailsResults);
                }
            }
            else{
                modifiedResults.setProductDetails(new ArrayList<>());
                modifiedResults.setCustomerCorrelationId(customerCorrelationId);
            }
        }catch (Exception e){
            throw new DomainException("An error occurred while retrieving wishlist : ", e.getMessage());
        }
        return modifiedResults;
    }
    @Override
    public Integer saveWishlistDetails(WishlistRequest wishlistRequest) {
        logger.info("Inside call to saveWishlistDetails() method with wishlistRequest: {}", wishlistRequest);
        try {
            // Remove product from cart if necessary
            if (wishlistRequest.isMovedWishlist()) {
                Query cartQuery = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID)
                        .is(wishlistRequest.getCustomerCorrelationId())
                        .and("productDetails.partNumber").is(wishlistRequest.getPartNumber()));

                Update cartUpdate = new Update()
                        .pull(PRODUCT_DETAILS, new Query(Criteria.where(Constants.PART_NUMBER).is(wishlistRequest.getPartNumber())));
                        //.inc(TOTAL_PRODUCT_COUNT, -(wishlistRequest.getProductQuantity()));

                UpdateResult cartResult = mongoTemplate.updateFirst(cartQuery, cartUpdate, Constants.SHOPPING_CART_COLLECTION_NAME);
                logger.info("Remove product from the wishlist details: {}", cartResult.getModifiedCount());
            }

            // Add or update wishlist details
            Query wishlistQuery = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID)
                    .is(wishlistRequest.getCustomerCorrelationId()));

            Wishlist wishlist = mongoTemplate.findOne(wishlistQuery, Wishlist.class);

            if (wishlist == null) {
                // Create new wishlist if not present
                Wishlist newWishlist = getNewWishlist(wishlistRequest);
                save(newWishlist);
                return 1;
            }

            // Update existing wishlist
            List<Wishlist.ProductDetail> productDetails = wishlist.getProductDetails();
            Wishlist.ProductDetail productDetail = productDetails.stream()
                    .filter(p -> p.getPartNumber().equals(wishlistRequest.getPartNumber()))
                    .findFirst()
                    .orElseGet(() -> {
                        Wishlist.ProductDetail newProductDetail = new Wishlist.ProductDetail();
                        newProductDetail.setPartNumber(wishlistRequest.getPartNumber());
                        productDetails.add(newProductDetail);
                        return newProductDetail;
                    });
            Update update = new Update().set(PRODUCT_DETAILS, productDetails);
            UpdateResult updateResult = mongoTemplate.updateFirst(wishlistQuery, update, Wishlist.class);
            logger.info("Updated wishlist: {}", updateResult.getModifiedCount());
        } catch (Exception e) {
            throw new DomainException("An error occurred while saving wishlist list", e);
        }
        return 1;
    }
    /*@Override
    public Integer saveWishlistDetails(WishlistRequest wishlistRequest) {
        logger.info("Inside call to saveWishlistDetails() method with wishlistRequest: {}", wishlistRequest);
        try {
            // Remove product from cart if necessary
            if (wishlistRequest.isMovedWishlist()) {
                Query cartQuery = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID)
                        .is(wishlistRequest.getCustomerCorrelationId())
                        .and("productDetails.partNumber").is(wishlistRequest.getPartNumber()));

                Update cartUpdate = new Update()
                        .pull(PRODUCT_DETAILS, new Query(Criteria.where(Constants.PART_NUMBER).is(wishlistRequest.getPartNumber())));
                //.inc(TOTAL_PRODUCT_COUNT, -(wishlistRequest.getProductQuantity()));

                UpdateResult cartResult = mongoTemplate.updateFirst(cartQuery, cartUpdate, Constants.SHOPPING_CART_COLLECTION_NAME);
                logger.info("Remove product from the wishlist details: {}", cartResult.getModifiedCount());
            }

            // Add or update wishlist details
            Query wishlistQuery = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID)
                    .is(wishlistRequest.getCustomerCorrelationId()));

            Wishlist wishlist = mongoTemplate.findOne(wishlistQuery, Wishlist.class);

            if (wishlist == null) {
                // Create new wishlist if not present
                Wishlist newWishlist = getNewWishlist(wishlistRequest);
                save(newWishlist);
                return 1;
            }

            // Check if the product already exists in the wishlist
            Query productQuery = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID)
                    .is(wishlistRequest.getCustomerCorrelationId())
                    .and("productDetails.partNumber").is(wishlistRequest.getPartNumber()));

            long count = mongoTemplate.count(productQuery, Wishlist.class);

            // If product exists, just return 1
            if (count > 0) {
                logger.info("Product exists in wishlist - no further action needed");
                return 1; // Indicate that the product is already in the wishlist
            }

            // If product does not exist, add it to the wishlist
            logger.info("Product does not exist in wishlist - adding new product");

            Wishlist.ProductDetail newProductDetail = new Wishlist.ProductDetail();
            newProductDetail.setPartNumber(wishlistRequest.getPartNumber());
            // No need to set product quantity

            Update addToWishlist = new Update().push(PRODUCT_DETAILS, newProductDetail);
            UpdateResult updateResult = mongoTemplate.updateFirst(wishlistQuery, addToWishlist, Wishlist.class);
            logger.info("Updated wishlist: {}", updateResult.getModifiedCount());
        } catch (Exception e) {
            throw new DomainException("An error occurred while saving wishlist list", e);
        }
        return 1;
    }*/


    @NotNull
    private static Wishlist getNewWishlist(WishlistRequest wishlistRequest) {
        Wishlist newWishlist = new Wishlist();
        newWishlist.setCustomerCorrelationId(wishlistRequest.getCustomerCorrelationId());
        Wishlist.ProductDetail newDetail = new Wishlist.ProductDetail();
        newDetail.setPartNumber(wishlistRequest.getPartNumber());
        newWishlist.setProductDetails(List.of(newDetail));
        return newWishlist;
    }

    @Override
    public boolean updateWishlistDetails(WishlistRequest wishlistRequest) {
        logger.info("Inside call to updateWishlistDetails() method with wishlistRequest: {}", wishlistRequest);
        try {
            Query query = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID).is(wishlistRequest.getCustomerCorrelationId()));
            Optional<Wishlist> optionalCart = Optional.ofNullable(mongoTemplate.findOne(query, Wishlist.class));
            logger.info("Case 1: Customer correlation ID exists - update or add product details");
            return optionalCart.map(existingWishlistDetails -> {
                List<Wishlist.ProductDetail> existingProductDetails = existingWishlistDetails.getProductDetails();

                Optional<Wishlist.ProductDetail> optionalProduct = existingProductDetails.stream()
                        .filter(p -> p.getPartNumber().equals(wishlistRequest.getPartNumber()))
                        .findFirst();

                if (optionalProduct.isPresent()) {
                    logger.info("Case 2: Product details exist - update quantity");
                    Update update = new Update().set(PRODUCT_DETAILS, existingProductDetails);
                    UpdateResult updateResult = mongoTemplate.updateFirst(query, update, Wishlist.class);

                    return updateResult.getModifiedCount() > 0;
                } else {
                    logger.info("Product details not found");
                    return false;
                }
            }).orElseGet(() -> {
                logger.info("Wishlist details not found");
                return false;
            });
        } catch (Exception e) {
            throw new DomainException("An error occurred while saving wishlist : ", e.getMessage());
        }
    }
    @Override
    public boolean deleteWishlistByCustomerCorrelationId(String customerCorrelationId) {
        logger.info("Inside call to deleteWishlistByCustomerCorrelationId() with customerCorrelationId: {}", customerCorrelationId);
        try {
            return Optional.ofNullable(customerCorrelationId)
                    .map(id -> {
                        Query query = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID).is(id));
                        DeleteResult result = mongoTemplate.remove(query, Constants.WISHLIST_COLLECTION_NAME);
                        return result.getDeletedCount() > 0;
                    })
                    .orElse(false);
        } catch (Exception e) {
            throw new DomainException("An error occurred while deleting wishlist : ", e.getMessage());
        }
    }

    @Override
    public boolean deleteWishlistDetailsByPartNumber(WishlistRequest wishlistRequest) {
        logger.info("Inside call to deleteWishlistDetailsByPartNumber() method with wishlistRequest: {}", wishlistRequest);
        try {
            return Optional.ofNullable(wishlistRequest)
                    .map(request -> {
                        Query query = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID).is(request.getCustomerCorrelationId())
                                .and("productDetails.partNumber").is(request.getPartNumber()));

                        Update update = new Update()
                                .pull(PRODUCT_DETAILS, new Query(Criteria.where(Constants.PART_NUMBER).is(request.getPartNumber())));
                                //.inc(TOTAL_PRODUCT_COUNT, -1);
                        UpdateResult result = mongoTemplate.updateFirst(query, update, Constants.WISHLIST_COLLECTION_NAME);
                        logger.info("Deleted wishlist {}", result.getModifiedCount());
                        return result.getMatchedCount() > 0;
                    })
                    .orElse(false);
        } catch (Exception e) {
            throw new DomainException("An error occurred while deleting wishlist : ", e.getMessage());
        }
    }
}
