package com.atp.product.service.impl;

import com.atp.product.service.ApplicationService;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import org.springframework.data.mongodb.core.DocumentCallbackHandler;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ApplicationServiceImpl<T> implements ApplicationService<T> {

    private final MongoOperations mongoTemplate;
    protected String collectionName;
    public ApplicationServiceImpl(MongoOperations mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    protected MongoOperations getMongoTemplate() {
        return mongoTemplate;
    }

    @Override
    public T findOne(Query query, Class<T> entityClass) {
        return getMongoTemplate().findOne(query, entityClass, collectionName);
    }

    @Override
    public List<T> find(Query query, Class<?> entityClass) {
        return (List<T>) getMongoTemplate().find(query, entityClass, collectionName);
    }

    @Override
    public long count(Query query) {
        return getMongoTemplate().count(query, collectionName);
    }

    @Override
    public long count(Query query, Class<?> entityClass) {
        return getMongoTemplate().count(query, entityClass, collectionName);
    }

    @Override
    public DeleteResult remove(T entity) {
        return getMongoTemplate().remove(entity, collectionName);
    }

    @Override
    public void save(T objectToSave) {
        getMongoTemplate().save(objectToSave);
    }

    @Override
    public void save(T objectToSave, String collectionName) {
        getMongoTemplate().save(objectToSave, collectionName);
    }

    @Override
    public void executeQuery(Query query, String collectionName, DocumentCallbackHandler dch) {
        getMongoTemplate().executeQuery(query, collectionName, dch);
    }

    @Override
    public UpdateResult updateMulti(Query query, Update update, Class<?> entityClass) {
        return getMongoTemplate().updateMulti(query, update, collectionName);
    }

    @Override
    public UpdateResult updateFirst(Query query, Update update, Class<?> entityClass) {
        return getMongoTemplate().updateFirst(query, update, collectionName);
    }

}
