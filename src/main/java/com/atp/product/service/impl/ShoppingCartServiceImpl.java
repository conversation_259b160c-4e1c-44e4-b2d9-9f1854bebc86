package com.atp.product.service.impl;

import com.atp.product.controller.dto.request.ShoppingCartRequest;
import com.atp.product.controller.dto.response.ProductResponseHomePage;
import com.atp.product.controller.dto.response.ShoppingCartListResponse;
import com.atp.product.model.ShoppingCart;
import com.atp.product.service.ShoppingCartService;
import com.atp.product.utils.Constants;
import com.atp.product.exception.DomainException;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import jakarta.servlet.http.HttpServletRequest;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class ShoppingCartServiceImpl extends ApplicationServiceImpl<ShoppingCart> implements ShoppingCartService {

    public static final Logger logger = LoggerFactory.getLogger(ShoppingCartServiceImpl.class);
    private final MongoTemplate mongoTemplate;
    private final CommonServiceImpl commonServiceImpl;

    public static final String PRODUCT_DETAILS = "productDetails";
    public static final String TOTAL_PRODUCT_COUNT = "totalProductCount";

    public ShoppingCartServiceImpl(MongoTemplate mongoTemplate, CommonServiceImpl commonServiceImpl) {
        super(mongoTemplate);
        this.mongoTemplate = mongoTemplate;
        this.commonServiceImpl = commonServiceImpl;
    }

    @Override
    public ShoppingCartListResponse getShoppingCartList(String customerCorrelationId) {
        logger.info("Inside call to getShoppingCartList() method with customerCorrelationId: {}", customerCorrelationId);
        ShoppingCartListResponse results = new ShoppingCartListResponse();
        ShoppingCartListResponse modifiedResults = new ShoppingCartListResponse();
        try {
            Query query = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID).is(customerCorrelationId));
            Optional<ShoppingCart> optionalCart = Optional.ofNullable(mongoTemplate.findOne(query, ShoppingCart.class));

            if(optionalCart.isPresent() && optionalCart.get().getProductDetails() != null && !optionalCart.get().getProductDetails().isEmpty()){
                MatchOperation match = Aggregation.match(Criteria.where(Constants.CUSTOMER_CORRELATION_ID).is(customerCorrelationId));
                UnwindOperation unwindProductDetails = Aggregation.unwind(PRODUCT_DETAILS);
                LookupOperation lookupProductInfo = Aggregation.lookup(
                        Constants.PRODUCT_COLLECTION_NAME,
                        "productDetails."+Constants.PART_NUMBER,
                        Constants.PART_NUMBER,
                        "productInfo"
                );
                UnwindOperation unwindProductInfo = Aggregation.unwind("productInfo");
                AddFieldsOperation addFields = Aggregation.addFields()
                        .addField("productDetails.productId").withValueOf(Fields.field("productInfo.catalogId"))
                        .addField("productDetails.productName").withValueOf(Fields.field("productInfo.name"))
                        .addField("productDetails.categoryName").withValueOf(Fields.field("productInfo.category"))
                        .addField("productDetails.primaryImage").withValueOf(Fields.field("productInfo.primaryImage"))
                        .addField("productDetails.partNumberSlug").withValueOf(Fields.field("productInfo.partNumberSlug"))
                        .addField("productDetails.assetMap").withValueOf(Fields.field("productInfo.assetMap"))
                        .addField("productDetails.description").withValueOf(Fields.field("productInfo.description"))
                        .addField("productDetails.productQuantity").withValueOf(Fields.field("productDetails.productQuantity"))
                        .addField("productDetails.vendorName").withValueOf(Fields.field("productInfo.vendorName"))
                        .build();
                GroupOperation groupByCustomerCorrelationId = Aggregation.group(Constants.CUSTOMER_CORRELATION_ID)
                        .push(PRODUCT_DETAILS).as(PRODUCT_DETAILS);
                ProjectionOperation project = Aggregation.project()
                        .and("_id").as(Constants.CUSTOMER_CORRELATION_ID)
                        .andInclude(PRODUCT_DETAILS)
                        .andExclude("_id");
                Aggregation aggregation = Aggregation.newAggregation(
                        match,
                        unwindProductDetails,
                        lookupProductInfo,
                        unwindProductInfo,
                        addFields,
                        groupByCustomerCorrelationId,
                        project
                );
                logger.info("Aggregation pipeline getShoppingCartList(): {}", aggregation);
                results = mongoTemplate.aggregate(aggregation, Constants.SHOPPING_CART_COLLECTION_NAME, ShoppingCartListResponse.class).getUniqueMappedResult();
                modifiedResults = results;
                if (modifiedResults != null && modifiedResults.getProductDetails() != null && !modifiedResults.getProductDetails().isEmpty()) {
                    List<ProductResponseHomePage> modifiedProductDetailsResults = commonServiceImpl.resizeImages(modifiedResults.getProductDetails());
                    modifiedResults.setProductDetails(modifiedProductDetailsResults);
                    modifiedResults.setTotalProductCount(calculateTotalProductCountForList(modifiedProductDetailsResults));
                    modifiedResults.setGrandTotal(calculateGrandTotal(modifiedProductDetailsResults));
                }
            }
            else{
                modifiedResults.setGrandTotal(0.0);
                modifiedResults.setProductDetails(new ArrayList<>());
                modifiedResults.setCustomerCorrelationId(customerCorrelationId);
                modifiedResults.setTotalProductCount(0);
            }
        }catch (Exception e){
            throw new DomainException("An error occurred while retrieving shopping cart list : ", e.getMessage());
        }
        return modifiedResults;
    }

    private double calculateGrandTotal(List<ProductResponseHomePage> modifiedProductDetailsResults) {
        return modifiedProductDetailsResults.stream()
                .mapToDouble(ProductResponseHomePage::getRowTotal)
                .sum();
    }
    private int calculateTotalProductCountForList(List<ProductResponseHomePage> modifiedProductDetailsResults) {
        return modifiedProductDetailsResults.stream()
                .mapToInt(ProductResponseHomePage::getProductQuantity)
                .sum();
    }

   /* @Override
    public Integer saveShoppingCartDetails(ShoppingCartRequest shoppingCartRequest) {
        logger.info("Inside call to saveShoppingCartDetails() method with shoppingCartRequest: {}", shoppingCartRequest);

        try {
            if (shoppingCartRequest.isMovedToCart()) {
                Query wishlistQuery = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID)
                        .is(shoppingCartRequest.getCustomerCorrelationId())
                        .and("productDetails.partNumber").is(shoppingCartRequest.getPartNumber()));

                Update removeFromWishlist = new Update().pull(PRODUCT_DETAILS,
                        new Query(Criteria.where(Constants.PART_NUMBER).is(shoppingCartRequest.getPartNumber())));

                UpdateResult result = mongoTemplate.updateFirst(wishlistQuery, removeFromWishlist, Constants.WISHLIST_COLLECTION_NAME);
                logger.info("Removed product from wishlist, modified count: {}", result.getModifiedCount());
            }

            Query cartQuery = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID).is(shoppingCartRequest.getCustomerCorrelationId()));

            // Check if the cart exists
            ShoppingCart cart = mongoTemplate.findOne(cartQuery, ShoppingCart.class);

            if (cart == null) {
                logger.info("Customer correlation ID not found in the collection - creating a new cart");

                // If no cart exists, create a new one
                ShoppingCart newCart = getShoppingCart(shoppingCartRequest);
                save(newCart);

                return calculateTotalProductCount(newCart.getProductDetails());
            } else {
                // Check if the product already exists in the cart
                Query productQuery = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID)
                        .is(shoppingCartRequest.getCustomerCorrelationId())
                        .and("productDetails.partNumber").is(shoppingCartRequest.getPartNumber()));

                long count = mongoTemplate.count(productQuery, ShoppingCart.class);

                if (count > 0) {
                    logger.info("Product exists in cart - updating quantity");
                    // Increment the product quantity using $inc
                    Update updateQuantity = new Update().inc("productDetails.$.productQuantity", shoppingCartRequest.getProductQuantity());
                    mongoTemplate.updateFirst(productQuery, updateQuantity, ShoppingCart.class);
                } else {
                    logger.info("Product does not exist in cart - adding new product");

                    // Product does not exist, add it to the cart using $push
                    ShoppingCart.ProductDetail newDetail = new ShoppingCart.ProductDetail();
                    newDetail.setPartNumber(shoppingCartRequest.getPartNumber());
                    newDetail.setProductQuantity(shoppingCartRequest.getProductQuantity());

                    Update addToCart = new Update().push("productDetails", newDetail);
                    mongoTemplate.updateFirst(cartQuery, addToCart, ShoppingCart.class);
                }
                // Re-fetch the updated cart from the database to get the latest data
                ShoppingCart updatedCart = mongoTemplate.findOne(cartQuery, ShoppingCart.class);
                // Calculate the total product count from the updated cart
                return calculateTotalProductCount(updatedCart.getProductDetails());
            }
        } catch (Exception e) {
            throw new DomainException("An error occurred while saving the shopping cart details: ", e);
        }
    }*/
   @Override
   public Integer saveShoppingCartDetails(ShoppingCartRequest shoppingCartRequest) {
       logger.info("Inside call to saveShoppingCartDetails() method with shoppingCartRequest: {}", shoppingCartRequest);
       //Map<String, String> priceAndQuantityForProduct = commonServiceImpl.getPriceAndQuantityForProduct(shoppingCartRequest.getPartNumber());
       Map<String, String> priceAndQuantityForProduct = commonServiceImpl.getPriceAndQuantityForProductAsync(shoppingCartRequest.getPartNumber()).join();
       int karmakProductQuantity = 0;
       if(Objects.nonNull(priceAndQuantityForProduct) && priceAndQuantityForProduct.size() > 0){
           String quantityString = priceAndQuantityForProduct.get(Constants.QUANTITY);
           karmakProductQuantity = Integer.parseInt(quantityString);
       }
       if (karmakProductQuantity < shoppingCartRequest.getProductQuantity()) {
           throw new DomainException("For inventory inquires, please contact the Bollingbrook location at 815-306-6000 or our Rockdale location at 815-744-7800.");
       }
       //Check product is available in wishlist or not if it is available then delete product from wishlist
       if(shoppingCartRequest.isMovedToCart()){
           Query query = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID).is(shoppingCartRequest.getCustomerCorrelationId())
                   .and("productDetails.partNumber").is(shoppingCartRequest.getPartNumber()));
           Update update = new Update().pull(PRODUCT_DETAILS, new Query(Criteria.where(Constants.PART_NUMBER)
                   .is(shoppingCartRequest.getPartNumber())));
           UpdateResult result = mongoTemplate.updateFirst(query, update, Constants.WISHLIST_COLLECTION_NAME);
           logger.info("Remove product from the wishlist details {}",result.getModifiedCount());
       }
       //Start business logic for save shopping cart details
       Query query = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID).is(shoppingCartRequest.getCustomerCorrelationId()));
       ShoppingCart cart = mongoTemplate.findOne(query, ShoppingCart.class);
       logger.info("Case 1: Customer correlation ID not in the collection - create new cart");

       if(cart == null){
           ShoppingCart newCart = getShoppingCart(shoppingCartRequest);
           save(newCart);
           return calculateTotalProductCount(newCart.getProductDetails());
       }
       else{
           List<ShoppingCart.ProductDetail> productDetails = cart.getProductDetails();
           Optional<ShoppingCart.ProductDetail> optionalProduct = productDetails.stream()
                   .filter(p -> p.getPartNumber().equals(shoppingCartRequest.getPartNumber()))
                   .findFirst();

           if (optionalProduct.isPresent()) {
               logger.info("Case 3: Product details exist - update quantity");
               ShoppingCart.ProductDetail productDetail = optionalProduct.get();
               productDetail.setProductQuantity(productDetail.getProductQuantity() + shoppingCartRequest.getProductQuantity());
           } else {
               logger.info("Case 4: Product details do not exist - create new");
               ShoppingCart.ProductDetail newDetail = new ShoppingCart.ProductDetail();
               newDetail.setPartNumber(shoppingCartRequest.getPartNumber());
               newDetail.setProductQuantity(shoppingCartRequest.getProductQuantity());

               productDetails.add(newDetail);
               //cart.setTotalProductCount(cart.getTotalProductCount() + 1);
           }
           int totalProductCount = calculateTotalProductCount(cart.getProductDetails());

           if (karmakProductQuantity < totalProductCount) {
               throw new DomainException("For inventory inquires, please contact the Bollingbrook location at 815-306-6000 or our Rockdale location at 815-744-7800.");
           }

           Update update = new Update()
                   .set(PRODUCT_DETAILS, productDetails);
           //.set(TOTAL_PRODUCT_COUNT, totalProductCount);

           UpdateResult updateResult = mongoTemplate.updateFirst(query, update, ShoppingCart.class);
           logger.info("Updated shopping cart : {}", updateResult.getModifiedCount());


           return totalProductCount;
       }
   }

    @NotNull
    private static ShoppingCart getShoppingCart(ShoppingCartRequest shoppingCartRequest) {
        ShoppingCart newCart = new ShoppingCart();
        newCart.setCustomerCorrelationId(shoppingCartRequest.getCustomerCorrelationId());

        ShoppingCart.ProductDetail newDetail = new ShoppingCart.ProductDetail();
        newDetail.setPartNumber(shoppingCartRequest.getPartNumber());
        newDetail.setProductQuantity(shoppingCartRequest.getProductQuantity());

        newCart.setProductDetails(List.of(newDetail));
        //newCart.setTotalProductCount(shoppingCartRequest.getProductQuantity());
        return newCart;
    }

    @Override
    public boolean updateShoppingCartDetails(ShoppingCartRequest shoppingCartRequest) {
        logger.info("Inside call to updateShoppingCartDetails() method with shoppingCartRequest: {}", shoppingCartRequest);

        //Map<String, String> priceAndQuantityForProduct = commonServiceImpl.getPriceAndQuantityForProduct(shoppingCartRequest.getPartNumber());
        Map<String, String> priceAndQuantityForProduct = commonServiceImpl.getPriceAndQuantityForProductAsync(shoppingCartRequest.getPartNumber()).join();
        int karmakProductQuantity = 0;
        if(Objects.nonNull(priceAndQuantityForProduct) && priceAndQuantityForProduct.size() > 0) {
            String quantityString = priceAndQuantityForProduct.get(Constants.QUANTITY);
            karmakProductQuantity = Integer.parseInt(quantityString);
        }
        if (karmakProductQuantity < shoppingCartRequest.getProductQuantity()) {
            throw new DomainException("For inventory inquires, please contact the Bollingbrook location at 815-306-6000 or our Rockdale location at 815-744-7800.");
        }

        Query query = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID).is(shoppingCartRequest.getCustomerCorrelationId()));
        Optional<ShoppingCart> optionalCart = Optional.ofNullable(mongoTemplate.findOne(query, ShoppingCart.class));
        logger.info("Case 1: Customer correlation ID exists - update or add product details");
        return optionalCart.map(existingCartDetails -> {
            List<ShoppingCart.ProductDetail> existingProductDetails = existingCartDetails.getProductDetails();

            Optional<ShoppingCart.ProductDetail> optionalProduct = existingProductDetails.stream()
                    .filter(p -> p.getPartNumber().equals(shoppingCartRequest.getPartNumber()))
                    .findFirst();

            if (optionalProduct.isPresent()) {
                logger.info("Case 2: Product details exist - update quantity");
                ShoppingCart.ProductDetail productDetail = optionalProduct.get();
                productDetail.setProductQuantity(shoppingCartRequest.getProductQuantity());

                Update update = new Update()
                        .set(PRODUCT_DETAILS, existingProductDetails);
                // .set(TOTAL_PRODUCT_COUNT, calculateTotalProductCount(existingCartDetails.getProductDetails()));

                UpdateResult updateResult = mongoTemplate.updateFirst(query, update, ShoppingCart.class);

                return updateResult.getModifiedCount() > 0;
            } else {
                logger.info("Product details not found");
                return false;
            }
        }).orElseGet(() -> {
            logger.info("Shopping cart details not found");
            return false;
        });
    }
   /*@Override
   public boolean updateShoppingCartDetails(ShoppingCartRequest shoppingCartRequest) {
       logger.info("Inside call to updateShoppingCartDetails() method with shoppingCartRequest: {}", shoppingCartRequest);
       try {
           Query query = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID).is(shoppingCartRequest.getCustomerCorrelationId()));
           Optional<ShoppingCart> optionalCart = Optional.ofNullable(mongoTemplate.findOne(query, ShoppingCart.class));
           logger.info("Case 1: Customer correlation ID exists - update or add product details");
           return optionalCart.map(existingCartDetails -> {
               Query productQuery = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID)
                       .is(shoppingCartRequest.getCustomerCorrelationId())
                       .and("productDetails.partNumber").is(shoppingCartRequest.getPartNumber()));

               // Check if the product exists in the cart using count
               long count = mongoTemplate.count(productQuery, ShoppingCart.class);

               if (count > 0) {
                   logger.info("Product exists in cart - updating quantity");

                   // Update the product quantity using $set for the product detail
                   Update updateQuantity = new Update().set("productDetails.$.productQuantity", shoppingCartRequest.getProductQuantity());
                   UpdateResult updateResult = mongoTemplate.updateFirst(productQuery, updateQuantity, ShoppingCart.class);

                   return updateResult.getModifiedCount() > 0;
               } else {
                   logger.info("Product details not found");
                   return false;
               }
           }).orElseGet(() -> {
               logger.info("Shopping cart details not found");
               return false;
           });
       } catch (Exception e) {
           throw new DomainException("An error occurred while saving shopping cart list : ", e.getMessage());
       }
   }*/
    private int calculateTotalProductCount(List<ShoppingCart.ProductDetail> modifiedProductDetails) {
        return modifiedProductDetails.stream()
                .mapToInt(ShoppingCart.ProductDetail::getProductQuantity)
                .sum();
    }
    @Override
    public boolean deleteShoppingCartByCustomerCorrelationId(String customerCorrelationId) {
        logger.info("Inside call to deleteShoppingCartByCustomerCorrelationId() with customerCorrelationId: {}", customerCorrelationId);
        try {
            return Optional.ofNullable(customerCorrelationId)
                    .map(id -> {
                        Query query = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID).is(id));
                        DeleteResult result = mongoTemplate.remove(query, Constants.SHOPPING_CART_COLLECTION_NAME);
                        return result.getDeletedCount() > 0;
                    })
                    .orElse(false);
        } catch (Exception e) {
            throw new DomainException("An error occurred while deleting shopping cart list : ", e.getMessage());
        }
    }

    @Override
    public boolean deleteShoppingCartDetailsByPartNumber(ShoppingCartRequest shoppingCartRequest) {
        logger.info("Inside call to deleteShoppingCartDetailsByPartNumber() method with shoppingCartRequest: {}", shoppingCartRequest);
        try {
            return Optional.ofNullable(shoppingCartRequest)
                    .map(request -> {
                        Query query = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID).is(request.getCustomerCorrelationId())
                                .and("productDetails.partNumber").is(request.getPartNumber()));

                        Update update = new Update()
                                .pull(PRODUCT_DETAILS, new Query(Criteria.where(Constants.PART_NUMBER).is(request.getPartNumber())));
                                //.inc(TOTAL_PRODUCT_COUNT, -(request.getProductQuantity()));
                        UpdateResult result = mongoTemplate.updateFirst(query, update, Constants.SHOPPING_CART_COLLECTION_NAME);
                        logger.info("Deleted shopping cart {}", result.getModifiedCount());
                        return result.getMatchedCount() > 0;
                    })
                    .orElse(false);
        } catch (Exception e) {
            throw new DomainException("An error occurred while deleting shopping cart : ", e.getMessage());
        }
    }

    @Override
    public Integer getTotalProductCount(String customerCorrelationId) {
        logger.info("Inside call to getTotalProductCount() method with customerCorrelationId: {}", customerCorrelationId);
        try{
            Query query = new Query(Criteria.where(Constants.CUSTOMER_CORRELATION_ID).is(customerCorrelationId));
            ShoppingCart existingCartDetails = mongoTemplate.findOne(query, ShoppingCart.class);
            if(Objects.nonNull(existingCartDetails) &&  Objects.nonNull(existingCartDetails.getProductDetails()))
                return calculateTotalProductCount(existingCartDetails.getProductDetails());
            else
                return 0;
        } catch (Exception e) {
            throw new DomainException("An error occurred while getting total product count in shopping cart : ",e.getMessage());
        }
    }
}
