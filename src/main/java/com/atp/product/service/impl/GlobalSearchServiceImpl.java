package com.atp.product.service.impl;

import com.atp.product.controller.dto.response.*;
import com.atp.product.service.GlobalSearchService;
import com.atp.product.utils.Constants;
import com.atp.product.exception.DomainException;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Pattern;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;

@Service
public class GlobalSearchServiceImpl implements GlobalSearchService {

    @Value("${spring.data.mongodb.uri}")
    private String mongoUri;

    public static final Logger logger = LoggerFactory.getLogger(GlobalSearchServiceImpl.class);
    private final MongoTemplate mongoTemplate;
    private final CommonServiceImpl commonServiceImpl;

    public GlobalSearchServiceImpl(MongoTemplate mongoTemplate, CommonServiceImpl commonServiceImpl) {
        this.mongoTemplate = mongoTemplate;
        this.commonServiceImpl = commonServiceImpl;
    }

    @Override
    public List<GlobalSearchResponse> typeAheadSearch(String queryString, String category) {
        logger.info("Inside call to typeAheadSearch() service with queryString: {}", queryString);
        List<GlobalSearchResponse> results;
        try{
            List<AggregationOperation> operations = getCriteria(category,queryString);

            ProjectionOperation projectionOperation = project()
                    .and(ConditionalOperators.ifNull(Constants.SUB_CATEGORY).then(""))
                    .as(Constants.SUB_CATEGORY)
                    .and(ConditionalOperators.ifNull(Constants.SUB_CATEGORY_SLUG).then(""))
                    .as(Constants.SUB_CATEGORY_SLUG)
                    .and(ConditionalOperators.ifNull(Constants.CATEGORY).then(""))
                    .as(Constants.CATEGORY)
                    .and(ConditionalOperators.ifNull(Constants.CATEGORY_SLUG).then(""))
                    .as(Constants.CATEGORY_SLUG)
                    .and(ConditionalOperators.ifNull(Constants.PART_NUMBER).then(""))
                    .as(Constants.PART_NUMBER)
                    .and(ConditionalOperators.ifNull(Constants.PART_NUMBER_SLUG).then(""))
                    .as(Constants.PART_NUMBER_SLUG)
                    .and(ConditionalOperators.ifNull(Constants.NAME).then(""))
                    .as(Constants.NAME)
                    .and(ConditionalOperators.ifNull(Constants.DESCRIPTION).then(""))
                    .as(Constants.DESCRIPTION)
                    .andExclude(Constants.ID);
            operations.add(projectionOperation);

            operations.add(Aggregation.limit(Constants.MAX_TYPE_AHEAD_RESULTS));
            Aggregation aggregation = newAggregation(operations);
            results = mongoTemplate.aggregate(aggregation, Constants.PRODUCT_COLLECTION_NAME, GlobalSearchResponse.class).getMappedResults();
        } catch (Exception e) {
            throw new DomainException("An error occurred while getting searchText: ", e.getMessage());
        }
        return results;
    }

    public List<AggregationOperation> getCriteria(String category, String queryString) {
        List<AggregationOperation> operations = new ArrayList<>();
        try{
            /*String db = mongoUri;
            boolean isAtlasSearch = StringUtils.contains(db, "mongodb+srv");
            if(!isAtlasSearch){
                List<Document> list;

                //First Approach-Exact Match Approach in Atlas Search (Used in Atomm Project)
                Document categoryDoc = new Document("text", new Document("query", queryString).append("path", Constants.CATEGORY));
                Document subCategoryDoc = new Document("text", new Document("query", queryString).append("path", Constants.SUB_CATEGORY));
                Document partNumberDoc = new Document("text", new Document("query", queryString).append("path", Constants.PART_NUMBER));
                Document descriptionDoc = new Document("text", new Document("query", queryString).append("path", Constants.DESCRIPTION));
                Document nameDoc = new Document("text", new Document("query", queryString).append("path", Constants.NAME));

                list = Arrays.asList(categoryDoc, subCategoryDoc, partNumberDoc, descriptionDoc, nameDoc);

                // Aggregation with $search operator using text search
                Document textSearch = new Document("$search", new Document("index", "part_search")
                        .append("compound", new Document("should", list)));


             // Second Approach -Fuzzy Matching Approach in Atlas Search
                *//*Document categoryDoc = new Document(Constants.AUTOCOMPLETE,new Document(Constants.QUERY, queryString).append(Constants.PATH, Constants.CATEGORY)
                        .append(Constants.FUZZY, new Document(Constants.MAX_EDITS, 2)).append(Constants.FUZZY,new Document(Constants.PREFIX_LENGTH, 2)));
                Document subCategoryDoc = new Document(Constants.AUTOCOMPLETE,new Document(Constants.QUERY, queryString).append(Constants.PATH, Constants.SUB_CATEGORY)
                        .append(Constants.FUZZY, new Document(Constants.MAX_EDITS, 2)).append(Constants.FUZZY,new Document(Constants.PREFIX_LENGTH, 2)));
                Document partNumberDoc = new Document(Constants.AUTOCOMPLETE,new Document(Constants.QUERY, queryString).append(Constants.PATH, Constants.PART_NUMBER)
                        .append(Constants.FUZZY, new Document(Constants.MAX_EDITS, 2)).append(Constants.FUZZY,new Document(Constants.PREFIX_LENGTH, 2)));
                Document shortDescriptionDoc = new Document(Constants.AUTOCOMPLETE,new Document(Constants.QUERY, queryString).append(Constants.PATH, Constants.DESCRIPTION)
                        .append(Constants.FUZZY, new Document(Constants.MAX_EDITS, 2)).append(Constants.FUZZY,new Document(Constants.PREFIX_LENGTH, 2)));
                Document nameDoc = new Document(Constants.AUTOCOMPLETE,new Document(Constants.QUERY, queryString).append(Constants.PATH, Constants.NAME)
                        .append(Constants.FUZZY, new Document(Constants.MAX_EDITS, 2)).append(Constants.FUZZY,new Document(Constants.PREFIX_LENGTH, 2)));

                list = Arrays.asList(categoryDoc,subCategoryDoc,partNumberDoc,shortDescriptionDoc,nameDoc);

                Document textSearch = new Document("$search", new Document("index", "attribute_search").append("compound",  new Document("should", list)));*//*

                operations.add(commonServiceImpl.documentToAggregationOperation(textSearch));
            }
            else{
                //if we are  using Atlas search then regex approach should be in else
            }*/

                //Third Approach-Regex-Based Partial Matching (Without atlas search)
                //as discussed with Michael using this approach for searching products
                Criteria criteria = new Criteria();
                Pattern pattern = Pattern.compile(queryString, Pattern.CASE_INSENSITIVE);
                criteria.orOperator(Criteria.where(Constants.CATEGORY).regex(pattern),
                                    Criteria.where(Constants.SUB_CATEGORY).regex(pattern),
                        Criteria.where(Constants.PART_NUMBER).regex(pattern),
                        Criteria.where(Constants.DESCRIPTION).regex(pattern),
                        Criteria.where(Constants.NAME).regex(pattern));
                criteria.andOperator(Criteria.where(Constants.CATALOG_TYPE).is(Constants.PRODUCT));
                MatchOperation textMatchOperation = Aggregation.match(criteria);
                operations.add(textMatchOperation);

            MatchOperation matchOperation;
            if (Objects.nonNull(category) && !category.isEmpty()) {
                matchOperation = Aggregation.match(Criteria.where(Constants.CATEGORY).is(category)
                        .and(Constants.ACTIVE).is(true)
                        .and(Constants.IS_DELETED).is(false)
                        .and(Constants.CATALOG_TYPE).is(Constants.PRODUCT)
                );
            }
            else{
                matchOperation = Aggregation.match(Criteria.where(Constants.ACTIVE).is(true)
                        .and(Constants.IS_DELETED).is(false)
                        .and(Constants.CATALOG_TYPE).is(Constants.PRODUCT));
            }
            operations.add(matchOperation);
        }
        catch (Exception e) {
            throw new DomainException("An error occurred while getting criteria for filter product list: ", e.getMessage());
        }
        return operations;
    }
}


