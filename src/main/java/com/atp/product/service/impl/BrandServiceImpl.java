package com.atp.product.service.impl;

import com.atp.product.controller.dto.response.*;
import com.atp.product.exception.bad_request.InvalidInputException;
import com.atp.product.model.Brands;
import com.atp.product.model.Product;
import com.atp.product.service.BrandService;
import com.atp.product.utils.AuthorizationUtils;
import com.atp.product.utils.Constants;
import com.atp.product.exception.DomainException;
import com.mongodb.BasicDBObject;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class BrandServiceImpl implements BrandService {

    private final MongoTemplate mongoTemplate;
    private final ProductServiceImpl productServiceImpl;
    private final S3ServiceImpl s3Service;
    private final CommonServiceImpl commonServiceImpl;
    private final HttpServletRequest request;
    public BrandServiceImpl(MongoTemplate mongoTemplate, ProductServiceImpl productServiceImpl, S3ServiceImpl s3Service, CommonServiceImpl commonServiceImpl, HttpServletRequest request) {
        this.mongoTemplate = mongoTemplate;
        this.productServiceImpl = productServiceImpl;
        this.s3Service = s3Service;
        this.commonServiceImpl = commonServiceImpl;
        this.request = request;
    }
    @Value("${aws.s3.bucket.name}")
    private String bucketName;

    @Value("${assets.root}")
    private String assetsRoot;
    @Override
    public List<BrandResponseHomePage> getShopByBrands() {
        log.info("Inside call to getShopByBrands() service");
        try {
            MatchOperation matchOperation = Aggregation.match(
                    Criteria.where(Constants.ACTIVE).is(true)
                            .and("featuredBrand").is(true));

            Aggregation aggregation = Aggregation.newAggregation(matchOperation);
            log.info("Aggregation pipeline getShopByBrands(): {}", aggregation);

            AggregationResults<BrandResponseHomePage> results = mongoTemplate.aggregate(aggregation, Constants.BRANDS, BrandResponseHomePage.class);
            log.info("Aggregation pipeline results getShopByBrands(): {}", results.getMappedResults());

            return getBrandResponseHomePages(results);
        } catch (InvalidInputException e) {
            throw e;
        } catch (Exception e) {
            throw new DomainException("An error occurred while retrieving shop by brands", e);
        }
    }

    @NotNull
    private List<BrandResponseHomePage> getBrandResponseHomePages(AggregationResults<BrandResponseHomePage> results) {
        List<BrandResponseHomePage> finalResults = results.getMappedResults();

        finalResults.forEach(brand -> {
            String logoUrl = brand.getBrandLogoUrl();
            if (!logoUrl.isBlank()) {
                String imageUrl = s3Service.generatePresignedUrl(bucketName, assetsRoot + "/" + logoUrl).toString();
                brand.setBrandLogoUrl(imageUrl);
            } else {
                brand.setBrandLogoUrl("");
            }
        });
        return finalResults;
    }

    @Override
    public List<CategoryBrandResponse> getBrandListByCategory() {
        log.info("Inside call to getBrandListByCategory() service");
        AggregationResults<CategoryBrandResponse> results;
        try {
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(Criteria.where(Constants.BRAND_NAME).exists(true).ne(null)
                            .and(Constants.IS_DELETED).is(false)
                            .and(Constants.ACTIVE).is(true)),
                    Aggregation.group(Constants.CATEGORY)
                            .addToSet(new BasicDBObject(Constants.BRAND_ID, "$"+Constants.BRAND_ID)
                                    .append(Constants.BRAND_NAME, "$"+Constants.BRAND_NAME)
                                    .append(Constants.BRAND_SLUG, "$"+Constants.BRAND_SLUG))
                            .as(Constants.BRANDS),
                    Aggregation.project()
                            .and("_id").as(Constants.CATEGORY_NAME)
                            .and("_id." + Constants.CATEGORY_SLUG).as(Constants.CATEGORY_SLUG)
                            .and(Constants.BRANDS).as(Constants.BRANDS)
                            .andExclude("_id")
            );
            log.info("Aggregation pipeline getBrandListByCategory(): {}", aggregation);
            results =mongoTemplate.aggregate(aggregation, Constants.PRODUCT_COLLECTION_NAME, CategoryBrandResponse.class);

            log.info("Aggregation pipeline results getBrandListByCategory(): {}", results.getMappedResults());
            return results.getMappedResults();
        } catch (InvalidInputException e) {
            throw e;
        } catch (Exception e) {
            throw new DomainException("An error occurred while retrieving brand list by category", e);
        }
    }

    @Override
    public BrandPageResponse getBrandPageByBrandSlug(String brandSlug) {
        log.info("Inside call to getBrandPageByBrandSlug() service with brandSlug: {}", brandSlug);
        BrandPageResponse brandPageResponse = new BrandPageResponse();
        try {
            // Retrieve the brand name separately
            Brands brands = Optional.ofNullable(getBrandDetailsByBrandSlug(brandSlug)).orElseGet(() -> {
                setDefaultBrandResponse(brandPageResponse);
                return null;
            });

            Optional.ofNullable(brands).ifPresent(brand -> {
                brandPageResponse.setBrandName(Optional.ofNullable(brand.getBrandName()).orElse(""));
                brandPageResponse.setBrandDescription(Optional.ofNullable(brand.getBrandDescription()).orElse(""));
                brandPageResponse.setBrandId(Optional.ofNullable(brand.getBrandId()).orElse(""));
                brandPageResponse.setBrandSlug(Optional.ofNullable(brand.getBrandSlug()).orElse(""));

                String logoUrl = Optional.ofNullable(brand.getBrandLogoUrl()).orElse("");
                brandPageResponse.setBrandLogoUrl(logoUrl.isEmpty() ? "" :
                        s3Service.generatePresignedUrl(bucketName, assetsRoot + "/" + logoUrl).toString());
            });
            // Get category list by brand id
            List<CategoryResponse> categoryList = getCategoryListByBrandSlug(brandSlug);
            brandPageResponse.setCategoryList(categoryList);

            // Get new products list by brand id
            List<ProductResponseHomePage> newProductList = getNewProductsByBrandSlug(brandSlug);
            brandPageResponse.setNewProductList(newProductList);

            // Get popular products list by brand id
            List<ProductResponseHomePage> popularProductList = getPopularProductsByBrandSlug(brandSlug);
            brandPageResponse.setPopularProductList(popularProductList);

            log.info("Final results getBrandPageByBrandId(): {}", brandPageResponse);
            return brandPageResponse;
        } catch (InvalidInputException e) {
            throw e;
        } catch (Exception e) {
            throw new DomainException("An error occurred while retrieving brand page by brand Id", e);
        }
    }
    private void setDefaultBrandResponse(BrandPageResponse brandPageResponse) {
        brandPageResponse.setBrandName("");
        brandPageResponse.setBrandDescription("");
        brandPageResponse.setBrandSlug("");
        brandPageResponse.setBrandLogoUrl("");
        brandPageResponse.setBrandId("");
    }

    public Brands getBrandDetailsByBrandSlug(String brandSlug) {
        log.info("Inside method for getting brand details getBrandDetailsById(): {}", brandSlug);
        try{
            Query brandQuery = new Query();
            brandQuery.addCriteria(Criteria.where(Constants.BRAND_SLUG).is(brandSlug));
            return mongoTemplate.findOne(brandQuery, Brands.class, Constants.BRANDS);
        }
        catch (Exception e) {
            throw new DomainException("An error occurred while retrieving brand details by brand Id", e.getMessage());
        }
    }

    public List<CategoryResponse> getCategoryListByBrandSlug(String brandSlug) {
        log.info("Inside method for getting category list getCategoryListByBrandSlug(): {}", brandSlug);
        try{
            MatchOperation matchOperation = Aggregation.match(Criteria.where(Constants.CATALOG_TYPE).is(Constants.PRODUCT)
                    .and(Constants.IS_DELETED).is(false)
                    .and(Constants.ACTIVE).is(true)
                    .and(Constants.BRAND_SLUG).is(brandSlug));
            ProjectionOperation projectOperation = Aggregation.project(Constants.PRIMARY_IMAGE, Constants.LAST_MODIFIED_DATE, Constants.CATEGORY_SLUG)
                    .and("_id").as(Constants.CATEGORY);

            GroupOperation groupOperation = Aggregation.group(Constants.CATEGORY)
                    .first(Constants.PRIMARY_IMAGE).as(Constants.PRIMARY_IMAGE)
                    .first(Constants.LAST_MODIFIED_DATE).as(Constants.LAST_MODIFIED_DATE)
                    .first(Constants.CATEGORY_SLUG).as(Constants.CATEGORY_SLUG);

            SortOperation sortOperation = Aggregation.sort(Sort.by(Sort.Order.desc(Constants.LAST_MODIFIED_DATE)));
            LimitOperation limitOperation = Aggregation.limit(20);
            Aggregation aggregation = Aggregation.newAggregation(matchOperation, groupOperation, projectOperation, limitOperation, sortOperation);

            log.info("Aggregation pipeline for categoryList getCategoryListByBrandSlug(): {}", aggregation);
            AggregationResults<CategoryResponse> results = mongoTemplate.aggregate(
                    aggregation, Constants.PRODUCT_COLLECTION_NAME, CategoryResponse.class);
            log.info("Aggregation pipeline results for categoryList getCategoryListByBrandSlug(): {}", results);

            List<CategoryResponse> mappedResults = results.getMappedResults();
            if(!mappedResults.isEmpty()){

                for (CategoryResponse categoryResponse : mappedResults){
                    String categoryName = categoryResponse.getCategory();
                    String imageUrl = getPrimaryImageByCategoryName(categoryName.isEmpty() ? "" : categoryName);
                    categoryResponse.setPrimaryImage(imageUrl != null && !imageUrl.isEmpty() ?
                            s3Service.generatePresignedUrl(bucketName, assetsRoot + "/" + imageUrl).toString():"");
                }
            }
            return mappedResults;
        }
        catch (Exception e) {
             throw new DomainException("An error occurred while retrieving category list by brand slug", e.getMessage());
        }
    }

    public String getPrimaryImageByCategoryName(String categoryName) {
        Criteria criteria = Criteria.where(Constants.NAME).is(categoryName)
                .and(Constants.CATALOG_TYPE).is("CATEGORY")
                .and(Constants.IS_DELETED).is(false)
                .and(Constants.ACTIVE).is(true);

        Query query = new Query(criteria);

        Product result = mongoTemplate.findOne(query, Product.class, Constants.PRODUCT_COLLECTION_NAME);

        if (result != null) {
            return result.getPrimaryImage();
        }
        return null;
    }

    public List<ProductResponseHomePage> getNewProductsByBrandSlug(String brandSlug) {
        log.info("Inside method for getting new product list getNewProductsByBrandSlug(): {}", brandSlug);
        try{
            List<AggregationOperation> operations = new ArrayList<>();

            MatchOperation matchOperationNewProducts = Aggregation.match(Criteria.where(Constants.IS_DELETED).is(false)
                    .and(Constants.ACTIVE).is(true)
                    .and(Constants.BRAND_SLUG).is(brandSlug)
                    .and(Constants.CATALOG_TYPE).is(Constants.PRODUCT)
                    .and("newItem").is(true));
            operations.add(matchOperationNewProducts);

            if(AuthorizationUtils.isAuthorizationHeaderValid(request)){
                String customerCorrelationId = AuthorizationUtils.getCustomerCorrelationId(request);
                operations.add(productServiceImpl.getLookupOperation(customerCorrelationId));
                operations.add(productServiceImpl.getAddFieldsOperation());
            }

            ProjectionOperation projectionOperationNewProducts = productServiceImpl.getProjectionOperation();
            operations.add(projectionOperationNewProducts);

            SortOperation sortOperationNewProducts = new SortOperation(Sort.by(Sort.Direction.DESC, Constants.LAST_MODIFIED_DATE));
            operations.add(sortOperationNewProducts);

            operations.add(Aggregation.limit(20));

            Aggregation aggregationNewProducts = Aggregation.newAggregation(operations);
            log.info("Aggregation pipeline for newProductList getNewProductsByBrandSlug(): {}", aggregationNewProducts);

            List<ProductResponseHomePage> newProductList = mongoTemplate.aggregate(
                    aggregationNewProducts, Constants.PRODUCT_COLLECTION_NAME, ProductResponseHomePage.class).getMappedResults();
            log.info("Aggregation pipeline results for newProductList getNewProductsByBrandSlug(): {}", newProductList);
            List<ProductResponseHomePage> newProductListModified = commonServiceImpl.resizeImages(newProductList);
            return newProductListModified != null ? newProductListModified : new ArrayList<>();
        }
        catch (Exception e) {
            throw new DomainException("An error occurred while retrieving new product list by brand slug", e.getMessage());
        }
    }

    public List<ProductResponseHomePage> getPopularProductsByBrandSlug(String brandSlug) {
        log.info("Inside method for getting popular product list getPopularProductsByBrandSlug(): {}", brandSlug);
        try{
            List<AggregationOperation> operations = new ArrayList<>();

            MatchOperation matchOperationPopularProducts = Aggregation.match(Criteria.where(Constants.IS_DELETED).is(false)
                    .and(Constants.ACTIVE).is(true)
                    .and(Constants.BRAND_SLUG).is(brandSlug)
                    .and(Constants.CATALOG_TYPE).is(Constants.PRODUCT));
            operations.add(matchOperationPopularProducts);

            if(AuthorizationUtils.isAuthorizationHeaderValid(request)){
                String customerCorrelationId = AuthorizationUtils.getCustomerCorrelationId(request);
                operations.add(productServiceImpl.getLookupOperation(customerCorrelationId));
                operations.add(productServiceImpl.getAddFieldsOperation());
            }

            ProjectionOperation projectionOperationPopularProducts = productServiceImpl.getProjectionOperation();
            operations.add(projectionOperationPopularProducts);

            SortOperation sortOperationPopularProducts = new SortOperation(Sort.by(Sort.Direction.DESC, Constants.LAST_MODIFIED_DATE));
            operations.add(sortOperationPopularProducts);

            operations.add(Aggregation.limit(20));

            Aggregation aggregationNewProducts = Aggregation.newAggregation(operations);
            log.info("Aggregation pipeline for popularProductList getPopularProductsByBrandSlug(): {}", aggregationNewProducts);

            List<ProductResponseHomePage> popularProductList = mongoTemplate.aggregate(
                    aggregationNewProducts, Constants.PRODUCT_COLLECTION_NAME, ProductResponseHomePage.class).getMappedResults();
            log.info("Aggregation pipeline results for popularProductList getPopularProductsByBrandSlug(): {}", popularProductList);
            List<ProductResponseHomePage> popularProductListModified = commonServiceImpl.resizeImages(popularProductList);
            return popularProductListModified != null ? popularProductListModified : new ArrayList<>();
        }
        catch (Exception e) {
            throw new DomainException("An error occurred while retrieving popular product list by brand slug", e.getMessage());
        }
    }
}
