package com.atp.product.service.impl;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;

import java.net.URL;
import java.time.Duration;

@Service
public class S3ServiceImpl {

    public final S3Client s3Client;
    public final S3Presigner presigner;
    public S3ServiceImpl(@Value("${aws.access.key}") String accessKeyId,
                         @Value("${aws.secret.key}") String secretAccessKey,
                         @Value("${aws.s3.region}") String region) {
        AwsBasicCredentials awsCreds = AwsBasicCredentials.create(accessKeyId, secretAccessKey);
        StaticCredentialsProvider credentialsProvider = StaticCredentialsProvider.create(awsCreds);
        
        this.s3Client = S3Client.builder()
                                .region(Region.of(region))
                                .credentialsProvider(credentialsProvider)
                                .build();
        
        this.presigner = S3Presigner.builder()
                                    .region(Region.of(region))
                                    .credentialsProvider(credentialsProvider)
                                    .build();
    }

    public URL generatePresignedUrl(String bucketName, String objectKey) {
        GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                                                            .bucket(bucketName)
                                                            .key(objectKey)
                                                            .build();

        GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                                                                        .getObjectRequest(getObjectRequest)
                                                                        .signatureDuration(Duration.ofMinutes(100))
                                                                        .build();

        return presigner.presignGetObject(presignRequest).url();
    }
}
