package com.atp.product.service.impl;

import com.atp.product.controller.dto.request.InternalPurchaseOrderRequest;
import com.atp.product.controller.dto.request.PurchaseOrderRequest;
import com.atp.product.controller.dto.request.UpdateInventoryRequest;
import com.atp.product.controller.dto.response.ProductResponseHomePage;
import com.atp.product.controller.dto.response.PurchaseOrderResponse;
import com.atp.product.controller.dto.response.UpdateInventoryResponse;
import com.atp.product.exception.DomainException;
import com.atp.product.karmak_responses.*;
import com.atp.product.model.ScheduledOrder;
import com.atp.product.utils.AuthorizationUtils;
import com.atp.product.utils.Constants;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import jakarta.servlet.http.HttpServletRequest;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
public class CommonServiceImpl {

    public static final Logger logger = LoggerFactory.getLogger(CommonServiceImpl.class);

    @Value("${aws.s3.bucket.name}")
    private String bucketName;

    @Value("${assets.root}")
    private String assetsRoot;

    @Value("${Karmak.partSearchURL}")
    private String karmaPartSearchURL;

    @Value("${Karmak.customerURL}")
    private String karmaCustomerURL;

    @Value("${Karmak.AccountNumber}")
    private String karmaAccountNumber;

    @Value("${Ocp-Apim-Subscription-Key}")
    private String subScriptionKey;

    @Value("${Karmak.purchaseOrderURL}")
    private String karmakPoUrl;
    @Value("${Karmak.purchaseOrderSubscriptionKey}")
    private String poSubKey;

    @Value("${Karmak.inventoryUpdateUrl}")
    private String karmakInventoryUpdateUrl;

    private final S3ServiceImpl s3Service;
    private final OkHttpClient okHttpClient;
    private final ObjectMapper objectMapper;
    private final HttpServletRequest request;
    private final TaskExecutor executorAsyncThread;
    private final PurchaseOrderService purchaseOrderService;

    public CommonServiceImpl(S3ServiceImpl s3Service, OkHttpClient okHttpClient, ObjectMapper objectMapper, HttpServletRequest request, TaskExecutor executorAsyncThread, PurchaseOrderService purchaseOrderService) {
        this.s3Service = s3Service;
        this.okHttpClient = okHttpClient;
        this.objectMapper = objectMapper;
        this.request = request;
        this.executorAsyncThread = executorAsyncThread;
        this.purchaseOrderService = purchaseOrderService;
    }

    public String readFile(String filePath) throws IOException {
        return new String(Files.readAllBytes(Paths.get(filePath)));
    }

    //Existing Method Without optimize
   /* public List<ProductResponseHomePage> resizeImages(List<ProductResponseHomePage> results) {
        List<ProductResponseHomePage> modifiedResults = new ArrayList<>();
        try {
            for (ProductResponseHomePage product : results) {
                processProductImage(product);
                updateProductPriceAndQuantity(product);
                modifiedResults.add(product);
            }
        } catch (Exception e) {
            throw new DomainException("An error occurred while resizing images", e.getMessage());
        }
        return modifiedResults;
    }

    private void processProductImage(ProductResponseHomePage product) {
        String primaryImage = product.getPrimaryImage();
        if (isValidImage(primaryImage)) {
            String resizedImageUrl = getResizedImageUrl(product, primaryImage, "200x200");
            product.setPrimaryImage(resizedImageUrl);
        } else {
            product.setPrimaryImage("");
        }
    }

    private boolean isValidImage(String image) {
        return !image.isBlank();
    }

    private String getResizedImageUrl(ProductResponseHomePage product, String baseImageUrl, String specifiedSize) {
        String basePattern = baseImageUrl.substring(0, baseImageUrl.lastIndexOf('_')) + "_";
        if (product.getAssetMap() != null && !product.getAssetMap().isEmpty()) {
            for (Map.Entry<String, String> entry : product.getAssetMap().entrySet()) {
                String url = entry.getValue();
                if (url.startsWith(basePattern) && url.contains(specifiedSize)) {
                    return s3Service.generatePresignedUrl(bucketName, assetsRoot + "/" + url).toString();
                }
            }
        }
        return baseImageUrl; // Return original if resized image URL is not found
    }


    private void updateProductPriceAndQuantity(ProductResponseHomePage product) {
        if (!AuthorizationUtils.isAuthorizationHeaderValid(request)) {
            Map<String, String> priceAndQuantity = getPriceAndQuantityForProduct(product.getPartNumber());
            if (priceAndQuantity != null && !priceAndQuantity.isEmpty()) {

                // Use BigDecimal to parse and round the price
                BigDecimal price = new BigDecimal(priceAndQuantity.get(Constants.PRICE));
                price = price.setScale(2, RoundingMode.HALF_UP);

                product.setPrice(price.doubleValue());
                //product.setPrice(Double.parseDouble(priceAndQuantity.get(Constants.PRICE)));
                product.setQuantity(Integer.parseInt(priceAndQuantity.get(Constants.QUANTITY)));
                if (product.getProductQuantity() != 0 && product.getPrice() != 0.0) {
                    //product.setRowTotal(product.getProductQuantity() * product.getPrice());
                    BigDecimal productQuantity = new BigDecimal(product.getProductQuantity());
                    BigDecimal rowTotal = price.multiply(productQuantity).setScale(2, RoundingMode.HALF_UP);
                    product.setRowTotal(rowTotal.doubleValue());

                }
            }
        }
    }*/

   /* public Map<String,String> getPriceAndQuantityForProduct(String partNumber) {
        Map<String,String> finalResults = new HashMap<>();
        try {
            String karmakUrl = karmaPartSearchURL;
            String karmakAccount = karmaAccountNumber;
            String subscriptionKey = subScriptionKey;

            //getCustomerId from Header
            String karmakCustomerId = AuthorizationUtils.getKarmakCustomerId(request);



            // Initialize customerID
            String customerID = null;

            // Check if karmakCustomerId is null or empty
            if (karmakCustomerId != null && !karmakCustomerId.isEmpty()) {
                customerID = karmakCustomerId;  // If valid, assign the string value
            } else {
                customerID = "null";  // Otherwise, set customerID as null
            }

            log.info("int customer ID " + customerID);

            String requestJson = "{\"customerID\": " + customerID + "," + "\"locationID\": 1," + "\"region\": null,"
                    + "\"parts\": [{" + "\"number\": \"" + partNumber + "\"," + "\"description\": null,"
                    + "\"exactMatch\": true," + "\"source\": null," + "\"crossReference\": false," + "\"PageSize\": 1" + "}]" + "}";


            RequestBody body = RequestBody.create(requestJson, MediaType.parse("application/json"));
            Request partRequest = new Request.Builder().url(karmakUrl).addHeader("Content-Type", "application/json")
                    .addHeader("Cache-Control", "no-cache").addHeader("KarmakAccountNumber", karmakAccount)
                    .addHeader("Ocp-Apim-Subscription-Key", subscriptionKey).post(body).build();
            // Create Item in PLM
            Call call = okHttpClient.newCall(partRequest);
            Response partResponse = call.execute();
            int statusCode = partResponse.code();
            if (statusCode >= 200 && statusCode < 300) {
                JsonNode json = objectMapper.readValue(partResponse.body().string(), JsonNode.class);
                partResponse.close();
                if (Objects.nonNull(json) && json instanceof ArrayNode) {
                    ArrayNode results = (ArrayNode) json;
                    if (!results.isEmpty()) {
                        JsonNode entries = results.get(0).get("results");
                        if (entries != null) {
                            ArrayNode parts = (ArrayNode) entries;
                            JsonNode firstPart = parts.get(0);
                            if (firstPart != null) {
                                // Extract customerPrice and available fields
                                JsonNode priceNode = firstPart.get("price");
                                JsonNode availableNode = firstPart.get("available");

                                if (priceNode != null && availableNode != null) {
                                    finalResults.put(Constants.PRICE, priceNode.get("customerPrice").asText());
                                    finalResults.put(Constants.QUANTITY, availableNode.asText());
                                } else {
                                    logger.info("Price or available field is missing in the first element.");
                                }
                            } else {
                                logger.info("First element of 'results' array is missing.");
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new DomainException("An error occurred while calling karmak api", e.getMessage());
        }
        return finalResults;
    }*/

    public Map<String, String> getCustomerIdByCustomerKey(String karmakCustomerKey) {
        Map<String, String> result = new HashMap<>();
        try {
            String requestUrl = karmaCustomerURL + karmakCustomerKey;

            Request customerRequest = new Request.Builder()
                    .url(requestUrl)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Cache-Control", "no-cache")
                    .addHeader("KarmakAccountNumber", karmaAccountNumber)
                    .addHeader("Ocp-Apim-Subscription-Key", subScriptionKey)
                    .get()
                    .build();

            Call call = okHttpClient.newCall(customerRequest);
            Response customerResponse = call.execute();
            int statusCode = customerResponse.code();

            if (statusCode >= 200 && statusCode < 300) {
                JsonNode json = objectMapper.readTree(customerResponse.body().string());
                customerResponse.close();

                if (Objects.nonNull(json)) {
                    JsonNode results = json.get("Results");

                    if (results != null && results.isArray() && !results.isEmpty()) {
                        JsonNode firstResult = results.get(0);
                        JsonNode customerIdNode = firstResult.get("CustomerID");
                        JsonNode companyNameNode = firstResult.get("CompanyName");

                        if (customerIdNode != null && companyNameNode != null) {
                            result.put("CustomerID", customerIdNode.asText());
                            result.put("CompanyName", companyNameNode.asText());
                        } else {
                            logger.info("CustomerID or CompanyName field is missing in the response.");
                        }
                    } else {
                        logger.info("'Results' array is empty or missing in the response.");
                    }
                } else {
                    logger.info("Invalid JSON response from the Karmak API.");
                }
            } else {
                logger.error("Failed to fetch CustomerID. Status code: {}", statusCode);
            }
        } catch (Exception e) {
            throw new DomainException("An error occurred while calling the Karmak Customer API", e.getMessage());
        }
        return result;
    }

    public AggregationOperation documentToAggregationOperation(@NonNull Document document) {
        return context -> document;
    }

    //ForkJOin optimization for improve load performance

    private String getKarmakCustomerId() {
        return AuthorizationUtils.getKarmakCustomerId(request);  // Returns customer ID from request
    }

    public List<ProductResponseHomePage> resizeImages(List<ProductResponseHomePage> results) {
        List<CompletableFuture<Void>> futures = results.stream()
                .map(product -> CompletableFuture.runAsync(() -> {
                    try {
                        processProductImage(product);
                        updateProductPriceAndQuantity(product);  // Make sure this is handled asynchronously.
                    } catch (Exception e) {
                        logger.error("Error processing product: {}", product.getPartNumber(), e);
                    }
                }, executorAsyncThread))  // Use the executorAsyncThread (decorated executor)
                .collect(Collectors.toList());

        // Wait for all futures to complete
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        return results;
    }

    private void processProductImage(ProductResponseHomePage product) {
        String primaryImage = product.getPrimaryImage();
        if (isValidImage(primaryImage)) {
            String resizedImageUrl = getResizedImageUrl(product, primaryImage, "200x200");
            product.setPrimaryImage(resizedImageUrl);
        } else {
            product.setPrimaryImage("");
        }
    }

    private boolean isValidImage(String image) {
        return !image.isBlank();
    }

    private String getResizedImageUrl(ProductResponseHomePage product, String baseImageUrl, String specifiedSize) {
        String basePattern = baseImageUrl.substring(0, baseImageUrl.lastIndexOf('_')) + "_";
        if (product.getAssetMap() != null && !product.getAssetMap().isEmpty()) {
            for (Map.Entry<String, String> entry : product.getAssetMap().entrySet()) {
                String url = entry.getValue();
                if (url.startsWith(basePattern) && url.contains(specifiedSize)) {
                    return s3Service.generatePresignedUrl(bucketName, assetsRoot + "/" + url).toString();
                }
            }
        }
        return baseImageUrl;
    }

    private void updateProductPriceAndQuantity(ProductResponseHomePage product) {
        // Proceed only if the authorization header is valid
        if (AuthorizationUtils.isAuthorizationHeaderValid(request)) {
            CompletableFuture<Map<String, String>> priceAndQuantityFuture = getPriceAndQuantityForProductAsync(product.getPartNumber());

            priceAndQuantityFuture.thenAccept(priceAndQuantity -> {
                if (priceAndQuantity == null || priceAndQuantity.isEmpty()) {
                    logger.warn("Price and quantity data is not for this product: {}", product.getPartNumber());
                    return;
                }
                try {
                    if (priceAndQuantity != null && !priceAndQuantity.isEmpty()) {
                        // Use BigDecimal to parse and round the price
                        BigDecimal price = new BigDecimal(priceAndQuantity.get(Constants.PRICE));
                        price = price.setScale(2, RoundingMode.HALF_UP);

                        product.setPrice(price.doubleValue());
                        product.setQuantity(Integer.parseInt(priceAndQuantity.get(Constants.QUANTITY)));
                        product.setKarmakPartId(Integer.parseInt(priceAndQuantity.get(Constants.PARTS_INVENTORY_DETAIL_ID)));

                        if (product.getProductQuantity() != 0 && product.getPrice() != 0.0) {
                            BigDecimal productQuantity = new BigDecimal(product.getProductQuantity());
                            BigDecimal rowTotal = price.multiply(productQuantity).setScale(2, RoundingMode.HALF_UP);
                            product.setRowTotal(rowTotal.doubleValue());
                        }
                    }
                } catch (NumberFormatException | ArithmeticException e) {
                    logger.error("Error processing price and quantity for product: {}", product.getPartNumber(), e);
                }
            }).exceptionally(ex -> {
                logger.error("Error fetching price and quantity for product: {}", product.getPartNumber(), ex);
                return null;
            }).join(); // Wait for completion if needed
        } else {
            logger.warn("Authorization failed for product: {}", product.getPartNumber());
        }
    }


    public CompletableFuture<Map<String, String>> getPriceAndQuantityForProductAsync(String partNumber) {
        Map<String, String> finalResults = new HashMap<>();
        try {

            String karmakUrl = karmaPartSearchURL;
            String karmakAccount = karmaAccountNumber;
            String subscriptionKey = subScriptionKey;

            String karmakCustomerId = getKarmakCustomerId();
            // Initialize customerID
            String customerID = null;

            // Check if karmakCustomerId is null or empty
            if (karmakCustomerId != null && !karmakCustomerId.isEmpty()) {
                customerID = karmakCustomerId;  // If valid, assign the string value
            } else {
                customerID = "null";  // Otherwise, set customerID as null
            }

            log.info("int customer ID " + customerID);

            String requestJson = "{\"customerID\": " + customerID + "," + "\"locationID\": 1," + "\"region\": null,"
                    + "\"parts\": [{" + "\"number\": \"" + partNumber + "\"," + "\"description\": null,"
                    + "\"exactMatch\": true," + "\"source\": null," + "\"crossReference\": false," + "\"PageSize\": 1" + "}]" + "}";


            RequestBody body = RequestBody.create(requestJson, MediaType.parse("application/json"));
            Request partRequest = new Request.Builder().url(karmakUrl).addHeader("Content-Type", "application/json")
                    .addHeader("Cache-Control", "no-cache").addHeader("KarmakAccountNumber", karmakAccount)
                    .addHeader("Ocp-Apim-Subscription-Key", subscriptionKey).post(body).build();
            // Create Item in PLM
            Call call = okHttpClient.newCall(partRequest);
            Response partResponse = call.execute();
            int statusCode = partResponse.code();
            if (statusCode >= 200 && statusCode < 300) {
                JsonNode json = objectMapper.readValue(partResponse.body().string(), JsonNode.class);
                partResponse.close();
                if (Objects.nonNull(json) && json instanceof ArrayNode) {
                    ArrayNode results = (ArrayNode) json;
                    if (!results.isEmpty()) {
                        JsonNode entries = results.get(0).get("results");
                        if (entries != null) {
                            ArrayNode parts = (ArrayNode) entries;
                            JsonNode firstPart = parts.get(0);
                            if (firstPart != null) {
                                // Extract customerPrice and available fields
                                JsonNode priceNode = firstPart.get("price");
                                JsonNode availableNode = firstPart.get("available");
                                JsonNode inventoryDetailIdNode = firstPart.get("PartsInventoryDetailID");
                                JsonNode branchCodeNode = firstPart.get("branchCode");


                                if (priceNode != null && availableNode != null) {
                                    finalResults.put(Constants.PRICE, priceNode.get("customerPrice").asText());
                                    finalResults.put(Constants.QUANTITY, availableNode.asText());
                                    finalResults.put(Constants.PARTS_INVENTORY_DETAIL_ID, Integer.toString(inventoryDetailIdNode.asInt()));
                                    finalResults.put(Constants.BRANCH_CODE, branchCodeNode.asText());
                                } else {
                                    logger.info("Price or available field is missing in the first element.");
                                }
                            } else {
                                logger.info("First element of 'results' array is missing.");
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new DomainException("An error occurred while calling karmak api", e.getMessage());
        }
        return CompletableFuture.completedFuture(finalResults);
    }

    //Part Purchase Order API
    /*public PurchaseOrderResponse createPurchaseOrder(PurchaseOrderRequest poRequest) {
        try {
            // Default the PODate to current date if it's null
            if (poRequest.getPoDate()== null) {
                poRequest.setPoDate(LocalDate.now());
            }

            String requestJson = objectMapper.writeValueAsString(poRequest);
            RequestBody body = RequestBody.create(requestJson, MediaType.parse("application/json"));

            Request request = new Request.Builder()
                    .url(karmakPoUrl)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Cache-Control", "no-cache")
                    .addHeader("KarmakAccountNumber", karmaAccountNumber)
                    .addHeader("Ocp-Apim-Subscription-Key", poSubKey)
                    .post(body)
                    .build();

            Response response = okHttpClient.newCall(request).execute();

            if (response.code() == 201) {
                if (response.body() == null) {
                    throw new KarmakApiException("Karmak API returned 201 but with no response body.");
                }
                return objectMapper.readValue(response.body().string(), PurchaseOrderResponse.class);
            } else {
                String error = response.body() != null ? response.body().string() : "No error body";
                logger.error("PO creation failed. Code: {}, Body: {}", response.code(), error);
                throw new PurchaseOrderCreationException("Failed to create Purchase Order. Karmak responded with error: " + error);
            }

        } catch (Exception e) {
            throw new KarmakApiException("Unexpected error during PO creation", e);
        }
    }*/


    public PurchaseOrderResponse createPurchaseOrder(PurchaseOrderRequest request) {
        try {
            if (request.getPoDate() == null) {
                request.setPoDate(LocalDate.now());
            }

            String json = objectMapper.writeValueAsString(request);
            RequestBody body = RequestBody.create(json, MediaType.parse("application/json"));

            Request httpRequest = new Request.Builder()
                    .url(karmakPoUrl)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Cache-Control", "no-cache")
                    .addHeader("KarmakAccountNumber", karmaAccountNumber)
                    .addHeader("Ocp-Apim-Subscription-Key", poSubKey)
                    .post(body)
                    .build();

            try (Response response = okHttpClient.newCall(httpRequest).execute()) {
                if (response.code() == 201) {
                    assert response.body() != null;
                    PurchaseOrderResponse poResponse = objectMapper.readValue(response.body().string(), PurchaseOrderResponse.class);

                    // Update inventory
                    List<UpdateInventoryRequest> inventoryRequests = poResponse.getPartsPurchaseOrder().getLineItems().stream()
                            .map(item -> {
                                UpdateInventoryRequest inv = new UpdateInventoryRequest();
                                inv.setBranch(""); // or configurable
                                inv.setPartNumber(item.getPartNumber());
                                inv.setSupplier(item.getSupplier());
                                inv.setQuantityAvailable(item.getQuantity());
                                return inv;
                            }).collect(Collectors.toList());

                    updateInventory(inventoryRequests);

                    return poResponse;
                } else {
                    String error = response.body() != null ? response.body().string() : "No error body";
                    logger.error("PO creation failed. Code: {}, Body: {}", response.code(), error);
                    throw new RuntimeException("Failed to create Purchase Order. " + error);
                }
            }
        } catch (Exception e) {
            logger.error("Exception during PO creation", e);
            throw new RuntimeException("Exception occurred while creating PO: " + e.getMessage(), e);
        }
    }

    public List<UpdateInventoryResponse> updateInventory(List<UpdateInventoryRequest> inventoryList) {
        try {
            String json = objectMapper.writeValueAsString(inventoryList);
            RequestBody body = RequestBody.create(json, MediaType.parse("application/json"));

            Request jsonRequest = new Request.Builder()
                    .url(karmakInventoryUpdateUrl)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Cache-Control", "no-cache")
                    .put(body)
                    .build();

            try (Response response = okHttpClient.newCall(jsonRequest).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    return objectMapper.readValue(response.body().string(), new TypeReference<List<UpdateInventoryResponse>>() {
                    });
                } else {
                    String error = response.body() != null ? response.body().string() : "No error body";
                    logger.error("Inventory update failed. Code: {}, Body: {}", response.code(), error);
                    throw new RuntimeException("Failed to update inventory. " + error);
                }
            }
        } catch (Exception e) {
            logger.error("Exception during inventory update", e);
            throw new RuntimeException("Inventory update error: " + e.getMessage(), e);
        }
    }

    //new method with internal DTO
    public PurchaseOrderResponse createPurchaseOrder(InternalPurchaseOrderRequest internalRequest) {
        try {
            if (internalRequest.getPoDate() == null) {
                internalRequest.setPoDate(LocalDate.now());
            }

            // Convert to the external API format (PurchaseOrderRequest)
            PurchaseOrderRequest karmakRequest = new PurchaseOrderRequest();
            karmakRequest.setLocationID(internalRequest.getLocationID());
            karmakRequest.setFillingLocationID(internalRequest.getFillingLocationID());
            karmakRequest.setApVendorID(internalRequest.getApVendorID());
            karmakRequest.setOrderedBy(internalRequest.getOrderedBy());
            karmakRequest.setSupplierID(internalRequest.getSupplierID());
            karmakRequest.setPoDate(internalRequest.getPoDate());
            karmakRequest.setDirectShip(internalRequest.isDirectShip());
            karmakRequest.setShipping(internalRequest.getShipping());

            List<PurchaseOrderRequest.LineItem> karmakLineItems = internalRequest.getLineItems().stream()
                    .map(line -> {
                        PurchaseOrderRequest.LineItem item = new PurchaseOrderRequest.LineItem();
                        item.setPartID(line.getPartID());
                        item.setQuantity(line.getQuantity());
                        item.setCost(line.getCost());
                        item.setMessage(line.getMessage());
                        return item;
                    }).collect(Collectors.toList());

            karmakRequest.setLineItems(karmakLineItems);

            // Send to Karmak
            String json = objectMapper.writeValueAsString(karmakRequest);
            RequestBody body = RequestBody.create(json, MediaType.parse("application/json"));

            Request httpRequest = new Request.Builder()
                    .url(karmakPoUrl)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Cache-Control", "no-cache")
                    .addHeader("KarmakAccountNumber", karmaAccountNumber)
                    .addHeader("Ocp-Apim-Subscription-Key", poSubKey)
                    .post(body)
                    .build();

            try (Response response = okHttpClient.newCall(httpRequest).execute()) {
                if (response.code() == 201) {
                    assert response.body() != null;
                    PurchaseOrderResponse poResponse = objectMapper.readValue(response.body().string(), PurchaseOrderResponse.class);

                    // Combine internal request & external response to update inventory
                    List<InternalPurchaseOrderRequest.InternalLineItem> originalItems = internalRequest.getLineItems();
                    List<PurchaseOrderResponse.LineItem> responseItems = poResponse.getPartsPurchaseOrder().getLineItems();

                    List<UpdateInventoryRequest> inventoryUpdates = IntStream.range(0, responseItems.size())
                            .mapToObj(i -> {
                                PurchaseOrderResponse.LineItem responseItem = responseItems.get(i);
                                InternalPurchaseOrderRequest.InternalLineItem originalItem = originalItems.get(i);

                                UpdateInventoryRequest update = new UpdateInventoryRequest();
                                update.setBranch(originalItem.getBranch()); // comes from request
                                update.setPartNumber(responseItem.getPartNumber()); // from response
                                update.setSupplier(responseItem.getSupplier());     // from response
                                update.setQuantityAvailable(originalItem.getTotalQuantity() - originalItem.getQuantity());
                                return update;
                            })
                            .collect(Collectors.toList());

                    updateInventory(inventoryUpdates);

                    return poResponse;
                } else {
                    String error = response.body() != null ? response.body().string() : "No error body";
                    logger.error("PO creation failed. Code: {}, Body: {}", response.code(), error);
                    throw new RuntimeException("Failed to create Purchase Order. " + error);
                }
            }

        } catch (Exception e) {
            logger.error("Exception during PO creation", e);
            throw new RuntimeException("Exception occurred while creating PO: " + e.getMessage(), e);
        }
    }


   /* @Scheduled(fixedRate = 3600*60000) // every 1 minute
    public void processScheduledOrders() {
        LocalDate now = LocalDate.now();
        Query query = new Query();
        query.addCriteria(Criteria.where("active").is(true).and("nextRunDate").is(now));

        List<ScheduledOrder> dueOrders = mongoTemplate.find(query, ScheduledOrder.class);
        List<String> globalUnavailableItems = new ArrayList<>();

        for (ScheduledOrder s : dueOrders) {

            KarmakPurchaseOrder purchaseOrder = purchaseOrderService.getPurchaseOrderByExternalId(s.getOriginalOrderId());


            Collection<List<ComplexValues>> complexValueList = purchaseOrderByExternalId.getNonNullComplexValues().values();

            List<String> unavailableParts = new ArrayList<>();
            List<InternalPurchaseOrderRequest.InternalLineItem> internalLineItems = new ArrayList<>();

            for (List<ComplexValue> complexValues : complexValueList) {
                for (ComplexValue complex : complexValues) {
                    Map<String, Value> values = complex.getNonNullValues();

                    String partNumber = Optional.ofNullable(values.get("OrderPartNumber"))
                            .map(Object::toString).orElse("N/A");

                    if (globalUnavailableItems.contains(partNumber)) {
                        unavailableParts.add(partNumber);
                        break;
                    }

                    CompletableFuture<Map<String, String>> resultFuture = getPriceAndQuantityForProductAsync(partNumber);
                    Map<String, String> result = resultFuture.join();

                    String availableQty = result.get(Constants.QUANTITY);
                    String price = result.get(Constants.PRICE);
                    String branch = result.get(Constants.BRANCH_CODE);

                    if (availableQty == null || Integer.parseInt(availableQty) <= 0) {
                        unavailableParts.add(partNumber);
                        globalUnavailableItems.add(partNumber);
                        break;
                    }

                    // Map to InternalLineItem
                    InternalPurchaseOrderRequest.InternalLineItem lineItem = new InternalPurchaseOrderRequest.InternalLineItem();
                    lineItem.setPartID(64); // or lookup ID by partNumber
                    lineItem.setQuantity(Integer.parseInt(availableQty));
                    lineItem.setCost(Double.parseDouble(price));
                    lineItem.setMessage("Scheduled order");
                    lineItem.setBranch(branch);
                    lineItem.setTotalQuantity(Integer.parseInt(availableQty)); // if same as quantity

                    internalLineItems.add(lineItem);
                }
            }

            if (unavailableParts.isEmpty() && !internalLineItems.isEmpty()) {
                InternalPurchaseOrderRequest internalRequest = new InternalPurchaseOrderRequest();
                internalRequest.setPoDate(LocalDate.now());
                internalRequest.setLineItems(internalLineItems);
                internalRequest.setSupplierID(0); // set appropriately
                internalRequest.setApVendorID(0); // set appropriately
                internalRequest.setLocationID(1); // set appropriately
                internalRequest.setFillingLocationID(1); // set appropriately
                internalRequest.setOrderedBy("system@scheduler");
                internalRequest.setDirectShip(false);

                try {
                    PurchaseOrderResponse response = createPurchaseOrder(internalRequest);
                    if (response != null && response.getPoNumber() != null) {
                        logger.info("PO created successfully: {}", response.getPoNumber());

                        //This method is in another project so i have to call the api from that project
                        placeOrder(purchaseOrder);
                        s.setNextRunDate(s.getNextRunDate().plusDays(s.getFrequencyDays()));
                        mongoTemplate.save(s);
                    } else {
                        logger.error("PO creation failed for order {}", s.getOriginalOrderId());
                    }
                } catch (Exception ex) {
                    logger.error("Exception while processing scheduled order {}: {}", s.getOriginalOrderId(), ex.getMessage());
                }

            } else {
                logger.info("Skipping order {} due to unavailable parts: {}", s.getOriginalOrderId(), unavailableParts);
                notificationService.sendOutOfStockEmail(s.getCustomerId(), unavailableParts);
            }
        }
    }*/

    /*@Scheduled(fixedRate = 3600 * 60000) // every 1 minute
    public void processScheduledOrders() {
        LocalDate now = LocalDate.now();
        Query query = new Query();
        query.addCriteria(Criteria.where("active").is(true).and("nextRunDate").is(now));

        List<ScheduledOrder> dueOrders = mongoTemplate.find(query, ScheduledOrder.class);
        List<String> globalUnavailableItems = new ArrayList<>();

        for (ScheduledOrder s : dueOrders) {
            KarmakPurchaseOrder purchaseOrder = purchaseOrderService.getPurchaseOrderByExternalId(s.getOriginalOrderId());

            if (purchaseOrder == null) {
                logger.warn("No purchase order found for external ID {}", s.getOriginalOrderId());
                continue;
            }

            ComplexValues complexValuesContainer = purchaseOrder.getComplexValues();
            if (complexValuesContainer == null || complexValuesContainer.getPurchaseOrders() == null) {
                logger.warn("No complex values found in purchase order {}", s.getOriginalOrderId());
                continue;
            }

            List<PurchaseOrder> purchaseOrders = complexValuesContainer.getPurchaseOrders();
            List<String> unavailableParts = new ArrayList<>();
            List<InternalPurchaseOrderRequest.InternalLineItem> internalLineItems = new ArrayList<>();

            for (PurchaseOrder poItem : purchaseOrders) {
                Values values = poItem.getValues();
                if (values == null) {
                    logger.warn("Skipping purchase order item with null values.");
                    continue;
                }

                // Extract part number safely
                String partNumber = Optional.ofNullable(values.getOrderPartNumber())
                        .map(OrderPartNumber::getContent)
                        .flatMap(contentList -> contentList.stream().findFirst())
                        .map(Content::getValue)
                        .map(Object::toString)
                        .orElse("N/A");

                if (globalUnavailableItems.contains(partNumber)) {
                    unavailableParts.add(partNumber);
                    continue;
                }

                CompletableFuture<Map<String, String>> resultFuture = getPriceAndQuantityForProductAsync(partNumber);
                Map<String, String> result = resultFuture.join();

                String availableQty = result.get(Constants.QUANTITY);
                String price = result.get(Constants.PRICE);
                String branch = result.get(Constants.BRANCH_CODE);

                if (availableQty == null || Integer.parseInt(availableQty) <= 0) {
                    unavailableParts.add(partNumber);
                    globalUnavailableItems.add(partNumber);
                    continue;
                }

                // Map to InternalLineItem
                InternalPurchaseOrderRequest.InternalLineItem lineItem = new InternalPurchaseOrderRequest.InternalLineItem();
                lineItem.setPartID(64); // TODO: lookup or map correct part ID if needed
                lineItem.setQuantity(Integer.parseInt(availableQty));
                lineItem.setCost(Double.parseDouble(price));
                lineItem.setMessage("Scheduled order");
                lineItem.setBranch(branch);
                lineItem.setTotalQuantity(Integer.parseInt(availableQty));

                internalLineItems.add(lineItem);
            }

            if (unavailableParts.isEmpty() && !internalLineItems.isEmpty()) {
                InternalPurchaseOrderRequest internalRequest = new InternalPurchaseOrderRequest();
                internalRequest.setPoDate(LocalDate.now());
                internalRequest.setLineItems(internalLineItems);
                internalRequest.setSupplierID(0); // set appropriately
                internalRequest.setApVendorID(0); // set appropriately
                internalRequest.setLocationID(1); // set appropriately
                internalRequest.setFillingLocationID(1); // set appropriately
                internalRequest.setOrderedBy("system@scheduler");
                internalRequest.setDirectShip(false);

                try {
                    PurchaseOrderResponse response = createPurchaseOrder(internalRequest);
                    if (response != null ) {
                        logger.info("PO created successfully: {}", response.getPoNumber());

                        // Call external API to place the order
                        placeOrder(purchaseOrder);

                        // Schedule next run
                        s.setNextRunDate(s.getNextRunDate().plusDays(s.getFrequencyDays()));
                        mongoTemplate.save(s);
                    } else {
                        logger.error("PO creation failed for order {}", s.getOriginalOrderId());
                    }
                } catch (Exception ex) {
                    logger.error("Exception while processing scheduled order {}: {}", s.getOriginalOrderId(), ex.getMessage(), ex);
                }
            } else {
                logger.info("Skipping order {} due to unavailable parts: {}", s.getOriginalOrderId(), unavailableParts);
                notificationService.sendOutOfStockEmail(s.getCustomerId(), unavailableParts);
            }
        }
    }*/



    /*@Scheduled(fixedRate = 60000) // every 1 minute
    public void processScheduledOrders() {
        LocalDateTime now = LocalDateTime.now();

        Query query = new Query();
        query.addCriteria(Criteria.where("active").is(true).and("nextRunDate").lte(now));

        //Step 1
        List<ScheduledOrder> dueOrders = mongoTemplate.find(query, ScheduledOrder.class);

        List<String> unavailableItems = new ArrayList<>();

        for (ScheduledOrder s : dueOrders) {
            //List<OrderItem> originalItems = orderService.getOrderItems(s.getOriginalOrderId());
            DataEntity entity = service.getDataEntityById(s.getOriginalOrderId(), EntityType.DE, Boolean.FALSE, Boolean.FALSE,
                    Boolean.FALSE, Boolean.FALSE);

            Collection<List<ComplexValue>> complexValueList = entity.getNonNullComplexValues().values();

            List<String> unavailableParts = new ArrayList<>();
            List<PurchaseOrderRequest.LineItem> lineItems = new ArrayList<>();

            //outerLoop:
            for (List<ComplexValue> complexValues : complexValueList) {
                for (ComplexValue complex : complexValues) {
                    Map<String, Value> values = complex.getNonNullValues();

                    String orderProductDetails = values.get("OrderProductDetails") != null
                            ? values.get("OrderProductDetails").toString()
                            : "N/A";
                    String orderQuantity = values.get("OrderQuantity") != null ? values.get("OrderQuantity").toString()
                            : "N/A";
                    String orderCost = values.get("OrderCost") != null ? values.get("OrderCost").toString() : "N/A";
                    String partNumber = values.get("OrderPartNumber") != null ? values.get("OrderPartNumber").toString()
                            : "N/A";

                    Product product = ecommProductDao.getProductByPartNumber(partNumber);
                    String oderPrimaryURL = baseUrl + "/images/defaultPrimaryImage.jpg";

                    boolean allAvailable = true;


                    if (!unavailableItems.contains(partNumber)) {
                        CompletableFuture<Map<String, String>> resultFuture = getPriceAndQuantityForProductAsync(partNumber);
                        Map<String, String> result = resultFuture.join();

                        String qtyStr = result.get("available");
                        if (qtyStr == null || Integer.parseInt(qtyStr) <= 0) {
                            unavailableParts.add(partNumber);
                            unavailableItems.add(partNumber);

                            break;

                            //continue outerLoop;
                        } else {
                            PurchaseOrderRequest.LineItem lineItem = new PurchaseOrderRequest.LineItem();
                            lineItem.setPartID(64);
                            int quantity = Integer.parseInt(qtyStr);
                            double price = Double.parseDouble(result.get("PRICE"));
                            lineItem.setQuantity(quantity);
                            lineItem.setCost(price * quantity);
                            lineItem.setMessage("Scheduled order");
                            lineItems.add(lineItem);
                        }
                    }
                    if (unavailableParts.size() == 0) {


                        // All parts available
                        PurchaseOrderRequest poRequest = new PurchaseOrderRequest();
                        //poRequest.se(s.getCustomerId());
                        poRequest.setPoDate(LocalDate.now());
                        poRequest.setLineItems(lineItems);

                        PurchaseOrderResponse response = createPurchaseOrder(poRequest);
                        if (response != null && response.getPoNumber() != null) {
                            logger.info("PO created successfully: {}", response.getPoNumber());

                            // Call internal order placement
                            orderService.placeOrder(s.getCustomerId(), response.getPoNumber());

                            // Update next run
                            s.setNextRunDate(s.getNextRunDate().plusDays(s.getFrequencyDays()));
                            mongoTemplate.save(s);
                        } else {
                            logger.error("PO creation failed, skipping order placement");
                        }
                    }else {
                        logger.info("Skipping order placement for order {} as some items are unavailable", s.getOriginalOrderId());
                        logger.info("Unavailable items: {}", unavailableParts);
                        notificationService.sendOutOfStockEmail(s.getCustomerId(), unavailableParts);
                    }
                }
            }


        }
    }*/
}
