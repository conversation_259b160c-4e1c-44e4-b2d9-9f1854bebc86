package com.atp.product.service.impl;

import com.atp.product.controller.dto.response.ProductResponseHomePage;
import com.atp.product.exception.DomainException;
import com.atp.product.model.RecentlyViewed;
import com.atp.product.service.ProductService;
import com.atp.product.service.RecentlyViewedService;
import com.atp.product.utils.AuthorizationUtils;
import com.atp.product.utils.Constants;
import com.mongodb.client.result.UpdateResult;
import jakarta.servlet.http.HttpServletRequest;
import lombok.NonNull;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class RecentlyViewedServiceImpl implements RecentlyViewedService {
    private static final Logger logger = LoggerFactory.getLogger(RecentlyViewedServiceImpl.class);
    private static final int MAX_RECENTLY_VIEWED = 20;

    @Autowired
    private MongoTemplate mongoTemplate;
    
    @Autowired
    private ProductService productService;

    @Autowired
    private CommonServiceImpl commonServiceImpl;

    @Autowired
    private  HttpServletRequest request;

    @Override
    public void recordProductView(String customerCorrelationId, String partNumberSlug) {
        logger.info("Recording product view for customer: {}, product slug: {}", customerCorrelationId, partNumberSlug);
        try {
            // First check if this product is already in the recently viewed list
            Query findQuery = new Query(Criteria.where("customerCorrelationId").is(customerCorrelationId)
                    .and("products.partNumberSlug").is(partNumberSlug));
            
            // If it exists, remove it so we can add it again at the top (most recent)
            Update pullUpdate = new Update().pull("products", 
                    new Query().addCriteria(
                            Criteria.where("partNumberSlug").is(partNumberSlug)));
            mongoTemplate.updateFirst(findQuery, pullUpdate, RecentlyViewed.class);

            // Create a new ViewedProduct object
            RecentlyViewed.ViewedProduct viewedProduct = new RecentlyViewed.ViewedProduct();
            viewedProduct.setPartNumberSlug(partNumberSlug);
            viewedProduct.setViewedAt(LocalDateTime.now());
            
            // Convert the ViewedProduct to a Document
            Document viewedProductDoc = new Document();
            viewedProductDoc.put("partNumberSlug", partNumberSlug);
            viewedProductDoc.put("viewedAt", viewedProduct.getViewedAt());

            // Use $push with $each and $slice to add the product and limit the array size
            Document pushOperation = new Document("$push",
                new Document("products",
                    new Document("$each", List.of(viewedProductDoc))
                        .append("$slice", -MAX_RECENTLY_VIEWED)
                )
            );
            
            mongoTemplate.getCollection(mongoTemplate.getCollectionName(RecentlyViewed.class))
                .updateOne(
                    new Document("customerCorrelationId", customerCorrelationId),
                    pushOperation,
                    new com.mongodb.client.model.UpdateOptions().upsert(true)
                );
        } catch (Exception e) {
            logger.error("Error recording product view", e);
            throw new DomainException("Failed to record product view", e.getMessage());
        }
    }

    @Override
    public List<String> getRecentlyViewedPartNumberSlugs(String customerCorrelationId, int limit) {
        logger.info("Getting recently viewed part number slugs for customer: {}", customerCorrelationId);
        try {
            List<AggregationOperation> operations = new ArrayList<>();

            // Match the customer
            operations.add(Aggregation.match(
                    Criteria.where("customerCorrelationId").is(customerCorrelationId)
            ));
            
            // Unwind the products array
            operations.add(Aggregation.unwind("products"));

            // Sort by viewedAt descending
            operations.add(Aggregation.sort(Sort.Direction.DESC, "products.viewedAt"));

            // Project only the partNumberSlug
            operations.add(Aggregation.project("products.partNumberSlug"));

            // Limit to requested number
            operations.add(Aggregation.limit(limit));
            
            Aggregation aggregation = Aggregation.newAggregation(operations);
            logger.info("Aggregation pipeline getRecentlyViewedPartNumberSlugs(): {}", aggregation);
            
            List<RecentlyViewed.ViewedProduct> results = mongoTemplate.aggregate(
                    aggregation,
                    "recently_viewed",
                    RecentlyViewed.ViewedProduct.class
            ).getMappedResults();

            return results.stream()
                    .map(RecentlyViewed.ViewedProduct::getPartNumberSlug)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Error getting recently viewed part number slugs", e);
            throw new DomainException("Failed to get recently viewed part number slugs", e.getMessage());
        }
    }

    @Override
    public List<ProductResponseHomePage> getRecentlyViewedProductDetails(String customerCorrelationId, int limit) {
        logger.info("Inside call to getRecentlyViewedProductDetails() service with customerCorrelationId: {}", customerCorrelationId);
        try {
            // Get the list of recently viewed part number slugs
            List<String> partNumberSlugs = getRecentlyViewedPartNumberSlugs(customerCorrelationId, limit);

            if (partNumberSlugs.isEmpty()) {
                logger.info("No recently viewed products found for customer: {}", customerCorrelationId);
                return new ArrayList<>();
            }

            List<AggregationOperation> operations = new ArrayList<>();

            // Match products with the given part number slugs
            MatchOperation matchOperation = Aggregation.match(Criteria.where(Constants.IS_DELETED).is(false)
                    .and(Constants.ACTIVE).is(true)
                    .and(Constants.PART_NUMBER_SLUG).in(partNumberSlugs));
            operations.add(matchOperation);

            // Add lookup for wishlist if user is authenticated
            if (AuthorizationUtils.isAuthorizationHeaderValid(request)) {
                String userCorrelationId = AuthorizationUtils.getCustomerCorrelationId(request);
                operations.add(getLookupOperation(userCorrelationId));
                operations.add(getAddFieldsOperation());
            }

            // Project only the fields needed for ProductResponseHomePage
            ProjectionOperation projectionOperation = getProjectionOperation();
            operations.add(projectionOperation);

            // Sort by last modified date
            SortOperation sortOperation = new SortOperation(Sort.by(Sort.Direction.DESC, Constants.LAST_MODIFIED_DATE));
            operations.add(sortOperation);

            // Create and execute aggregation
            Aggregation aggregation = Aggregation.newAggregation(operations);
            logger.info("Aggregation pipeline getRecentlyViewedProductDetails(): {}", aggregation);

            List<ProductResponseHomePage> results = mongoTemplate.aggregate(
                    aggregation,
                    Constants.PRODUCT_COLLECTION_NAME,
                    ProductResponseHomePage.class
            ).getMappedResults();

            logger.info("Found {} products for recently viewed items", results.size());

            if (results.isEmpty()) {
                return results;
            }

            // Process images and other product details
            results = commonServiceImpl.resizeImages(results);

            // Create a new mutable list to avoid UnsupportedOperationException
            List<ProductResponseHomePage> sortedResults = new ArrayList<>(results);

            // Sort results to match the order of recently viewed products
            Map<String, Integer> slugOrder = new HashMap<>();
            for (int i = 0; i < partNumberSlugs.size(); i++) {
                slugOrder.put(partNumberSlugs.get(i), i);
            }

            sortedResults.sort(Comparator.comparing(product ->
                    slugOrder.getOrDefault(product.getPartNumberSlug(), Integer.MAX_VALUE)));

            return sortedResults;
        } catch (IllegalArgumentException e) {
            logger.error("Invalid argument provided: {}", e.getMessage(), e);
            throw new DomainException(Constants.INVALID_ARGUMENT_PROVIDED, e);
        } catch (Exception e) {
            logger.error("Error retrieving recently viewed products: {}", e.getMessage(), e);
            throw new DomainException("An error occurred while retrieving recently viewed products", e.getMessage());
        }

    }

    public ProjectionOperation getProjectionOperation(){
        try {
            return Aggregation.project()
                    .and(Constants.CATALOG_ID).as("productId")
                    .and(Constants.NAME).as("productName")
                    .and(Constants.CATEGORY).as(Constants.CATEGORY_NAME)
                    .and(Constants.PRIMARY_IMAGE).as(Constants.PRIMARY_IMAGE)
                    .and(Constants.LAST_MODIFIED_DATE).as(Constants.LAST_MODIFIED_DATE)
                    .and(Constants.PART_NUMBER).as(Constants.PART_NUMBER)
                    .and(Constants.PART_NUMBER_SLUG).as(Constants.PART_NUMBER_SLUG)
                    .and(Constants.ASSET_MAP).as(Constants.ASSET_MAP)
                    .and(Constants.DESCRIPTION).as(Constants.DESCRIPTION)
                    .and(Constants.WISHLIST_FLAG).as(Constants.WISHLIST_FLAG)
                    .and(Constants.PRICE).as(Constants.PRICE)
                    .and(Constants.VENDOR_NAME).as(Constants.VENDOR_NAME)
                    .and(Constants.CROSS_REFERENCE_ID).as(Constants.CROSS_REFERENCE_ID)
                    ;
        }
        catch (Exception e) {
            throw new DomainException("An error occurred while creating the projection operation", e.getMessage());
        }
    }

    public AggregationOperation getAddFieldsOperation(){
        try {
            Document addFieldsStage = new Document("$addFields", new Document(Constants.WISHLIST_FLAG,
                    new Document("$gt", List.of(
                            new Document("$size", "$wishlistEntry"), 0
                    ))
            ));
            return commonServiceImpl.documentToAggregationOperation(addFieldsStage);
        }
        catch (Exception e) {
            throw new DomainException("An error occurred while creating the addFields operation", e.getMessage());
        }
    }

    public AggregationOperation getLookupOperation(String customerCorrelationId){
        try{
            Document lookupStage = new Document("$lookup", new Document()
                    .append("from", Constants.WISHLIST_COLLECTION_NAME)
                    .append("let", new Document(Constants.PART_NUMBER, "$"+Constants.PART_NUMBER))
                    .append("pipeline", List.of(
                            new Document("$match", new Document("$expr", new Document("$and", List.of(
                                    new Document("$eq", List.of("$customerCorrelationId", customerCorrelationId)),
                                    new Document("$in", List.of("$$"+Constants.PART_NUMBER, "$productDetails.partNumber"))
                            ))))
                    ))
                    .append("as", "wishlistEntry")
            );
            return documentToAggregationOperation(lookupStage);
        } catch (Exception e) {
            throw new DomainException("An error occurred while retrieving products by item type", e.getMessage());
        }
    }
    public AggregationOperation documentToAggregationOperation(@NonNull Document document) {
        return context -> document;
    }

}