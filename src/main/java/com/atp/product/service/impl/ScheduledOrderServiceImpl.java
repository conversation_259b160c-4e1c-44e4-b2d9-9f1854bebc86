package com.atp.product.service.impl;

import com.atp.product.controller.dto.request.InternalPurchaseOrderRequest;
import com.atp.product.controller.dto.request.ScheduledOrderRequest;
import com.atp.product.controller.dto.request.UpdateScheduleRequest;
import com.atp.product.controller.dto.response.PurchaseOrderResponse;
import com.atp.product.controller.dto.response.ScheduledOrderResponse;
import com.atp.product.karmak_responses.*;
import com.atp.product.karmak_responses.OrderPartNumber;
import com.atp.product.karmak_responses.Content;
import com.atp.product.model.ScheduledOrder;
import com.atp.product.service.NotificationService;
import com.atp.product.service.ScheduledOrderService;
import com.atp.product.utils.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.io.IOException;

import okhttp3.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.ArrayNode;

/**
 * Implementation of ScheduledOrderService with optimized performance
 */
@Slf4j
@Service
public class ScheduledOrderServiceImpl implements ScheduledOrderService {

    private final MongoTemplate mongoTemplate;
    private final PurchaseOrderService purchaseOrderService;
    private final NotificationService notificationService;
    private final CommonServiceImpl commonService;
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;

    // Configuration properties for third-party API
    @Value("${third-party.api.url:http://localhost:8080/server/api/data-entities/create}")
    private String thirdPartyApiUrl;

    @Value("${third-party.api.authorization:}")
    private String authorizationToken;

    @Value("${third-party.api.timeout:30}")
    private int apiTimeoutSeconds;

    @Autowired
    public ScheduledOrderServiceImpl(MongoTemplate mongoTemplate,
                                   PurchaseOrderService purchaseOrderService,
                                   NotificationService notificationService,
                                   CommonServiceImpl commonService) {
        this.mongoTemplate = mongoTemplate;
        this.purchaseOrderService = purchaseOrderService;
        this.notificationService = notificationService;
        this.commonService = commonService;

        // Initialize HTTP client with default timeout (will be configured via properties)
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                .writeTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                .readTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                .build();

        this.objectMapper = new ObjectMapper();
    }

    @Override
    public ScheduledOrderResponse subscribe(ScheduledOrderRequest request) {
        log.info("Creating scheduled order subscription for customer: {}", request.getCustomerId());
        
        try {
            // Validate that the original order exists
            KarmakPurchaseOrder originalOrder = purchaseOrderService.getPurchaseOrderByExternalId(request.getOriginalOrderId());
            if (originalOrder == null) {
                throw new IllegalArgumentException("Original order not found: " + request.getOriginalOrderId());
            }

            // Create new scheduled order
            ScheduledOrder scheduledOrder = new ScheduledOrder();
            scheduledOrder.setCustomerId(request.getCustomerId());
            scheduledOrder.setOriginalOrderId(request.getOriginalOrderId());
            scheduledOrder.setFrequencyDays(request.getFrequencyDays());
            scheduledOrder.setSubscriptionDate(LocalDate.now());
            scheduledOrder.setNextRunDate(request.getStartDate() != null ? request.getStartDate() : LocalDate.now().plusDays(request.getFrequencyDays()));
            scheduledOrder.setEndDate(request.getEndDate());
            scheduledOrder.setNotes(request.getNotes());
            scheduledOrder.setAutoRenew(request.getAutoRenew());
            scheduledOrder.setMaxOrders(request.getMaxOrders());
            scheduledOrder.setNotifyOnOutOfStock(request.getNotifyOnOutOfStock());
            scheduledOrder.setNotifyOnSuccess(request.getNotifyOnSuccess());
            scheduledOrder.setActive(true);
            scheduledOrder.setStatus("ACTIVE");
            scheduledOrder.setCreatedAt(LocalDateTime.now());
            scheduledOrder.setUpdatedAt(LocalDateTime.now());

            // Save to database
            mongoTemplate.save(scheduledOrder);

            // Send confirmation email
            if (request.getNotifyOnSuccess()) {
                notificationService.sendScheduledOrderConfirmation(
                    request.getCustomerId(), 
                    scheduledOrder.getId(), 
                    scheduledOrder.getNextRunDate().toString()
                );
            }

            log.info("Scheduled order created successfully with ID: {}", scheduledOrder.getId());
            return mapToResponse(scheduledOrder);

        } catch (Exception e) {
            log.error("Failed to create scheduled order for customer: {}", request.getCustomerId(), e);
            throw new RuntimeException("Failed to create scheduled order: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean cancel(String scheduledOrderId, String customerId) {
        log.info("Cancelling scheduled order: {} for customer: {}", scheduledOrderId, customerId);
        
        try {
            Query query = new Query(Criteria.where("id").is(scheduledOrderId)
                                          .and("customerId").is(customerId)
                                          .and("active").is(true));
            
            Update update = new Update()
                .set("active", false)
                .set("status", "CANCELLED")
                .set("updatedAt", LocalDateTime.now());
            
            var result = mongoTemplate.updateFirst(query, update, ScheduledOrder.class);
            
            if (result.getModifiedCount() > 0) {
                // Send cancellation email
                notificationService.sendScheduledOrderCancellation(customerId, scheduledOrderId);
                log.info("Scheduled order cancelled successfully: {}", scheduledOrderId);
                return true;
            } else {
                log.warn("Scheduled order not found or already cancelled: {}", scheduledOrderId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("Failed to cancel scheduled order: {}", scheduledOrderId, e);
            return false;
        }
    }

    @Override
    public ScheduledOrderResponse updateSchedule(String scheduledOrderId, UpdateScheduleRequest request) {
        log.info("Updating scheduled order: {}", scheduledOrderId);
        
        try {
            Query query = new Query(Criteria.where("id").is(scheduledOrderId)
                                          .and("customerId").is(request.getCustomerId())
                                          .and("active").is(true));
            
            Update update = new Update().set("updatedAt", LocalDateTime.now());
            
            // Update only provided fields
            if (request.getFrequencyDays() != null) {
                update.set("frequencyDays", request.getFrequencyDays());
            }
            if (request.getNextRunDate() != null) {
                update.set("nextRunDate", request.getNextRunDate());
            }
            if (request.getEndDate() != null) {
                update.set("endDate", request.getEndDate());
            }
            if (request.getNotes() != null) {
                update.set("notes", request.getNotes());
            }
            if (request.getAutoRenew() != null) {
                update.set("autoRenew", request.getAutoRenew());
            }
            if (request.getMaxOrders() != null) {
                update.set("maxOrders", request.getMaxOrders());
            }
            if (request.getNotifyOnOutOfStock() != null) {
                update.set("notifyOnOutOfStock", request.getNotifyOnOutOfStock());
            }
            if (request.getNotifyOnSuccess() != null) {
                update.set("notifyOnSuccess", request.getNotifyOnSuccess());
            }
            if (request.getActive() != null) {
                update.set("active", request.getActive());
                update.set("status", request.getActive() ? "ACTIVE" : "PAUSED");
            }
            
            var result = mongoTemplate.updateFirst(query, update, ScheduledOrder.class);
            
            if (result.getModifiedCount() > 0) {
                // Get updated order
                ScheduledOrder updatedOrder = mongoTemplate.findOne(query, ScheduledOrder.class);
                
                // Send update notification
                if (request.getFrequencyDays() != null) {
                    notificationService.sendScheduledOrderUpdate(
                        request.getCustomerId(), 
                        scheduledOrderId, 
                        request.getFrequencyDays()
                    );
                }
                
                log.info("Scheduled order updated successfully: {}", scheduledOrderId);
                return mapToResponse(updatedOrder);
            } else {
                log.warn("Scheduled order not found for update: {}", scheduledOrderId);
                return null;
            }
            
        } catch (Exception e) {
            log.error("Failed to update scheduled order: {}", scheduledOrderId, e);
            throw new RuntimeException("Failed to update scheduled order: " + e.getMessage(), e);
        }
    }

    @Override
    public List<ScheduledOrderResponse> getScheduledOrdersByCustomer(String customerId) {
        log.info("Retrieving scheduled orders for customer: {}", customerId);
        
        try {
            Query query = new Query(Criteria.where("customerId").is(customerId));
            List<ScheduledOrder> orders = mongoTemplate.find(query, ScheduledOrder.class);
            
            return orders.stream()
                        .map(this::mapToResponse)
                        .collect(Collectors.toList());
                        
        } catch (Exception e) {
            log.error("Failed to retrieve scheduled orders for customer: {}", customerId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public ScheduledOrderResponse getScheduledOrderById(String scheduledOrderId, String customerId) {
        log.info("Retrieving scheduled order: {} for customer: {}", scheduledOrderId, customerId);
        
        try {
            Query query = new Query(Criteria.where("id").is(scheduledOrderId)
                                          .and("customerId").is(customerId));
            
            ScheduledOrder order = mongoTemplate.findOne(query, ScheduledOrder.class);
            return order != null ? mapToResponse(order) : null;
            
        } catch (Exception e) {
            log.error("Failed to retrieve scheduled order: {}", scheduledOrderId, e);
            return null;
        }
    }

    @Override
    public boolean pauseScheduledOrder(String scheduledOrderId, String customerId) {
        log.info("Pausing scheduled order: {} for customer: {}", scheduledOrderId, customerId);
        
        try {
            Query query = new Query(Criteria.where("id").is(scheduledOrderId)
                                          .and("customerId").is(customerId)
                                          .and("active").is(true));
            
            Update update = new Update()
                .set("paused", true)
                .set("status", "PAUSED")
                .set("updatedAt", LocalDateTime.now());
            
            var result = mongoTemplate.updateFirst(query, update, ScheduledOrder.class);
            
            if (result.getModifiedCount() > 0) {
                log.info("Scheduled order paused successfully: {}", scheduledOrderId);
                return true;
            } else {
                log.warn("Scheduled order not found for pausing: {}", scheduledOrderId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("Failed to pause scheduled order: {}", scheduledOrderId, e);
            return false;
        }
    }

    @Override
    public boolean resumeScheduledOrder(String scheduledOrderId, String customerId) {
        log.info("Resuming scheduled order: {} for customer: {}", scheduledOrderId, customerId);
        
        try {
            Query query = new Query(Criteria.where("id").is(scheduledOrderId)
                                          .and("customerId").is(customerId)
                                          .and("active").is(true));
            
            Update update = new Update()
                .set("paused", false)
                .set("status", "ACTIVE")
                .set("updatedAt", LocalDateTime.now());
            
            var result = mongoTemplate.updateFirst(query, update, ScheduledOrder.class);
            
            if (result.getModifiedCount() > 0) {
                log.info("Scheduled order resumed successfully: {}", scheduledOrderId);
                return true;
            } else {
                log.warn("Scheduled order not found for resuming: {}", scheduledOrderId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("Failed to resume scheduled order: {}", scheduledOrderId, e);
            return false;
        }
    }

    @Override
    public List<ScheduledOrderResponse> getAllActiveScheduledOrders() {
        log.info("Retrieving all active scheduled orders");
        
        try {
            Query query = new Query(Criteria.where("active").is(true));
            List<ScheduledOrder> orders = mongoTemplate.find(query, ScheduledOrder.class);
            
            return orders.stream()
                        .map(this::mapToResponse)
                        .collect(Collectors.toList());
                        
        } catch (Exception e) {
            log.error("Failed to retrieve all active scheduled orders", e);
            return new ArrayList<>();
        }
    }

    private ScheduledOrderResponse mapToResponse(ScheduledOrder order) {
        ScheduledOrderResponse response = new ScheduledOrderResponse();
        response.setId(order.getId());
        response.setCustomerId(order.getCustomerId());
        response.setOriginalOrderId(order.getOriginalOrderId());
        response.setSubscriptionDate(order.getSubscriptionDate());
        response.setFrequencyDays(order.getFrequencyDays());
        response.setNextRunDate(order.getNextRunDate());
        response.setEndDate(order.getEndDate());
        response.setActive(order.isActive());
        response.setPaused(order.isPaused());
        response.setNotes(order.getNotes());
        response.setAutoRenew(order.getAutoRenew());
        response.setMaxOrders(order.getMaxOrders());
        response.setOrdersProcessed(order.getOrdersProcessed());
        response.setLastProcessedDate(order.getLastProcessedDate());
        response.setNotifyOnOutOfStock(order.getNotifyOnOutOfStock());
        response.setNotifyOnSuccess(order.getNotifyOnSuccess());
        response.setStatus(order.getStatus());
        response.setCreatedAt(order.getCreatedAt());
        response.setUpdatedAt(order.getUpdatedAt());
        return response;
    }

    /**
     * Optimized scheduled order processing (moved from CommonServiceImpl)
     */
    @Override
    @Scheduled(fixedRate = 3600 * 60000) // every 1 hour
    public void processScheduledOrders() {
        log.info("Starting scheduled order processing...");

        LocalDate now = LocalDate.now();
        // FIXED: Use exact date match like original code
        Query query = new Query(Criteria.where("active").is(true)
                                      .and("paused").ne(true)
                                      .and("nextRunDate").is(now));

        List<ScheduledOrder> dueOrders = mongoTemplate.find(query, ScheduledOrder.class);
        log.info("Found {} scheduled orders due for processing", dueOrders.size());

        if (dueOrders.isEmpty()) {
            log.info("No scheduled orders due for processing");
            return;
        }

        // FIXED: Add global unavailable items tracking like original (thread-safe for parallel processing)
        List<String> globalUnavailableItems = Collections.synchronizedList(new ArrayList<>());

        // Process orders in parallel for better performance
        List<CompletableFuture<Void>> futures = dueOrders.stream()
                .map(order -> processScheduledOrderAsync(order, globalUnavailableItems))
                .collect(Collectors.toList());

        // Wait for all orders to complete processing
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenRun(() -> log.info("Completed processing {} scheduled orders", dueOrders.size()))
                .exceptionally(throwable -> {
                    log.error("Error during batch processing of scheduled orders", throwable);
                    return null;
                });
    }

    /**
     * Process a single scheduled order asynchronously
     */
    private CompletableFuture<Void> processScheduledOrderAsync(ScheduledOrder scheduledOrder,
                                                              List<String> globalUnavailableItems) {
        return CompletableFuture.runAsync(() -> {
            try {
                processSingleScheduledOrder(scheduledOrder, globalUnavailableItems);
            } catch (Exception e) {
                log.error("Failed to process scheduled order: {}", scheduledOrder.getId(), e);

                // Send failure notification
                notificationService.sendPurchaseOrderFailure(
                    scheduledOrder.getCustomerId(),
                    scheduledOrder.getId(),
                    e.getMessage()
                );
            }
        });
    }

    /**
     * Process a single scheduled order
     */
    private void processSingleScheduledOrder(ScheduledOrder scheduledOrder,
                                           List<String> globalUnavailableItems) {
        log.info("Processing scheduled order: {} for customer: {}",
                scheduledOrder.getId(), scheduledOrder.getCustomerId());

        // Check if order has reached max orders limit
        if (scheduledOrder.getMaxOrders() != null &&
            scheduledOrder.getOrdersProcessed() >= scheduledOrder.getMaxOrders()) {
            log.info("Scheduled order {} has reached max orders limit, marking as completed",
                    scheduledOrder.getId());
            markOrderAsCompleted(scheduledOrder);
            return;
        }

        // Check if order has passed end date
        if (scheduledOrder.getEndDate() != null &&
            LocalDate.now().isAfter(scheduledOrder.getEndDate())) {
            log.info("Scheduled order {} has passed end date, marking as completed",
                    scheduledOrder.getId());
            markOrderAsCompleted(scheduledOrder);
            return;
        }

        // Get the original purchase order
        KarmakPurchaseOrder purchaseOrder = purchaseOrderService.getPurchaseOrderByExternalId(
                scheduledOrder.getOriginalOrderId());

        if (purchaseOrder == null) {
            log.warn("No purchase order found for external ID {}", scheduledOrder.getOriginalOrderId());
            return;
        }

        // Extract and validate complex values
        ComplexValues complexValuesContainer = purchaseOrder.getComplexValues();
        if (complexValuesContainer == null || complexValuesContainer.getPurchaseOrders() == null) {
            log.warn("No complex values found in purchase order {}", scheduledOrder.getOriginalOrderId());
            return;
        }

        List<PurchaseOrder> purchaseOrders = complexValuesContainer.getPurchaseOrders();
        List<String> unavailableParts = new ArrayList<>();
        List<InternalPurchaseOrderRequest.InternalLineItem> internalLineItems = new ArrayList<>();

        // Process each purchase order item
        for (PurchaseOrder poItem : purchaseOrders) {
            Values values = poItem.getValues();
            if (values == null) {
                log.warn("Skipping purchase order item with null values.");
                continue;
            }

            // FIXED: Extract part number safely like original code
            String partNumber = Optional.ofNullable(values.getOrderPartNumber())
                    .map(OrderPartNumber::getContent)
                    .flatMap(contentList -> contentList.stream().findFirst())
                    .map(Content::getValue)
                    .map(Object::toString)
                    .orElse("N/A");

            // FIXED: Check global unavailable items first like original
            if (globalUnavailableItems.contains(partNumber)) {
                unavailableParts.add(partNumber);
                continue;
            }

            // Check availability and pricing
            try {
                CompletableFuture<Map<String, String>> resultFuture = commonService.getPriceAndQuantityForProductAsync(partNumber);
                Map<String, String> result = resultFuture.join();

                // FIXED: Use correct constants like original code
                String availableQty = result.get(Constants.QUANTITY);
                String price = result.get(Constants.PRICE);
                String branch = result.get(Constants.BRANCH_CODE);

                if (availableQty == null || Integer.parseInt(availableQty) <= 0) {
                    unavailableParts.add(partNumber);
                    globalUnavailableItems.add(partNumber); // FIXED: Add to global list
                    continue;
                }

                // FIXED: Map to InternalLineItem like original code
                InternalPurchaseOrderRequest.InternalLineItem lineItem =
                        new InternalPurchaseOrderRequest.InternalLineItem();
                lineItem.setPartID(64); // TODO: lookup or map correct part ID if needed
                lineItem.setQuantity(Integer.parseInt(values.getOrderQuantity().toString())); // FIXED: Use available quantity
                lineItem.setCost(Double.parseDouble(price)); // FIXED: Use price directly
                lineItem.setMessage("Scheduled order");
                lineItem.setBranch(branch); // FIXED: Set branch from result
                lineItem.setTotalQuantity(Integer.parseInt(availableQty)); // FIXED: Use available quantity

                internalLineItems.add(lineItem);

            } catch (Exception e) {
                log.error("Error checking availability for part {} in order {}",
                         partNumber, scheduledOrder.getId(), e);
                unavailableParts.add(partNumber);
            }
        }

        // Process the order if all parts are available
        if (unavailableParts.isEmpty() && !internalLineItems.isEmpty()) {
            processAvailableOrder(scheduledOrder, internalLineItems, purchaseOrder);
        } else {
            handleUnavailableOrder(scheduledOrder, unavailableParts);
        }
    }

    /**
     * Process order when all parts are available
     */
    private void processAvailableOrder(ScheduledOrder scheduledOrder,
                                     List<InternalPurchaseOrderRequest.InternalLineItem> lineItems,
                                     KarmakPurchaseOrder originalOrder) {
        try {
            // FIXED: Create internal purchase order request like original code
            InternalPurchaseOrderRequest internalRequest = new InternalPurchaseOrderRequest();
            internalRequest.setPoDate(LocalDate.now());
            internalRequest.setLineItems(lineItems);
            internalRequest.setSupplierID(0); // FIXED: set appropriately like original
            internalRequest.setApVendorID(0); // FIXED: set appropriately like original
            internalRequest.setLocationID(1); // set appropriately
            internalRequest.setFillingLocationID(1); // set appropriately
            internalRequest.setOrderedBy("system@scheduler"); // FIXED: like original
            internalRequest.setDirectShip(false);

            // Create purchase order
            PurchaseOrderResponse response = commonService.createPurchaseOrder(internalRequest);

            if (response != null) {
                log.info("PO created successfully: {} for scheduled order: {}",
                        response.getPoNumber(), scheduledOrder.getId());

                // Place the order (external API call)
                boolean orderPlaced = placeOrder(originalOrder);
                if (!orderPlaced) {
                    log.warn("Failed to place order via external API for scheduled order: {}",
                            scheduledOrder.getId());
                    // Continue with processing even if external API fails
                }

                // Update scheduled order for next run
                updateScheduledOrderAfterSuccess(scheduledOrder);

                // Send success notification
                if (scheduledOrder.getNotifyOnSuccess()) {
                    notificationService.sendPurchaseOrderSuccess(
                        scheduledOrder.getCustomerId(),
                        response.getPoNumber(),
                        scheduledOrder.getId()
                    );
                }

            } else {
                log.error("PO creation failed for scheduled order {}", scheduledOrder.getId());

                // Send failure notification
                notificationService.sendPurchaseOrderFailure(
                    scheduledOrder.getCustomerId(),
                    scheduledOrder.getId(),
                    "Purchase order creation failed"
                );
            }

        } catch (Exception e) {
            log.error("Exception while processing available order {}: {}",
                     scheduledOrder.getId(), e.getMessage(), e);

            // Send failure notification
            notificationService.sendPurchaseOrderFailure(
                scheduledOrder.getCustomerId(),
                scheduledOrder.getId(),
                e.getMessage()
            );
        }
    }

    /**
     * Handle order when some parts are unavailable
     */
    private void handleUnavailableOrder(ScheduledOrder scheduledOrder, List<String> unavailableParts) {
        log.info("Skipping order {} due to unavailable parts: {}",
                scheduledOrder.getId(), unavailableParts);

        // Send out of stock notification if enabled
        if (scheduledOrder.getNotifyOnOutOfStock()) {
            notificationService.sendOutOfStockEmail(scheduledOrder.getCustomerId(), unavailableParts);
        }

        // Reschedule for next attempt (add 1 day to retry sooner)
        LocalDate nextRetry = LocalDate.now().plusDays(1);
        updateScheduledOrderNextRun(scheduledOrder, nextRetry);
    }

    /**
     * Update scheduled order after successful processing
     */
    private void updateScheduledOrderAfterSuccess(ScheduledOrder scheduledOrder) {
        Query query = new Query(Criteria.where("id").is(scheduledOrder.getId()));

        Update update = new Update()
            .set("nextRunDate", scheduledOrder.getNextRunDate().plusDays(scheduledOrder.getFrequencyDays()))
            .set("ordersProcessed", scheduledOrder.getOrdersProcessed() + 1)
            .set("lastProcessedDate", LocalDateTime.now())
            .set("updatedAt", LocalDateTime.now());

        mongoTemplate.updateFirst(query, update, ScheduledOrder.class);

        log.info("Updated scheduled order {} for next run on {}",
                scheduledOrder.getId(),
                scheduledOrder.getNextRunDate().plusDays(scheduledOrder.getFrequencyDays()));
    }

    /**
     * Update scheduled order next run date
     */
    private void updateScheduledOrderNextRun(ScheduledOrder scheduledOrder, LocalDate nextRunDate) {
        Query query = new Query(Criteria.where("id").is(scheduledOrder.getId()));

        Update update = new Update()
            .set("nextRunDate", nextRunDate)
            .set("updatedAt", LocalDateTime.now());

        mongoTemplate.updateFirst(query, update, ScheduledOrder.class);
    }

    /**
     * Mark order as completed
     */
    private void markOrderAsCompleted(ScheduledOrder scheduledOrder) {
        Query query = new Query(Criteria.where("id").is(scheduledOrder.getId()));

        Update update = new Update()
            .set("active", false)
            .set("status", "COMPLETED")
            .set("updatedAt", LocalDateTime.now());

        mongoTemplate.updateFirst(query, update, ScheduledOrder.class);

        log.info("Marked scheduled order {} as completed", scheduledOrder.getId());
    }

    /**
     * Extract part number from Values object
     */
    private String extractPartNumber(Values values) {
        if (values.getOrderPartNumber() != null &&
            values.getOrderPartNumber().getContent() != null &&
            !values.getOrderPartNumber().getContent().isEmpty()) {

            Content content = values.getOrderPartNumber().getContent().get(0);
            return content.getValue() != null ? content.getValue().toString() : null;
        }
        return null;
    }

    /**
     * Extract quantity from Values object
     */
    private String extractQuantity(Values values) {
        if (values.getOrderQuantity() != null &&
            values.getOrderQuantity().getContent() != null &&
            !values.getOrderQuantity().getContent().isEmpty()) {

            Content content = values.getOrderQuantity().getContent().get(0);
            return content.getValue() != null ? content.getValue().toString() : "1";
        }
        return "1"; // Default quantity
    }

    /**
     * Extract cost from Values object
     */
    private String extractCost(Values values) {
        if (values.getOrderCost() != null &&
            values.getOrderCost().getContent() != null &&
            !values.getOrderCost().getContent().isEmpty()) {

            Content content = values.getOrderCost().getContent().get(0);
            return content.getValue() != null ? content.getValue().toString() : "0.0";
        }
        return "0.0"; // Default cost
    }

    /**
     * Place order by calling third-party API
     * @param originalOrder The original KarmakPurchaseOrder to be placed
     * @return true if order was placed successfully, false otherwise
     */
    public boolean placeOrder(KarmakPurchaseOrder originalOrder) {
        log.info("Placing order for external ID: {}", originalOrder.getExternalId());

        try {
            // Build the request payload based on the original order
            ObjectNode requestPayload = buildOrderPayload(originalOrder);

            // Convert to JSON string
            String jsonPayload = objectMapper.writeValueAsString(requestPayload);
            log.debug("Order payload: {}", jsonPayload);

            // Create HTTP request
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, jsonPayload);

            Request.Builder requestBuilder = new Request.Builder()
                    .url(thirdPartyApiUrl)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json");

            // Add authorization header if configured
            if (authorizationToken != null && !authorizationToken.trim().isEmpty()) {
                requestBuilder.addHeader("Authorization", authorizationToken);
            }

            Request request = requestBuilder.build();

            // Execute the request
            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    log.info("Order placed successfully for external ID: {}. Response code: {}",
                            originalOrder.getExternalId(), response.code());

                    // Log response body for debugging (optional)
                    if (response.body() != null) {
                        String responseBody = response.body().string();
                        log.debug("API Response: {}", responseBody);
                    }

                    return true;
                } else {
                    log.error("Failed to place order for external ID: {}. Response code: {}, Message: {}",
                             originalOrder.getExternalId(), response.code(), response.message());

                    // Log error response body if available
                    if (response.body() != null) {
                        String errorBody = response.body().string();
                        log.error("Error response body: {}", errorBody);
                    }

                    return false;
                }
            }

        } catch (IOException e) {
            log.error("IO Exception while placing order for external ID: {}",
                     originalOrder.getExternalId(), e);
            return false;
        } catch (Exception e) {
            log.error("Unexpected error while placing order for external ID: {}",
                     originalOrder.getExternalId(), e);
            return false;
        }
    }

    /**
     * Build the order payload from KarmakPurchaseOrder
     * @param originalOrder The original order to convert
     * @return ObjectNode representing the API payload
     */
    private ObjectNode buildOrderPayload(KarmakPurchaseOrder originalOrder) {
        ObjectNode payload = objectMapper.createObjectNode();

        // Set basic fields
        payload.put("parentId", originalOrder.getParentId() != null ? originalOrder.getParentId() : "CSTM_1185");
        payload.put("entityType", originalOrder.getEntityType() != null ? originalOrder.getEntityType() : "DE");
        payload.put("workspaceId", originalOrder.getWorkspaceId() != null ? originalOrder.getWorkspaceId() : "Maintenance");
        payload.put("datatypeDefinitionId", originalOrder.getDatatypeDefinitionId() != null ? originalOrder.getDatatypeDefinitionId() : "PurchaseOrder");
        payload.set("simpleValues", objectMapper.createObjectNode());
        payload.put("completeness", originalOrder.getCompleteness() != null ? originalOrder.getCompleteness() : 0);
        payload.put("deleted", originalOrder.isDeleted());
        payload.put("systemEntity", originalOrder.isSystemEntity());
        payload.put("approvalStatus", originalOrder.getApprovalStatus() != null ? originalOrder.getApprovalStatus() : "NOT_APPROVED");
        payload.put("externalId", originalOrder.getExternalId() != null ? originalOrder.getExternalId() : "");
        payload.put("name", originalOrder.getName() != null ? originalOrder.getName() : "");

        // Build Values array
        ArrayNode valuesArray = buildValuesArray(originalOrder);
        payload.set("Values", valuesArray);

        // Build path array
        ArrayNode pathArray = buildPathArray(originalOrder);
        payload.set("path", pathArray);

        // Build ComplexValues
        ObjectNode complexValues = buildComplexValues(originalOrder);
        payload.set("ComplexValues", complexValues);

        return payload;
    }

    /**
     * Build the Values array for the API payload
     */
    private ArrayNode buildValuesArray(KarmakPurchaseOrder originalOrder) {
        ArrayNode valuesArray = objectMapper.createArrayNode();

        // Add ECM_OrderDate
        ObjectNode orderDate = objectMapper.createObjectNode();
        orderDate.put("attributeId", "ECM_OrderDate");
        ArrayNode orderDateContent = objectMapper.createArrayNode();
        ObjectNode orderDateValue = objectMapper.createObjectNode();
        orderDateValue.put("value", LocalDate.now().format(DateTimeFormatter.ofPattern("MM/dd/yyyy")));
        orderDateContent.add(orderDateValue);
        orderDate.set("content", orderDateContent);
        valuesArray.add(orderDate);

        // Add ECM_GrandTotal (calculate from complex values)
        double grandTotal = calculateGrandTotal(originalOrder);
        ObjectNode grandTotalNode = objectMapper.createObjectNode();
        grandTotalNode.put("attributeId", "ECM_GrandTotal");
        ArrayNode grandTotalContent = objectMapper.createArrayNode();
        ObjectNode grandTotalValue = objectMapper.createObjectNode();
        grandTotalValue.put("value", grandTotal);
        grandTotalContent.add(grandTotalValue);
        grandTotalNode.set("content", grandTotalContent);
        valuesArray.add(grandTotalNode);

        // Add OrderStatus
        ObjectNode orderStatus = objectMapper.createObjectNode();
        orderStatus.put("attributeId", "OrderStatus");
        ArrayNode orderStatusContent = objectMapper.createArrayNode();
        ObjectNode orderStatusValue = objectMapper.createObjectNode();
        orderStatusValue.put("value", "Order Placed/New Order");
        orderStatusContent.add(orderStatusValue);
        orderStatus.set("content", orderStatusContent);
        valuesArray.add(orderStatus);

        // Add BinLocation
        ObjectNode binLocation = objectMapper.createObjectNode();
        binLocation.put("attributeId", "BinLocation");
        ArrayNode binLocationContent = objectMapper.createArrayNode();
        ObjectNode binLocationValue = objectMapper.createObjectNode();
        binLocationValue.put("value", "");
        binLocationContent.add(binLocationValue);
        binLocation.set("content", binLocationContent);
        valuesArray.add(binLocation);

        // Add DeliveryMethod
        ObjectNode deliveryMethod = objectMapper.createObjectNode();
        deliveryMethod.put("attributeId", "DeliveryMethod");
        ArrayNode deliveryMethodContent = objectMapper.createArrayNode();
        ObjectNode deliveryMethodValue = objectMapper.createObjectNode();
        deliveryMethodValue.put("value", "Store Pick Up");
        deliveryMethodContent.add(deliveryMethodValue);
        deliveryMethod.set("content", deliveryMethodContent);
        valuesArray.add(deliveryMethod);

        return valuesArray;
    }

    /**
     * Build the path array for the API payload
     */
    private ArrayNode buildPathArray(KarmakPurchaseOrder originalOrder) {
        ArrayNode pathArray = objectMapper.createArrayNode();

        if (originalOrder.getPath() != null && !originalOrder.getPath().isEmpty()) {
            for (String pathElement : originalOrder.getPath()) {
                pathArray.add(pathElement);
            }
        } else {
            // Default path structure
            pathArray.add("MainEntityRoot");
            pathArray.add("CustomersHierarchy");
            pathArray.add(originalOrder.getParentId() != null ? originalOrder.getParentId() : "CSTM_1185");
        }

        return pathArray;
    }

    /**
     * Build the ComplexValues object for the API payload
     */
    private ObjectNode buildComplexValues(KarmakPurchaseOrder originalOrder) {
        ObjectNode complexValues = objectMapper.createObjectNode();
        ArrayNode purchaseOrdersArray = objectMapper.createArrayNode();

        if (originalOrder.getComplexValues() != null &&
            originalOrder.getComplexValues().getPurchaseOrders() != null) {

            for (PurchaseOrder po : originalOrder.getComplexValues().getPurchaseOrders()) {
                ObjectNode purchaseOrderNode = objectMapper.createObjectNode();
                purchaseOrderNode.put("type", po.getType() != null ? po.getType() : "PurchaseOrders");
                purchaseOrderNode.put("name", po.getName() != null ? po.getName() : "PurchaseOrders");

                // Build Values object for this purchase order
                ObjectNode valuesNode = buildPurchaseOrderValues(po);
                purchaseOrderNode.set("Values", valuesNode);

                purchaseOrdersArray.add(purchaseOrderNode);
            }
        }

        complexValues.set("PurchaseOrders", purchaseOrdersArray);
        return complexValues;
    }

    /**
     * Build the Values object for a single purchase order in ComplexValues
     */
    private ObjectNode buildPurchaseOrderValues(PurchaseOrder purchaseOrder) {
        ObjectNode valuesNode = objectMapper.createObjectNode();

        if (purchaseOrder.getValues() != null) {
            Values values = purchaseOrder.getValues();

            // OrderPartNumber
            if (values.getOrderPartNumber() != null) {
                valuesNode.set("OrderPartNumber", buildAttributeNode("OrderPartNumber", values.getOrderPartNumber()));
            }

            // OrderCost
            if (values.getOrderCost() != null) {
                valuesNode.set("OrderCost", buildAttributeNode("OrderCost", values.getOrderCost()));
            }

            // OrderQuantity
            if (values.getOrderQuantity() != null) {
                valuesNode.set("OrderQuantity", buildAttributeNode("OrderQuantity", values.getOrderQuantity()));
            }

            // OrderPrimaryURL
            if (values.getOrderPrimaryURL() != null) {
                valuesNode.set("OrderPrimaryURL", buildAttributeNode("OrderPrimaryURL", values.getOrderPrimaryURL()));
            }

            // OrderProductDetails
            if (values.getOrderProductDetails() != null) {
                valuesNode.set("OrderProductDetails", buildAttributeNode("OrderProductDetails", values.getOrderProductDetails()));
            }

            // OrderSupplierName
            if (values.getOrderSupplierName() != null) {
                valuesNode.set("OrderSupplierName", buildAttributeNode("OrderSupplierName", values.getOrderSupplierName()));
            }
        }

        return valuesNode;
    }

    /**
     * Build an attribute node with content array
     */
    private ObjectNode buildAttributeNode(String attributeId, Object attributeObject) {
        ObjectNode attributeNode = objectMapper.createObjectNode();
        attributeNode.put("attributeId", attributeId);

        ArrayNode contentArray = objectMapper.createArrayNode();

        // Extract content from the attribute object
        if (attributeObject != null) {
            try {
                // Use reflection to get content
                java.lang.reflect.Method getContentMethod = attributeObject.getClass().getMethod("getContent");
                @SuppressWarnings("unchecked")
                List<Content> contentList = (List<Content>) getContentMethod.invoke(attributeObject);

                if (contentList != null && !contentList.isEmpty()) {
                    for (Content content : contentList) {
                        ObjectNode contentNode = objectMapper.createObjectNode();
                        Object value = content.getValue();

                        // Handle different value types
                        if (value instanceof Number) {
                            contentNode.put("value", ((Number) value).doubleValue());
                        } else if (value instanceof String) {
                            contentNode.put("value", (String) value);
                        } else if (value != null) {
                            contentNode.put("value", value.toString());
                        } else {
                            contentNode.put("value", "");
                        }

                        contentArray.add(contentNode);
                    }
                }
            } catch (Exception e) {
                log.warn("Failed to extract content from attribute {}: {}", attributeId, e.getMessage());
                // Add empty content as fallback
                ObjectNode emptyContent = objectMapper.createObjectNode();
                emptyContent.put("value", "");
                contentArray.add(emptyContent);
            }
        }

        // If no content was added, add empty content
        if (contentArray.size() == 0) {
            ObjectNode emptyContent = objectMapper.createObjectNode();
            emptyContent.put("value", "");
            contentArray.add(emptyContent);
        }

        attributeNode.set("content", contentArray);
        return attributeNode;
    }

    /**
     * Calculate grand total from complex values
     */
    private double calculateGrandTotal(KarmakPurchaseOrder originalOrder) {
        double total = 0.0;

        if (originalOrder.getComplexValues() != null &&
            originalOrder.getComplexValues().getPurchaseOrders() != null) {

            for (PurchaseOrder po : originalOrder.getComplexValues().getPurchaseOrders()) {
                if (po.getValues() != null && po.getValues().getOrderCost() != null) {
                    try {
                        List<Content> costContent = po.getValues().getOrderCost().getContent();
                        if (costContent != null && !costContent.isEmpty()) {
                            Object costValue = costContent.get(0).getValue();
                            if (costValue instanceof Number) {
                                total += ((Number) costValue).doubleValue();
                            } else if (costValue instanceof String) {
                                total += Double.parseDouble((String) costValue);
                            }
                        }
                    } catch (Exception e) {
                        log.warn("Failed to parse cost value for grand total calculation: {}", e.getMessage());
                    }
                }
            }
        }

        return total;
    }
}
