package com.atp.product.service.impl;

import com.atp.product.controller.dto.request.InternalPurchaseOrderRequest;
import com.atp.product.controller.dto.request.ScheduledOrderRequest;
import com.atp.product.controller.dto.request.UpdateScheduleRequest;
import com.atp.product.controller.dto.response.PurchaseOrderResponse;
import com.atp.product.controller.dto.response.ScheduledOrderResponse;
import com.atp.product.exception.DomainException;
import com.atp.product.exception.bad_request.ScheduledOrderAlreadyExistsException;
import com.atp.product.karmak_responses.*;
import com.atp.product.karmak_responses.OrderPartNumber;
import com.atp.product.karmak_responses.Content;
import com.atp.product.model.ScheduledOrder;
import com.atp.product.service.NotificationService;
import com.atp.product.service.ScheduledOrderService;
import com.atp.product.utils.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import okhttp3.*;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;

/**
 * Implementation of ScheduledOrderService with optimized performance
 */
@Slf4j
@Service
public class ScheduledOrderServiceImpl implements ScheduledOrderService {

    private final MongoTemplate mongoTemplate;
    private final PurchaseOrderService purchaseOrderService;
    private final NotificationService notificationService;
    private final CommonServiceImpl commonService;
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;

   /* // Configuration properties for third-party API
    @Value("${third-party.api.url:http://localhost:8080/server/api/data-entities/create}")
    private String thirdPartyApiUrl;

    @Value("${third-party.api.login.url:http://localhost:8080/server/rest/auth/login}")
    private String loginApiUrl;

    @Value("${third-party.api.username:IshaniPandya}")
    private String apiUsername;

    @Value("${third-party.api.password:Indianic@123}")
    private String apiPassword;

    @Value("${third-party.api.timeout:30}")
    private int apiTimeoutSeconds;*/

    // Cache for authentication token
    private String cachedToken;
    private long tokenExpiryTime;

    @Value("${Karmak.partSearchURL}")
    private String karmaPartSearchURL;


    @Value("${Karmak.AccountNumber}")
    private String karmaAccountNumber;

    @Value("${Ocp-Apim-Subscription-Key}")
    private String subScriptionKey;

    @Autowired
    public ScheduledOrderServiceImpl(MongoTemplate mongoTemplate,
                                   PurchaseOrderService purchaseOrderService,
                                   NotificationService notificationService,
                                   CommonServiceImpl commonService) {
        this.mongoTemplate = mongoTemplate;
        this.purchaseOrderService = purchaseOrderService;
        this.notificationService = notificationService;
        this.commonService = commonService;

        // Initialize HTTP client with default timeout (will be configured via properties)
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                .writeTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                .readTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                .build();

        this.objectMapper = new ObjectMapper();
    }

    @Override
    public ScheduledOrderResponse subscribe(ScheduledOrderRequest request) {
        log.info("Creating scheduled order subscription for customer: {}", request.getKarmakCustomerId());

        try {
            // Look for any scheduled order by original order ID
            Query query = new Query(Criteria.where("originalOrderId").is(request.getOriginalOrderId()));
            ScheduledOrder existingOrder = mongoTemplate.findOne(query, ScheduledOrder.class);

            if (existingOrder != null) {
                if (existingOrder.isActive()) {
                    // Already active
                    throw new ScheduledOrderAlreadyExistsException(
                            "A scheduled order already exists for original order ID: " + request.getOriginalOrderId()
                    );
                } else {
                    // Previously cancelled - reactivate
                    Update update = new Update()
                            .set("active", true)
                            .set("status", "ACTIVE")
                            .set("frequencyDays", request.getFrequencyDays())
                            .set("subscriptionDate", LocalDate.now())
                            .set("nextRunDate", LocalDate.now().plusDays(request.getFrequencyDays()))
                            .set("notes", request.getNotes())
                            .set("notifyOnOutOfStock", request.getNotifyOnOutOfStock())
                            .set("updatedAt", LocalDateTime.now());

                    mongoTemplate.updateFirst(query, update, ScheduledOrder.class);

                    ScheduledOrder reactivatedOrder = mongoTemplate.findOne(query, ScheduledOrder.class);

                    assert reactivatedOrder != null;
                    log.info("Reactivated existing scheduled order with ID: {}", reactivatedOrder.getId());
                    return mapToResponse(reactivatedOrder);
                }
            }

            // No existing subscription - create new one
            ScheduledOrder scheduledOrder = new ScheduledOrder();
            scheduledOrder.setKarmakCustomerId(request.getKarmakCustomerId());
            scheduledOrder.setCustomerCorrelationId(request.getCustomerCorrelationId());
            scheduledOrder.setOriginalOrderId(request.getOriginalOrderId());
            scheduledOrder.setFrequencyDays(request.getFrequencyDays());
            scheduledOrder.setSubscriptionDate(LocalDate.now());
            //scheduledOrder.setNextRunDate(LocalDate.now().plusDays(request.getFrequencyDays()));
            scheduledOrder.setNextRunDate(LocalDate.now());
            scheduledOrder.setNotes(request.getNotes());
            scheduledOrder.setNotifyOnOutOfStock(request.getNotifyOnOutOfStock());
            scheduledOrder.setActive(true);
            scheduledOrder.setStatus("ACTIVE");
            scheduledOrder.setOrdersProcessed(0);
            scheduledOrder.setCreatedAt(LocalDateTime.now());
            scheduledOrder.setUpdatedAt(LocalDateTime.now());

            mongoTemplate.save(scheduledOrder);

            log.info("Created new scheduled order with ID: {}", scheduledOrder.getId());
            return mapToResponse(scheduledOrder);

        } catch (DomainException e) {
            log.error("Failed to create scheduled order for customer: {}", request.getKarmakCustomerId(), e);
            throw new DomainException("Failed to create scheduled order: " + e.getMessage(), e);
        }
    }

    public boolean doesScheduledOrderByOriginalOrderIdExist(String originalOrderId) {
        // Build a query to find documents where 'originalOrderId' matches the given ID
        Query query = new Query(Criteria.where("originalOrderId").is(originalOrderId));
        // This is more efficient than fetching the entire document if you only need to know if it exists.
        return mongoTemplate.exists(query, ScheduledOrder.class);
    }

    @Override
    public boolean cancel(String scheduledOrderId, String karmakCustomerId) {
        log.info("Cancelling scheduled order: {} for customer: {}", scheduledOrderId, karmakCustomerId);

        try {
            Query query = new Query(Criteria.where("id").is(scheduledOrderId)
                                          .and("karmakCustomerId").is(karmakCustomerId)
                                          .and("active").is(true));
            
            Update update = new Update()
                .set("active", false)
                .set("status", "CANCELLED")
                .set("updatedAt", LocalDateTime.now())
                    .set("notes", "Cancelled by customer");
            
            var result = mongoTemplate.updateFirst(query, update, ScheduledOrder.class);
            
            if (result.getModifiedCount() > 0) {
                // Send cancellation email
                //notificationService.sendScheduledOrderCancellation(customerId, scheduledOrderId);
                log.info("Scheduled order cancelled successfully: {}", scheduledOrderId);
                return true;
            } else {
                log.warn("Scheduled order not found or already cancelled: {}", scheduledOrderId);
                return false;
            }
            
        } catch (Exception e) {
            log.error("Failed to cancel scheduled order: {}", scheduledOrderId, e);
            return false;
        }
    }

    @Override
    public ScheduledOrderResponse updateSchedule(String scheduledOrderId, UpdateScheduleRequest request) {
        log.info("Updating scheduled order: {}", scheduledOrderId);
        
        try {
            Query query = new Query(Criteria.where("id").is(scheduledOrderId)
                                          .and("karmakCustomerId").is(request.getKarmakCustomerId())
                                          .and("active").is(true));
            
            Update update = new Update().set("updatedAt", LocalDateTime.now());
            
            // Update only provided fields
            if (request.getFrequencyDays() != null) {
                update.set("frequencyDays", request.getFrequencyDays());
            }
            if (request.getNextRunDate() != null) {
                update.set("nextRunDate", request.getNextRunDate());
            }
            if (request.getNotes() != null) {
                update.set("notes", request.getNotes());
            }
            if (request.getKarmakCustomerId() != null) {
                update.set("karmakCustomerId", request.getKarmakCustomerId());
            }
            if (request.getCustomerCorrelationId() != null) {
                update.set("customerCorrelationId", request.getCustomerCorrelationId());
            }
            if (request.getNotifyOnOutOfStock() != null) {
                update.set("notifyOnOutOfStock", request.getNotifyOnOutOfStock());
            }
            if (request.getActive() != null) {
                update.set("active", request.getActive());
                update.set("status", request.getActive() ? "ACTIVE" : "CANCELLED");
            }
            
            var result = mongoTemplate.updateFirst(query, update, ScheduledOrder.class);
            
            if (result.getModifiedCount() > 0) {
                // Get updated order
                ScheduledOrder updatedOrder = mongoTemplate.findOne(query, ScheduledOrder.class);
                
                // Send update notification
                if (request.getFrequencyDays() != null) {
                    notificationService.sendScheduledOrderUpdate(
                        request.getKarmakCustomerId(),
                        scheduledOrderId, 
                        request.getFrequencyDays()
                    );
                }
                
                log.info("Scheduled order updated successfully: {}", scheduledOrderId);
                return mapToResponse(updatedOrder);
            } else {
                log.warn("Scheduled order not found for update: {}", scheduledOrderId);
                return null;
            }
            
        } catch (Exception e) {
            log.error("Failed to update scheduled order: {}", scheduledOrderId, e);
            throw new RuntimeException("Failed to update scheduled order: " + e.getMessage(), e);
        }
    }

    @Override
    public List<ScheduledOrderResponse> getScheduledOrdersByCustomer(String karmakCustomerId) {
        log.info("Retrieving scheduled orders for customer: {}", karmakCustomerId);

        try {
            Query query = new Query(Criteria.where("karmakCustomerId").is(karmakCustomerId));
            List<ScheduledOrder> orders = mongoTemplate.find(query, ScheduledOrder.class);
            
            return orders.stream()
                        .map(this::mapToResponse)
                        .collect(Collectors.toList());
                        
        } catch (Exception e) {
            log.error("Failed to retrieve scheduled orders for customer: {}", karmakCustomerId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public ScheduledOrderResponse getScheduledOrderById(String scheduledOrderId, String karmakCustomerId) {
        log.info("Retrieving scheduled order: {} for customer: {}", scheduledOrderId, karmakCustomerId);

        try {
            Query query = new Query(Criteria.where("id").is(scheduledOrderId)
                                          .and("karmakCustomerId").is(karmakCustomerId));
            
            ScheduledOrder order = mongoTemplate.findOne(query, ScheduledOrder.class);
            return order != null ? mapToResponse(order) : null;
            
        } catch (Exception e) {
            log.error("Failed to retrieve scheduled order: {} for customer: {}", scheduledOrderId, karmakCustomerId, e);
            return null;
        }
    }





    @Override
    public List<ScheduledOrderResponse> getAllActiveScheduledOrders() {
        log.info("Retrieving all active scheduled orders");
        
        try {
            Query query = new Query(Criteria.where("active").is(true));
            List<ScheduledOrder> orders = mongoTemplate.find(query, ScheduledOrder.class);
            
            return orders.stream()
                        .map(this::mapToResponse)
                        .collect(Collectors.toList());
                        
        } catch (Exception e) {
            log.error("Failed to retrieve all active scheduled orders", e);
            return new ArrayList<>();
        }
    }

    private ScheduledOrderResponse mapToResponse(ScheduledOrder order) {
        ScheduledOrderResponse response = new ScheduledOrderResponse();
        response.setId(order.getId());
        response.setKarmakCustomerId(order.getKarmakCustomerId());
        response.setCustomerCorrelationId(order.getCustomerCorrelationId());
        response.setOriginalOrderId(order.getOriginalOrderId());
        response.setSubscriptionDate(order.getSubscriptionDate());
        response.setFrequencyDays(order.getFrequencyDays());
        response.setNextRunDate(order.getNextRunDate());
        response.setActive(order.isActive());
        response.setNotes(order.getNotes());
        response.setOrdersProcessed(order.getOrdersProcessed());
        response.setLastProcessedDate(order.getLastProcessedDate());
        response.setNotifyOnOutOfStock(order.getNotifyOnOutOfStock());
        response.setStatus(order.getStatus());
        response.setCreatedAt(order.getCreatedAt());
        response.setUpdatedAt(order.getUpdatedAt());
        return response;
    }

    /**
     * Optimized scheduled order processing (moved from CommonServiceImpl)
     */
    @Override
    //@Scheduled(fixedRate = 3600 * 60000) // every 1 hour
    public void processScheduledOrders() {
        log.info("Starting scheduled order processing...");

        LocalDate now = LocalDate.now();
        // Use exact date match like original code
        Query query = new Query(Criteria.where("active").is(true)
                                      .and("nextRunDate").is(now));

        List<ScheduledOrder> dueOrders = mongoTemplate.find(query, ScheduledOrder.class);
        log.info("Found {} scheduled orders due for processing", dueOrders.size());

        if (dueOrders.isEmpty()) {
            log.info("No scheduled orders due for processing");
            return;
        }

        // FIXED: Add global unavailable items tracking like original (thread-safe for parallel processing)
        List<String> globalUnavailableItems = Collections.synchronizedList(new ArrayList<>());

        // Process orders in parallel for better performance
        List<CompletableFuture<Void>> futures = dueOrders.stream()
                .map(order -> processScheduledOrderAsync(order, globalUnavailableItems))
                .toList();

        // Wait for all orders to complete processing
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenRun(() -> log.info("Completed processing {} scheduled orders", dueOrders.size()))
                .exceptionally(throwable -> {
                    log.error("Error during batch processing of scheduled orders", throwable);
                    return null;
                });
    }

    /**
     * Process a single scheduled order asynchronously
     */
    private CompletableFuture<Void> processScheduledOrderAsync(ScheduledOrder scheduledOrder,
                                                              List<String> globalUnavailableItems) {
        return CompletableFuture.runAsync(() -> {
            try {
                processSingleScheduledOrder(scheduledOrder, globalUnavailableItems);
            } catch (Exception e) {
                log.error("Failed to process scheduled order: {}", scheduledOrder.getId(), e);

                // Send failure notification
                notificationService.sendPurchaseOrderFailure(
                    scheduledOrder.getKarmakCustomerId(),
                    scheduledOrder.getId(),
                    e.getMessage()
                );
            }
        });
    }

    /**
     * Process a single scheduled order
     */
    private void processSingleScheduledOrder(ScheduledOrder scheduledOrder,
                                           List<String> globalUnavailableItems) {
        log.info("Processing scheduled order: {} for customer: {}",
                scheduledOrder.getId(), scheduledOrder.getKarmakCustomerId());

        // Get the original purchase order
        KarmakPurchaseOrder purchaseOrder = purchaseOrderService.getPurchaseOrderByExternalId(
                scheduledOrder.getOriginalOrderId());

        if (purchaseOrder == null) {
            log.warn("No purchase order found for external ID {}", scheduledOrder.getOriginalOrderId());
            return;
        }

        // Extract and validate complex values
        ComplexValues complexValuesContainer = purchaseOrder.getComplexValues();
        if (complexValuesContainer == null || complexValuesContainer.getPurchaseOrders() == null) {
            log.warn("No complex values found in purchase order {}", scheduledOrder.getOriginalOrderId());
            return;
        }

        List<PurchaseOrder> purchaseOrders = complexValuesContainer.getPurchaseOrders();
        List<String> unavailableParts = new ArrayList<>();
        List<InternalPurchaseOrderRequest.InternalLineItem> internalLineItems = new ArrayList<>();

        // Process each purchase order item
        for (PurchaseOrder poItem : purchaseOrders) {
            Values values = poItem.getValues();
            if (values == null) {
                log.warn("Skipping purchase order item with null values.");
                continue;
            }

            // FIXED: Extract part number safely like original code
            String partNumber = Optional.ofNullable(values.getOrderPartNumber())
                    .map(OrderPartNumber::getContent)
                    .flatMap(contentList -> contentList.stream().findFirst())
                    .map(Content::getValue)
                    .map(Object::toString)
                    .orElse("N/A");

            // FIXED: Check global unavailable items first like original
            if (globalUnavailableItems.contains(partNumber)) {
                unavailableParts.add(partNumber);
                continue;
            }

            // Check availability and pricing
            try {
                /*CompletableFuture<Map<String, String>> resultFuture = getPriceAndQuantityForProductAsync(partNumber, scheduledOrder.getKarmakCustomerId());
                Map<String, String> result = resultFuture.join();

                // FIXED: Use correct constants like original code
                String availableQty = result.get(Constants.QUANTITY);
                String price = result.get(Constants.PRICE);
                String branch = result.get(Constants.BRANCH_CODE);
                String karmakPartId = result.get(Constants.PARTS_INVENTORY_DETAIL_ID);*/
                String availableQty = "5";
                String price = "10.0";
                String branch = "1";
                String karmakPartId = "1";

                if (availableQty == null || Integer.parseInt(availableQty) <= 0) {
                    unavailableParts.add(partNumber);
                    globalUnavailableItems.add(partNumber); // FIXED: Add to global list
                    continue;
                }

                // FIXED: Map to InternalLineItem like original code
                InternalPurchaseOrderRequest.InternalLineItem lineItem =
                        new InternalPurchaseOrderRequest.InternalLineItem();
                lineItem.setPartID(Integer.parseInt(karmakPartId));
                lineItem.setQuantity(Integer.parseInt(poItem.getValues().getOrderQuantity().getContent().get(0).getValue().toString())); // FIXED: Use available quantity
                lineItem.setCost(Double.parseDouble(price)); // FIXED: Use price directly
                lineItem.setMessage("Scheduled order");
                lineItem.setBranch(branch); // FIXED: Set branch from result
                lineItem.setTotalQuantity(Integer.parseInt(availableQty)); // FIXED: Use available quantity

                internalLineItems.add(lineItem);

            } catch (Exception e) {
                log.error("Error checking availability for part {} in order {}",
                         partNumber, scheduledOrder.getId(), e);
                unavailableParts.add(partNumber);
            }
        }

        // Process the order if all parts are available
        if (unavailableParts.isEmpty() && !internalLineItems.isEmpty()) {
            processAvailableOrder(scheduledOrder, internalLineItems, purchaseOrder);
        } else {
            handleUnavailableOrder(scheduledOrder, unavailableParts);
        }
    }

    public CompletableFuture<Map<String, String>> getPriceAndQuantityForProductAsync(String partNumber, String karmakCustomerId) {
        Map<String, String> finalResults = new HashMap<>();
        try {

            /*String karmakUrl = karmaPartSearchURL;
            String karmakAccount = karmaAccountNumber;
            String subscriptionKey = subScriptionKey;*/


            // Initialize customerID
            String customerID = null;

            // Check if karmakCustomerId is null or empty
            if (karmakCustomerId != null && !karmakCustomerId.isEmpty()) {
                customerID = karmakCustomerId;  // If valid, assign the string value
            } else {
                customerID = "null";  // Otherwise, set customerID as null
            }

            log.info("int customer ID " + customerID);

            String requestJson = "{\"customerID\": " + customerID + "," + "\"locationID\": 1," + "\"region\": null,"
                    + "\"parts\": [{" + "\"number\": \"" + partNumber + "\"," + "\"description\": null,"
                    + "\"exactMatch\": true," + "\"source\": null," + "\"crossReference\": false," + "\"PageSize\": 1" + "}]" + "}";


            RequestBody body = RequestBody.create(requestJson, MediaType.parse("application/json"));
            Request partRequest = new Request.Builder().url(karmaPartSearchURL).addHeader("Content-Type", "application/json")
                    .addHeader("Cache-Control", "no-cache").addHeader("KarmakAccountNumber", karmaAccountNumber)
                    .addHeader("Ocp-Apim-Subscription-Key", subScriptionKey).post(body).build();
            // Create Item in PLM
            Call call = httpClient.newCall(partRequest);
            Response partResponse = call.execute();
            int statusCode = partResponse.code();
            if (statusCode >= 200 && statusCode < 300) {
                JsonNode json = objectMapper.readValue(partResponse.body().string(), JsonNode.class);
                partResponse.close();
                if (Objects.nonNull(json) && json instanceof ArrayNode) {
                    ArrayNode results = (ArrayNode) json;
                    if (!results.isEmpty()) {
                        JsonNode entries = results.get(0).get("results");
                        if (entries != null) {
                            ArrayNode parts = (ArrayNode) entries;
                            JsonNode firstPart = parts.get(0);
                            if (firstPart != null) {
                                // Extract customerPrice and available fields
                                JsonNode priceNode = firstPart.get("price");
                                JsonNode availableNode = firstPart.get("available");
                                JsonNode inventoryDetailIdNode = firstPart.get("PartsInventoryDetailID");
                                JsonNode branchCodeNode = firstPart.get("branchCode");


                                if (priceNode != null && availableNode != null) {
                                    finalResults.put(Constants.PRICE, priceNode.get("customerPrice").asText());
                                    finalResults.put(Constants.QUANTITY, availableNode.asText());
                                    finalResults.put(Constants.PARTS_INVENTORY_DETAIL_ID, Integer.toString(inventoryDetailIdNode.asInt()));
                                    finalResults.put(Constants.BRANCH_CODE, branchCodeNode.asText());
                                } else {
                                    log.info("Price or available field is missing in the first element.");
                                }
                            } else {
                                log.info("First element of 'results' array is missing.");
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new DomainException("An error occurred while calling karmak api", e.getMessage());
        }
        return CompletableFuture.completedFuture(finalResults);
    }

    /**
     * Process order when all parts are available
     */
    private void processAvailableOrder(ScheduledOrder scheduledOrder,
                                     List<InternalPurchaseOrderRequest.InternalLineItem> lineItems,
                                     KarmakPurchaseOrder originalOrder) {
        try {
            // FIXED: Create internal purchase order request like original code
            InternalPurchaseOrderRequest internalRequest = new InternalPurchaseOrderRequest();
            internalRequest.setPoDate(LocalDate.now());
            internalRequest.setLineItems(lineItems);
            internalRequest.setSupplierID(0); // FIXED: set appropriately like original
            internalRequest.setApVendorID(0); // FIXED: set appropriately like original
            internalRequest.setLocationID(1); // set appropriately
            internalRequest.setFillingLocationID(1); // set appropriately
            internalRequest.setOrderedBy("system@scheduler"); // FIXED: like original
            internalRequest.setDirectShip(false);

            // Create purchase order
            //PurchaseOrderResponse response = commonService.createPurchaseOrder(internalRequest);

            /*if (response != null) {
                log.info("PO created successfully: {} for scheduled order: {}",
                        response.getPartsPurchaseOrder().getPoNumber(), scheduledOrder.getId());

                // Extract PO information from response
                String partsPurchaseOrderID = String.valueOf(response.getPartsPurchaseOrder().getPartsPurchaseOrderID());
                String poNumber = response.getPartsPurchaseOrder().getPoNumber();

                // Update originalOrder Values with PO information
                updateOriginalOrderWithPOInfo(originalOrder, partsPurchaseOrderID, poNumber);

                // Place the order (external API call)
               *//* boolean orderPlaced = placeOrder(originalOrder);
                if (!orderPlaced) {
                    log.warn("Failed to place order via external API for scheduled order: {}",
                            scheduledOrder.getId());
                    // Continue with processing even if external API fails
                }*//*

                // Update scheduled order for next run
                updateScheduledOrderAfterSuccess(scheduledOrder);

                // Send success notification
                notificationService.sendPurchaseOrderSuccess(
                    scheduledOrder.getKarmakCustomerId(),
                        response.getPartsPurchaseOrder().getPoNumber(),
                    scheduledOrder.getId()
                );

            } else {
                log.error("PO creation failed for scheduled order {}", scheduledOrder.getId());

                // Send failure notification
                notificationService.sendPurchaseOrderFailure(
                    scheduledOrder.getKarmakCustomerId(),
                    scheduledOrder.getId(),
                    "Purchase order creation failed"
                );
            }*/

            String partsPurchaseOrderID = "12345";
            String poNumber = "67890";

            // Update originalOrder Values with PO information
            updateOriginalOrderWithPOInfo(originalOrder, partsPurchaseOrderID, poNumber);

        } catch (Exception e) {
            log.error("Exception while processing available order {}: {}",
                     scheduledOrder.getId(), e.getMessage(), e);

            // Send failure notification
            notificationService.sendPurchaseOrderFailure(
                scheduledOrder.getKarmakCustomerId(),
                scheduledOrder.getId(),
                e.getMessage()
            );
        }
    }

    /**
     * Handle order when some parts are unavailable
     */
    private void handleUnavailableOrder(ScheduledOrder scheduledOrder, List<String> unavailableParts) {
        log.info("Skipping order {} due to unavailable parts: {}",
                scheduledOrder.getId(), unavailableParts);

        // Send out of stock notification if enabled
        if (scheduledOrder.getNotifyOnOutOfStock()) {
            notificationService.sendOutOfStockEmail(scheduledOrder.getKarmakCustomerId(), unavailableParts);
        }

        // Reschedule for next attempt (add 1 day to retry sooner)
        LocalDate nextRetry = LocalDate.now().plusDays(1);
        //updateScheduledOrderNextRun(scheduledOrder, nextRetry);
    }

    /**
     * Update scheduled order after successful processing
     */
    private void updateScheduledOrderAfterSuccess(ScheduledOrder scheduledOrder) {
        Query query = new Query(Criteria.where("id").is(scheduledOrder.getId()));

        Update update = new Update()
            .set("nextRunDate", scheduledOrder.getNextRunDate().plusDays(scheduledOrder.getFrequencyDays()))
            .set("ordersProcessed", scheduledOrder.getOrdersProcessed() + 1)
            .set("lastProcessedDate", LocalDateTime.now())
            .set("updatedAt", LocalDateTime.now());

        mongoTemplate.updateFirst(query, update, ScheduledOrder.class);

        log.info("Updated scheduled order {} for next run on {}",
                scheduledOrder.getId(),
                scheduledOrder.getNextRunDate().plusDays(scheduledOrder.getFrequencyDays()));
    }

    /**
     * Update scheduled order next run date
     */
    private void updateScheduledOrderNextRun(ScheduledOrder scheduledOrder, LocalDate nextRunDate) {
        Query query = new Query(Criteria.where("id").is(scheduledOrder.getId()));

        Update update = new Update()
            .set("nextRunDate", nextRunDate)
            .set("updatedAt", LocalDateTime.now());

        mongoTemplate.updateFirst(query, update, ScheduledOrder.class);
    }

    /**
     * Update originalOrder Values with PO information from createPurchaseOrder response
     * @param originalOrder The original order to update
     * @param partsPurchaseOrderID The Parts Purchase Order ID from response
     * @param poNumber The PO Number from response
     */
    private void updateOriginalOrderWithPOInfo(KarmakPurchaseOrder originalOrder,
                                               String partsPurchaseOrderID,
                                               String poNumber) {
        try {
            log.debug("Updating originalOrder Values with PO info - PartsPurchaseOrderID: {}, PONumber: {}",
                    partsPurchaseOrderID, poNumber);

            // Get existing Values list or create new one
            List<com.atp.product.karmak_responses.Value> values = originalOrder.getValues();
            if (values == null) {
                values = new ArrayList<>();
                originalOrder.setValues(values);
            }

            // Update or add PartsPurchaseOrderID
            updateOrAddValueField(values, "PartsPurchaseOrderID", partsPurchaseOrderID);

            // Update or add PONumber
            updateOrAddValueField(values, "PONumber", poNumber);

            log.info("Successfully updated originalOrder Values with PartsPurchaseOrderID: {} and PONumber: {}",
                    partsPurchaseOrderID, poNumber);

        } catch (Exception e) {
            log.error("Failed to update originalOrder with PO info - PartsPurchaseOrderID: {}, PONumber: {}",
                    partsPurchaseOrderID, poNumber, e);
        }
    }

    /**
     * Update or add a specific field in the Values array
     * @param values The Values list to update
     * @param attributeId The attribute ID to update/add
     * @param value The value to set
     */
    private void updateOrAddValueField(List<com.atp.product.karmak_responses.Value> values,
                                       String attributeId,
                                       String value) {
        // Look for existing field
        com.atp.product.karmak_responses.Value existingValue = values.stream()
                .filter(v -> attributeId.equals(v.getAttributeId()))
                .findFirst()
                .orElse(null);

        if (existingValue != null) {
            // Update existing field
            log.debug("Updating existing field: {} with value: {}", attributeId, value);

            List<Content> content = existingValue.getContent();
            if (content == null || content.isEmpty()) {
                content = new ArrayList<>();
                existingValue.setContent(content);
            }

            // Update first content or create new one
            if (content.isEmpty()) {
                Content newContent = new Content();
                newContent.setValue(value);
                content.add(newContent);
            } else {
                content.get(0).setValue(value);
            }
        } else {
            // Add new field
            log.debug("Adding new field: {} with value: {}", attributeId, value);

            com.atp.product.karmak_responses.Value newValue = new com.atp.product.karmak_responses.Value();
            newValue.setAttributeId(attributeId);

            Content content = new Content();
            content.setValue(value);

            List<Content> contentList = new ArrayList<>();
            contentList.add(content);
            newValue.setContent(contentList);

            values.add(newValue);
        }
    }

    /**
     * Mark order as completed
     */
    private void markOrderAsCompleted(ScheduledOrder scheduledOrder) {
        Query query = new Query(Criteria.where("id").is(scheduledOrder.getId()));

        Update update = new Update()
            .set("active", false)
            .set("status", "COMPLETED")
            .set("updatedAt", LocalDateTime.now());

        mongoTemplate.updateFirst(query, update, ScheduledOrder.class);

        log.info("Marked scheduled order {} as completed", scheduledOrder.getId());
    }

    /**
     * Extract part number from Values object
     */
    private String extractPartNumber(Values values) {
        if (values.getOrderPartNumber() != null &&
            values.getOrderPartNumber().getContent() != null &&
            !values.getOrderPartNumber().getContent().isEmpty()) {

            Content content = values.getOrderPartNumber().getContent().get(0);
            return content.getValue() != null ? content.getValue().toString() : null;
        }
        return null;
    }

    /**
     * Extract quantity from Values object
     */
    private String extractQuantity(Values values) {
        if (values.getOrderQuantity() != null &&
            values.getOrderQuantity().getContent() != null &&
            !values.getOrderQuantity().getContent().isEmpty()) {

            Content content = values.getOrderQuantity().getContent().get(0);
            return content.getValue() != null ? content.getValue().toString() : "1";
        }
        return "1"; // Default quantity
    }

    /**
     * Extract cost from Values object
     */
    private String extractCost(Values values) {
        if (values.getOrderCost() != null &&
            values.getOrderCost().getContent() != null &&
            !values.getOrderCost().getContent().isEmpty()) {

            Content content = values.getOrderCost().getContent().get(0);
            return content.getValue() != null ? content.getValue().toString() : "0.0";
        }
        return "0.0"; // Default cost
    }

    /**
     * Place order by calling third-party API
     * @param originalOrder The original KarmakPurchaseOrder to be placed
     * @return true if order was placed successfully, false otherwise
     */
    /*public boolean placeOrder(KarmakPurchaseOrder originalOrder) {
        log.info("Placing order for external ID: {}", originalOrder.getExternalId());

        try {
            // Convert originalOrder directly to JSON string
            String jsonPayload = objectMapper.writeValueAsString(originalOrder);
            log.debug("Order payload: {}", jsonPayload);

            // Create HTTP request
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, jsonPayload);

            Request.Builder requestBuilder = new Request.Builder()
                    .url(thirdPartyApiUrl)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json");

            // Get authentication token dynamically
            String authToken = getAuthToken();
            if (authToken != null && !authToken.trim().isEmpty()) {
                requestBuilder.addHeader("Authorization", authToken);
            } else {
                log.error("Failed to get authentication token for order: {}", originalOrder.getExternalId());
                return false;
            }

            Request request = requestBuilder.build();

            // Execute the request
            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful()) {
                    log.info("Order placed successfully for external ID: {}. Response code: {}",
                            originalOrder.getExternalId(), response.code());

                    // Log response body for debugging (optional)
                    if (response.body() != null) {
                        String responseBody = response.body().string();
                        log.debug("API Response: {}", responseBody);
                    }

                    return true;
                } else {
                    log.error("Failed to place order for external ID: {}. Response code: {}, Message: {}",
                             originalOrder.getExternalId(), response.code(), response.message());

                    // Log error response body if available
                    if (response.body() != null) {
                        String errorBody = response.body().string();
                        log.error("Error response body: {}", errorBody);
                    }

                    return false;
                }
            }

        } catch (IOException e) {
            log.error("IO Exception while placing order for external ID: {}",
                     originalOrder.getExternalId(), e);
            return false;
        } catch (Exception e) {
            log.error("Unexpected error while placing order for external ID: {}",
                     originalOrder.getExternalId(), e);
            return false;
        }
    }

    *//**
     * Get authentication token from login API
     * @return JWT token string or null if authentication fails
     *//*
    private String getAuthToken() {
        // Check if we have a valid cached token
        if (cachedToken != null && System.currentTimeMillis() < tokenExpiryTime) {
            log.debug("Using cached authentication token");
            return cachedToken;
        }

        log.info("Authenticating to get new token from: {}", loginApiUrl);

        try {
            // Create login request payload
            ObjectNode loginPayload = objectMapper.createObjectNode();
            loginPayload.put("userid", apiUsername);
            loginPayload.put("password", apiPassword);
            loginPayload.put("deviceUUID", "");
            loginPayload.put("enc", false);
            loginPayload.set("userProperties", objectMapper.createArrayNode());

            String loginJson = objectMapper.writeValueAsString(loginPayload);
            log.debug("Login payload: {}", loginJson);

            // Create HTTP request for login
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, loginJson);

            Request request = new Request.Builder()
                    .url(loginApiUrl)
                    .method("POST", body)
                    .addHeader("Content-Type", "application/json")
                    .build();

            // Execute login request
            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    log.debug("Login response: {}", responseBody);

                    // Parse response to extract token
                    JsonNode responseJson = objectMapper.readTree(responseBody);

                    if (responseJson.has("status") && responseJson.get("status").asInt() == 1) {
                        String token = responseJson.get("token").asText();

                        // Cache the token (assume 1 hour validity, adjust as needed)
                        cachedToken = token;
                        tokenExpiryTime = System.currentTimeMillis() + (55 * 60 * 1000); // 55 minutes

                        log.info("Successfully authenticated and cached token");
                        return token;
                    } else {
                        log.error("Authentication failed. Status: {}", responseJson.get("status"));
                        return null;
                    }
                } else {
                    log.error("Login request failed. Response code: {}, Message: {}",
                             response.code(), response.message());
                    return null;
                }
            }

        } catch (Exception e) {
            log.error("Exception during authentication", e);
            return null;
        }
    }*/


}








