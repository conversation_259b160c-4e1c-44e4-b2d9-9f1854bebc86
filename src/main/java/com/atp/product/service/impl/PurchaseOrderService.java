package com.atp.product.service.impl;

import com.atp.product.karmak_responses.*;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class PurchaseOrderService {

    private final MongoTemplate mongoTemplate;

    @Autowired
    public PurchaseOrderService(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    public KarmakPurchaseOrder getPurchaseOrderByExternalId(String externalId) {
        Query query = new Query(Criteria.where("externalId").is(externalId));
        Document doc = mongoTemplate.findOne(query, Document.class, "entity");
        if (doc == null) {
            return null;
        }
        return mapDocumentToKarmakPurchaseOrder(doc);
    }

    private KarmakPurchaseOrder mapDocumentToKarmakPurchaseOrder(Document doc) {
        KarmakPurchaseOrder po = new KarmakPurchaseOrder();
        mapBasicFields(po, doc);
        mapComplexValues(po, doc);
        mapFlattenedValues(po, doc);
        return po;
    }

    private void mapBasicFields(KarmakPurchaseOrder po, Document doc) {
        po.setParentId(doc.getString("parentId"));
        po.setEntityType(doc.getString("entityType"));
        po.setWorkspaceId(doc.getString("workspaceId"));
        po.setDatatypeDefinitionId(doc.getString("datatypeDefinitionId"));
        po.setExternalId(""); // or doc.getString("externalId")
        po.setName(doc.getString("name"));
        po.setCompleteness(doc.get("completeness", Number.class).longValue());
        po.setSystemEntity(doc.getBoolean("isSystemEntity", false));
        po.setDeleted(false);
        po.setApprovalStatus("NOT_APPROVED");
        po.setPath((List<String>) doc.get("ancestors"));
    }

    private void mapComplexValues(KarmakPurchaseOrder po, Document doc) {
        Document complexValuesDoc = (Document) doc.get("complexValues");
        if (complexValuesDoc == null) return;

        ComplexValues complexValues = new ComplexValues();
        List<Document> purchaseOrdersDocs = (List<Document>) complexValuesDoc.get("PurchaseOrders");
        if (purchaseOrdersDocs == null || purchaseOrdersDocs.isEmpty()) return;

        List<PurchaseOrder> purchaseOrders = purchaseOrdersDocs.stream()
                .map(this::mapPurchaseOrder)
                .toList();

        complexValues.setPurchaseOrders(purchaseOrders);
        po.setComplexValues(complexValues);
    }

    private PurchaseOrder mapPurchaseOrder(Document poDoc) {
        PurchaseOrder purchaseOrder = new PurchaseOrder();
        purchaseOrder.setType(poDoc.getString("type"));
        purchaseOrder.setName(poDoc.getString("name"));

        Document valuesDoc = (Document) poDoc.get("values");
        if (valuesDoc != null) {
            Values values = new Values();
            values.setOrderPartNumber(mapValue(valuesDoc, "OrderPartNumber", OrderPartNumber.class));
            values.setOrderCost(mapValue(valuesDoc, "OrderCost", OrderCost.class));
            values.setOrderQuantity(mapValue(valuesDoc, "OrderQuantity", OrderQuantity.class));
            values.setOrderPrimaryURL(mapValue(valuesDoc, "OrderPrimaryURL", OrderPrimaryURL.class));
            values.setOrderProductDetails(mapValue(valuesDoc, "OrderProductDetails", OrderProductDetails.class));
            values.setOrderSupplierName(mapValue(valuesDoc, "OrderSupplierName", OrderSupplierName.class));
            values.setOrderPartNumberSlug(mapValue(valuesDoc, "OrderPartNumberSlug", OrderPartNumberSlug.class));
            purchaseOrder.setValues(values);
        }
        return purchaseOrder;
    }

    private void mapFlattenedValues(KarmakPurchaseOrder po, Document doc) {
        Document valuesDoc = (Document) doc.get("values");
        if (valuesDoc == null) return;

        List<Value> flatValues = new ArrayList<>();
        for (Map.Entry<String, Object> entry : valuesDoc.entrySet()) {
            String key = entry.getKey();
            Document langDoc = (Document) ((Document) valuesDoc.get(key)).get("English");
            List<Document> contents = (List<Document>) langDoc.get("content");

            Value val = new Value();
            val.setAttributeId(key);

            List<Content> contentList = contents.stream()
                    .map(c -> createContent(key, c.get("text")))
                    .toList();

            val.setContent(contentList);
            flatValues.add(val);
        }
        po.setValues(flatValues);
    }

    private Content createContent(String key, Object text) {
        Content<Object> content = new Content<>();

        if ("ECM_OrderDate".equals(key)) {
            content.setValue(new SimpleDateFormat("MM/dd/yyyy").format(new Date()));
        } else if ("ECM_GrandTotal".equals(key)) {
            if (text instanceof Document tdoc) {
                Object decimal = (tdoc.get("$numberDecimal"));
                content.setValue(decimal != null ? Double.valueOf(decimal.toString()) : null);
            } else {
                content.setValue(Double.valueOf(text.toString()));
            }
        } else if (text instanceof Document tdoc) {
            content.setValue((tdoc.values().iterator().next().toString()));
        } else {
            content.setValue(text != null ? text.toString() : null);
        }

        return content;
    }


    private <T> T mapValue(Document parent, String key, Class<T> clazz) {
        Document valDoc = (Document) parent.get(key);
        if (valDoc == null) return null;

        try {
            T instance = clazz.getDeclaredConstructor().newInstance();
            clazz.getMethod("setAttributeId", String.class).invoke(instance, key);

            Document english = (Document) valDoc.get("English");
            List<Document> contents = (List<Document>) english.get("content");
            List<Content> contentList = new ArrayList<>();

            for (Document c : contents) {
                Content <Object>content = new Content<>();
                Object text = c.get("text");
                Object value = extractValue(key, text);
                content.setValue(value);
                contentList.add(content);
            }

            clazz.getMethod("setContent", List.class).invoke(instance, contentList);
            return instance;

        } catch (Exception e) {
            throw new RuntimeException("Error mapping value for " + key, e);
        }
    }

    /**
     * Helper method to centralize value extraction and conversion.
     */
    private Object extractValue(String key, Object text) {
        Object rawValue;

        if (text instanceof Document tdoc) {
            rawValue = tdoc.values().iterator().next();
        } else {
            rawValue = text;
        }

        if (rawValue == null) return null;

        return switch (key) {
            case "OrderCost" -> Double.valueOf(rawValue.toString());
            case "OrderQuantity" -> Integer.valueOf(rawValue.toString());
            default -> rawValue.toString();
        };
    }


}
