package com.atp.product.service.impl;

import com.atp.product.karmak_responses.*;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class PurchaseOrderService {

    private final MongoTemplate mongoTemplate;

    // Thread-safe date formatter
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("MM/dd/yyyy");

    // Cache for reflection-based object creation to avoid repeated reflection calls
    private static final Map<Class<?>, Function<String, Object>> OBJECT_FACTORY_CACHE = new ConcurrentHashMap<>();

    // Pre-defined field projections for MongoDB queries
    private static final String[] BASIC_FIELDS = {
        "parentId", "entityType", "workspaceId", "datatypeDefinitionId",
        "externalId", "name", "completeness", "isSystemEntity", "ancestors"
    };

    private static final String[] COMPLEX_FIELDS = {"complexValues"};
    private static final String[] VALUE_FIELDS = {"values"};

    @Autowired
    public PurchaseOrderService(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
        initializeObjectFactories();
    }

    /**
     * Initialize object factories to replace reflection-based object creation
     */
    private void initializeObjectFactories() {
        OBJECT_FACTORY_CACHE.put(OrderPartNumber.class, attributeId -> {
            OrderPartNumber obj = new OrderPartNumber();
            obj.setAttributeId(attributeId);
            return obj;
        });

        OBJECT_FACTORY_CACHE.put(OrderCost.class, attributeId -> {
            OrderCost obj = new OrderCost();
            obj.setAttributeId(attributeId);
            return obj;
        });

        OBJECT_FACTORY_CACHE.put(OrderQuantity.class, attributeId -> {
            OrderQuantity obj = new OrderQuantity();
            obj.setAttributeId(attributeId);
            return obj;
        });

        OBJECT_FACTORY_CACHE.put(OrderPrimaryURL.class, attributeId -> {
            OrderPrimaryURL obj = new OrderPrimaryURL();
            obj.setAttributeId(attributeId);
            return obj;
        });

        OBJECT_FACTORY_CACHE.put(OrderProductDetails.class, attributeId -> {
            OrderProductDetails obj = new OrderProductDetails();
            obj.setAttributeId(attributeId);
            return obj;
        });

        OBJECT_FACTORY_CACHE.put(OrderSupplierName.class, attributeId -> {
            OrderSupplierName obj = new OrderSupplierName();
            obj.setAttributeId(attributeId);
            return obj;
        });

        OBJECT_FACTORY_CACHE.put(OrderPartNumberSlug.class, attributeId -> {
            OrderPartNumberSlug obj = new OrderPartNumberSlug();
            obj.setAttributeId(attributeId);
            return obj;
        });
    }

    /**
     * Optimized method with field projection
     */
    public KarmakPurchaseOrder getPurchaseOrderByExternalId(String externalId) {
        // Use field projection to fetch only required fields
        Query query = new Query(Criteria.where("externalId").is(externalId));

        // Include all necessary fields in projection for better performance
        String[] allFields = combineArrays(BASIC_FIELDS, COMPLEX_FIELDS, VALUE_FIELDS);
        query.fields().include(allFields);

        Document doc = mongoTemplate.findOne(query, Document.class, "entity");
        if (doc == null) {
            return null;
        }
        return mapDocumentToKarmakPurchaseOrder(doc);
    }

    /**
     * Batch processing method for multiple external IDs
     */
    public List<KarmakPurchaseOrder> getPurchaseOrdersByExternalIds(List<String> externalIds) {
        if (externalIds == null || externalIds.isEmpty()) {
            return Collections.emptyList();
        }

        Query query = new Query(Criteria.where("externalId").in(externalIds));
        String[] allFields = combineArrays(BASIC_FIELDS, COMPLEX_FIELDS, VALUE_FIELDS);
        query.fields().include(allFields);

        List<Document> docs = mongoTemplate.find(query, Document.class, "entity");

        // Use parallel stream for large collections
        return docs.parallelStream()
                .map(this::mapDocumentToKarmakPurchaseOrder)
                .collect(Collectors.toList());
    }

    /**
     * Utility method to combine multiple arrays
     */
    private String[] combineArrays(String[]... arrays) {
        return Arrays.stream(arrays)
                .flatMap(Arrays::stream)
                .toArray(String[]::new);
    }

    /**
     * Optimized mapping with null checks and early returns
     */
    private KarmakPurchaseOrder mapDocumentToKarmakPurchaseOrder(Document doc) {
        if (doc == null) {
            return null;
        }

        KarmakPurchaseOrder po = new KarmakPurchaseOrder();

        // Use parallel execution for independent mapping operations
        mapBasicFields(po, doc);

        // Only process complex values if they exist
        if (doc.containsKey("complexValues")) {
            mapComplexValues(po, doc);
        }

        // Only process flattened values if they exist
        if (doc.containsKey("values")) {
            mapFlattenedValues(po, doc);
        }

        return po;
    }

    /**
     * Optimized basic field mapping with null safety
     */
    private void mapBasicFields(KarmakPurchaseOrder po, Document doc) {
        po.setParentId(doc.getString("parentId"));
        po.setEntityType(doc.getString("entityType"));
        po.setWorkspaceId(doc.getString("workspaceId"));
        po.setDatatypeDefinitionId(doc.getString("datatypeDefinitionId"));
        po.setExternalId(doc.getString("externalId")); // Fixed to use actual value
        po.setName(doc.getString("name"));

        // Safe number conversion
        Number completeness = doc.get("completeness", Number.class);
        po.setCompleteness(completeness != null ? completeness.longValue() : 0L);

        po.setSystemEntity(doc.getBoolean("isSystemEntity", false));
        po.setDeleted(false);
        po.setApprovalStatus("NOT_APPROVED");

        // Safe list casting
        Object ancestors = doc.get("ancestors");
        if (ancestors instanceof List<?>) {
            po.setPath((List<String>) ancestors);
        }
    }

    /**
     * Optimized complex values mapping with parallel processing for large collections
     */
    private void mapComplexValues(KarmakPurchaseOrder po, Document doc) {
        Document complexValuesDoc = (Document) doc.get("complexValues");
        if (complexValuesDoc == null) return;

        ComplexValues complexValues = new ComplexValues();
        Object purchaseOrdersObj = complexValuesDoc.get("PurchaseOrders");

        if (!(purchaseOrdersObj instanceof List<?>)) return;

        @SuppressWarnings("unchecked")
        List<Document> purchaseOrdersDocs = (List<Document>) purchaseOrdersObj;

        if (purchaseOrdersDocs.isEmpty()) return;

        // Use parallel stream for large collections (>10 items)
        List<PurchaseOrder> purchaseOrders = purchaseOrdersDocs.size() > 10
            ? purchaseOrdersDocs.parallelStream()
                .map(this::mapPurchaseOrder)
                .collect(Collectors.toList())
            : purchaseOrdersDocs.stream()
                .map(this::mapPurchaseOrder)
                .collect(Collectors.toList());

        complexValues.setPurchaseOrders(purchaseOrders);
        po.setComplexValues(complexValues);
    }

    /**
     * Optimized purchase order mapping using factory pattern instead of reflection
     */
    private PurchaseOrder mapPurchaseOrder(Document poDoc) {
        PurchaseOrder purchaseOrder = new PurchaseOrder();
        purchaseOrder.setType(poDoc.getString("type"));
        purchaseOrder.setName(poDoc.getString("name"));

        Document valuesDoc = (Document) poDoc.get("values");
        if (valuesDoc != null) {
            Values values = new Values();
            values.setOrderPartNumber(mapValueOptimized(valuesDoc, "OrderPartNumber", OrderPartNumber.class));
            values.setOrderCost(mapValueOptimized(valuesDoc, "OrderCost", OrderCost.class));
            values.setOrderQuantity(mapValueOptimized(valuesDoc, "OrderQuantity", OrderQuantity.class));
            values.setOrderPrimaryURL(mapValueOptimized(valuesDoc, "OrderPrimaryURL", OrderPrimaryURL.class));
            values.setOrderProductDetails(mapValueOptimized(valuesDoc, "OrderProductDetails", OrderProductDetails.class));
            values.setOrderSupplierName(mapValueOptimized(valuesDoc, "OrderSupplierName", OrderSupplierName.class));
            values.setOrderPartNumberSlug(mapValueOptimized(valuesDoc, "OrderPartNumberSlug", OrderPartNumberSlug.class));
            purchaseOrder.setValues(values);
        }
        return purchaseOrder;
    }

    /**
     * Optimized flattened values mapping with better null safety and performance
     */
    private void mapFlattenedValues(KarmakPurchaseOrder po, Document doc) {
        Document valuesDoc = (Document) doc.get("values");
        if (valuesDoc == null || valuesDoc.isEmpty()) return;

        // Pre-allocate list with estimated size for better performance
        List<Value> flatValues = new ArrayList<>(valuesDoc.size());

        for (Map.Entry<String, Object> entry : valuesDoc.entrySet()) {
            String key = entry.getKey();
            Object valueObj = entry.getValue();

            if (!(valueObj instanceof Document)) continue;

            Document valueDoc = (Document) valueObj;
            Object englishObj = valueDoc.get("English");

            if (!(englishObj instanceof Document)) continue;

            Document langDoc = (Document) englishObj;
            Object contentsObj = langDoc.get("content");

            if (!(contentsObj instanceof List<?>)) continue;

            @SuppressWarnings("unchecked")
            List<Document> contents = (List<Document>) contentsObj;

            Value val = new Value();
            val.setAttributeId(key);

            List<Content> contentList = contents.stream()
                    .map(c -> createContentOptimized(key, c.get("text")))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            val.setContent(contentList);
            flatValues.add(val);
        }
        po.setValues(flatValues);
    }

    /**
     * Optimized content creation with cached date formatter and better type handling
     */
    private Content createContentOptimized(String key, Object text) {
        Content<Object> content = new Content<>();

        if ("ECM_OrderDate".equals(key)) {
            // Use thread-safe DateTimeFormatter instead of SimpleDateFormat
            content.setValue(LocalDateTime.now().format(DATE_FORMATTER));
        } else if ("ECM_GrandTotal".equals(key)) {
            content.setValue(parseDecimalValue(text));
        } else if (text instanceof Document tdoc) {
            // Safe extraction from document
            Iterator<Object> values = tdoc.values().iterator();
            content.setValue(values.hasNext() ? values.next().toString() : null);
        } else {
            content.setValue(text != null ? text.toString() : null);
        }

        return content;
    }

    /**
     * Helper method to safely parse decimal values
     */
    private Double parseDecimalValue(Object text) {
        if (text instanceof Document tdoc) {
            Object decimal = tdoc.get("$numberDecimal");
            if (decimal != null) {
                try {
                    return Double.valueOf(decimal.toString());
                } catch (NumberFormatException e) {
                    return null;
                }
            }
        } else if (text != null) {
            try {
                return Double.valueOf(text.toString());
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }


    /**
     * Optimized value mapping using factory pattern instead of reflection
     */
    @SuppressWarnings("unchecked")
    private <T> T mapValueOptimized(Document parent, String key, Class<T> clazz) {
        Document valDoc = (Document) parent.get(key);
        if (valDoc == null) return null;

        // Use factory pattern instead of reflection
        Function<String, Object> factory = OBJECT_FACTORY_CACHE.get(clazz);
        if (factory == null) {
            throw new IllegalArgumentException("No factory found for class: " + clazz.getName());
        }

        T instance = (T) factory.apply(key);

        Object englishObj = valDoc.get("English");
        if (!(englishObj instanceof Document)) return instance;

        Document english = (Document) englishObj;
        Object contentsObj = english.get("content");

        if (!(contentsObj instanceof List<?>)) return instance;

        @SuppressWarnings("unchecked")
        List<Document> contents = (List<Document>) contentsObj;

        // Pre-allocate list for better performance
        List<Content> contentList = new ArrayList<>(contents.size());

        for (Document c : contents) {
            Content<Object> content = new Content<>();
            Object text = c.get("text");
            Object value = extractValueOptimized(key, text);
            content.setValue(value);
            contentList.add(content);
        }

        // Use type-safe setter methods instead of reflection
        setContentForInstance(instance, contentList);
        return instance;
    }

    /**
     * Type-safe content setting without reflection
     */
    @SuppressWarnings("unchecked")
    private void setContentForInstance(Object instance, List<Content> contentList) {
        if (instance instanceof OrderPartNumber) {
            ((OrderPartNumber) instance).setContent(contentList);
        } else if (instance instanceof OrderCost) {
            ((OrderCost) instance).setContent(contentList);
        } else if (instance instanceof OrderQuantity) {
            ((OrderQuantity) instance).setContent(contentList);
        } else if (instance instanceof OrderPrimaryURL) {
            ((OrderPrimaryURL) instance).setContent(contentList);
        } else if (instance instanceof OrderProductDetails) {
            ((OrderProductDetails) instance).setContent(contentList);
        } else if (instance instanceof OrderSupplierName) {
            ((OrderSupplierName) instance).setContent(contentList);
        } else if (instance instanceof OrderPartNumberSlug) {
            ((OrderPartNumberSlug) instance).setContent(contentList);
        }
    }

    /**
     * Optimized value extraction with better error handling
     */
    private Object extractValueOptimized(String key, Object text) {
        Object rawValue;

        if (text instanceof Document tdoc) {
            Iterator<Object> values = tdoc.values().iterator();
            rawValue = values.hasNext() ? values.next() : null;
        } else {
            rawValue = text;
        }

        if (rawValue == null) return null;

        try {
            return switch (key) {
                case "OrderCost" -> Double.valueOf(rawValue.toString());
                case "OrderQuantity" -> Integer.valueOf(rawValue.toString());
                default -> rawValue.toString();
            };
        } catch (NumberFormatException e) {
            // Log the error and return string representation as fallback
            return rawValue.toString();
        }
    }

    /**
     * Cache management methods
     */

    @CacheEvict(value = "purchaseOrders", key = "#externalId")
    public void evictPurchaseOrderFromCache(String externalId) {
        // This method will remove specific entry from cache
    }

    @CacheEvict(value = "purchaseOrders", allEntries = true)
    public void clearAllPurchaseOrdersCache() {
        // This method will clear entire cache
    }
}
