package com.atp.product.service;

import org.springframework.core.io.ByteArrayResource;

/**
 * Service interface for invoice generation
 */
public interface InvoiceService {

    /**
     * Generate invoice PDF for a purchase order by external ID
     * @param name Customer name
     * @param email Customer email
     * @param externalId External order ID
     * @return ByteArrayResource containing the PDF invoice, or null if order not found
     */
    ByteArrayResource generateInvoice(String name, String email, String externalId);

    /**
     * Generate invoice PDF for a purchase order by order ID
     * @param orderId Purchase order ID
     * @param name Customer name
     * @param email Customer email
     * @return ByteArrayResource containing the PDF invoice, or null if order not found
     */
    ByteArrayResource generateInvoiceById(String orderId, String name, String email);
}
