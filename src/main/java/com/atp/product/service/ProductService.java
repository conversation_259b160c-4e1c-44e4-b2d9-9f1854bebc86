package com.atp.product.service;

import com.atp.product.controller.dto.response.ProductDetailsResponse;
import com.atp.product.controller.dto.response.ProductResponseHomePage;

import java.util.List;
import java.util.Optional;

public interface ProductService {

    /**
     * Get Featured Product details for home page screen
     */
    List<ProductResponseHomePage> getFeaturedProducts();

    /**
     * Get New Product details for home page screen
     */
    List<ProductResponseHomePage> getNewProducts();

    /**
     * Get Product details for home page screen
     */
    ProductDetailsResponse getProductDetailsByPartNumberSlug(String partNumberSlug);

    /**
     * Get Similar Product list by using brand id
     */
    List<ProductResponseHomePage> getSimilarProductListByCrossReferenceId(List<String> crossReferenceIds);

}
