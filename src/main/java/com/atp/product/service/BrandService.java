package com.atp.product.service;

import com.atp.product.controller.dto.response.BrandPageResponse;
import com.atp.product.controller.dto.response.BrandResponseHomePage;
import com.atp.product.controller.dto.response.CategoryBrandResponse;

import java.util.List;

public interface BrandService {

    /**
     * Get Shop by parts details for home page screen
     */
    List<BrandResponseHomePage> getShopByBrands();

    /**
     * Get Shop by parts details for home page screen
     */
    List<CategoryBrandResponse> getBrandListByCategory();
    /**
     * Get brand page details by brand id
     */
    BrandPageResponse getBrandPageByBrandSlug(String brandSlug);
}
