package com.atp.product.service;

import java.util.List;

/**
 * Service interface for handling notifications
 */
public interface NotificationService {

    /**
     * Send out of stock email notification to customer
     * @param customerId The customer ID
     * @param unavailableParts List of part numbers that are out of stock
     */
    void sendOutOfStockEmail(String customerId, List<String> unavailableParts);

    /**
     * Send scheduled order confirmation email
     * @param customerId The customer ID
     * @param scheduledOrderId The scheduled order ID
     * @param nextRunDate The next scheduled run date
     */
    void sendScheduledOrderConfirmation(String customerId, String scheduledOrderId, String nextRunDate);

    /**
     * Send scheduled order cancellation email
     * @param customerId The customer ID
     * @param scheduledOrderId The scheduled order ID
     */
    void sendScheduledOrderCancellation(String customerId, String scheduledOrderId);

    /**
     * Send scheduled order update confirmation email
     * @param customerId The customer ID
     * @param scheduledOrderId The scheduled order ID
     * @param newFrequency The new frequency in days
     */
    void sendScheduledOrderUpdate(String customerId, String scheduledOrderId, int newFrequency);

    /**
     * Send purchase order success notification
     * @param customerId The customer ID
     * @param poNumber The purchase order number
     * @param scheduledOrderId The scheduled order ID
     */
    void sendPurchaseOrderSuccess(String customerId, String poNumber, String scheduledOrderId);

    /**
     * Send purchase order failure notification
     * @param customerId The customer ID
     * @param scheduledOrderId The scheduled order ID
     * @param errorMessage The error message
     */
    void sendPurchaseOrderFailure(String customerId, String scheduledOrderId, String errorMessage);
}
