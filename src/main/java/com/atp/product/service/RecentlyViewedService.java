package com.atp.product.service;

import com.atp.product.controller.dto.response.ProductResponseHomePage;

import java.util.List;

public interface RecentlyViewedService {

    void recordProductView(String customerCorrelationId, String partNumberSlug);
    
    /**
     * Get list of part number slugs for recently viewed products
     * @param customerCorrelationId The unique identifier for the customer
     * @param limit Maximum number of results to return
     * @return List of part number slugs in order of most recently viewed first
     */
    List<String> getRecentlyViewedPartNumberSlugs(String customerCorrelationId, int limit);
    

    List<ProductResponseHomePage> getRecentlyViewedProductDetails(String customerCorrelationId, int limit);
}