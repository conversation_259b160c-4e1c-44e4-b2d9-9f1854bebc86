package com.atp.product.service;

import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import org.springframework.data.mongodb.core.DocumentCallbackHandler;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.List;

public interface ApplicationService<T> {
    public T findOne(Query query, Class<T> entityClass);

    public List<T> find(Query query, Class<?> entityClass);

    public long count(Query query);

    public long count(Query query, Class<?> entityClass);

    public DeleteResult remove(T entity);

    public void save(T objectToSave);

    public void save(T objectToSave, String collectionName);

    public void executeQuery(Query query, String collectionName, DocumentCallbackHandler dch);

    public UpdateResult updateMulti(Query query, Update update, Class<?> entityClass);

    public UpdateResult updateFirst(Query query, Update update, Class<?> entityClass);

}
