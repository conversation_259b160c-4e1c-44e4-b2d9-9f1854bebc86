package com.atp.product.service;

import com.atp.product.controller.dto.request.ScheduledOrderRequest;
import com.atp.product.controller.dto.request.UpdateScheduleRequest;
import com.atp.product.controller.dto.response.ScheduledOrderResponse;
import com.atp.product.model.ScheduledOrder;

import java.util.List;

/**
 * Service interface for managing scheduled orders
 */
public interface ScheduledOrderService {

    /**
     * Subscribe to a scheduled order
     * @param request The scheduled order subscription request
     * @return The created scheduled order response
     */
    ScheduledOrderResponse subscribe(ScheduledOrderRequest request);

    /**
     * Cancel a scheduled order
     * @param scheduledOrderId The ID of the scheduled order to cancel
     * @param karmakCustomerId The Karmak customer ID for validation
     * @return true if cancelled successfully, false otherwise
     */
    boolean cancel(String scheduledOrderId, String karmakCustomerId);

    /**
     * Update an existing scheduled order
     * @param scheduledOrderId The ID of the scheduled order to update
     * @param request The update request with new scheduling parameters
     * @return The updated scheduled order response
     */
    ScheduledOrderResponse updateSchedule(String scheduledOrderId, UpdateScheduleRequest request);

    /**
     * Get all scheduled orders for a customer
     * @param customerId The customer ID
     * @return List of scheduled orders for the customer
     */
    List<ScheduledOrderResponse> getScheduledOrdersByCustomer(String customerId);

    /**
     * Get a specific scheduled order by ID
     * @param scheduledOrderId The scheduled order ID
     * @param customerId The customer ID for validation
     * @return The scheduled order response or null if not found
     */
    ScheduledOrderResponse getScheduledOrderById(String scheduledOrderId, String customerId);

    /**
     * Process all due scheduled orders (moved from CommonServiceImpl)
     * This method is called by the scheduler to process orders that are due
     */
    void processScheduledOrders();



    /**
     * Get all active scheduled orders (for admin purposes)
     * @return List of all active scheduled orders
     */
    List<ScheduledOrderResponse> getAllActiveScheduledOrders();
}
