package com.atp.product.service;

import com.atp.product.controller.dto.request.FilterRequest;
import com.atp.product.controller.dto.response.BrandCategoryResponse;
import com.atp.product.controller.dto.response.CategorySubcategoryResponse;
import com.atp.product.controller.dto.response.FilterResponse;

import java.util.List;

public interface CategoryService {

    /**
     * Get Category and Subcategory List for home page screen
     */
    List<CategorySubcategoryResponse> getCategorySubCategoryList();
    /**
     * Get All Filter List for product catalog
     */
    FilterResponse getAllFilterListForProductCatalog(FilterRequest filterRequest);

    /**
     * Get Category details by brand for menu bar
     */
    List<BrandCategoryResponse> getCategoryListByBrand();
}
