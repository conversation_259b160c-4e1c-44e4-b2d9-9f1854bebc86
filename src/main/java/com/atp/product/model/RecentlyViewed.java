package com.atp.product.model;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
@Document(collection = "recently_viewed")
public class RecentlyViewed {
    @Id
    private String id;
    private String customerCorrelationId;
    private List<ViewedProduct> products = new ArrayList<>();
    
    @Data
    public static class ViewedProduct {
        private String partNumberSlug;
        private LocalDateTime viewedAt;
    }
}