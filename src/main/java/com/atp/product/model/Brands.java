package com.atp.product.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.TypeAlias;
import org.springframework.data.mongodb.core.mapping.Document;

@Document(collection = "brands")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TypeAlias("Brands")
public class Brands {
    private String brandId;
    private String brandName;
    private String brandLogoUrl;
    private boolean active;
    private boolean featuredBrand;
    private String brandDescription;
    private String brandSlug;
}
