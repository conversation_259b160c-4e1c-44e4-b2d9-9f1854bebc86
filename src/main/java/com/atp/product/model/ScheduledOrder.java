package com.atp.product.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Document(collection = "scheduled_orders")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScheduledOrder {

    @Id
    private String id;

    private String customerId;
    private String originalOrderId;

    private LocalDate subscriptionDate;
    private int frequencyDays;

    private LocalDate nextRunDate;
    private LocalDate endDate;

    private boolean active = true;

    private String notes;
    private Integer ordersProcessed = 0;

    private LocalDateTime lastProcessedDate;
    private Boolean notifyOnOutOfStock = true;

    private String status = "ACTIVE"; // ACTIVE, CANCELLED, COMPLETED

    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}