package com.atp.product.model;

import java.util.List;
import java.util.Map;

import org.springframework.data.annotation.TypeAlias;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.DocumentReference;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Document(collection = "category_and_products")
@Data 
@AllArgsConstructor
@NoArgsConstructor
@TypeAlias("CategoryProduct")
public class Product {

	private String catalogId;
	private String parentId;
	private String name;
	private CatalogType catalogType;
	private String description;
	private String longDescription;
	private boolean isHomepageCategory;
	private String title1;
	private String title2;
	private double price;
	private String primaryImage;
	private List<String> alternateImages;
	@DocumentReference
	private List<KeyValue> specificationAttributes;
	private boolean featuredItem;
	private boolean newItem;
	private String partNumber;
	private int quantity;
	private String brandId;
	private String brandName;
	private String vendorId;
	private String vendorName;
	private List<String> model;
	private List<String> year;
	private boolean active;
	private List<String> make;
	private List<String> featuredAndBenifits;
	private boolean isDeleted;
	private String category;
	private String subCategory;
	private List<String> applicationSummary;
	private String partNumberSlug;
	private String categorySlug;
	private String subCategorySlug;
	private String brandSlug;
	private Map<String, String> assetMap;
	private List<String> crossReferenceId;
}