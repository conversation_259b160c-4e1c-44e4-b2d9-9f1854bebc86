package com.atp.product.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.TypeAlias;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.DocumentReference;

import java.util.List;

@Document(collection = "wishlist")
@Data
@AllArgsConstructor
@NoArgsConstructor
@TypeAlias("WishList")
public class Wishlist {
    @Id
    private String id;
    private String customerCorrelationId;
    private List<ProductDetail> productDetails;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ProductDetail {
        private String partNumber;
    }
}
