package com.atp.product.karmak_responses;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/karmak")
public class KarmakController {

    @GetMapping("/purchase-order")
    public ResponseEntity<KarmakPurchaseOrder> getKarmakPurchaseOrder() {
        KarmakPurchaseOrder order = new KarmakPurchaseOrder();
        
        // Set basic order information
        order.setParentId("PARENT_" + UUID.randomUUID().toString());
        order.setEntityType("PURCHASE_ORDER");
        order.setWorkspaceId("WORKSPACE_123");
        order.setDatatypeDefinitionId("PURCHASE_ORDER_DEF");
        order.setCompleteness(100L);
        order.setDeleted(false);
        order.setSystemEntity(false);
        order.setApprovalStatus("APPROVED");
        order.setExternalId("EXT_" + UUID.randomUUID().toString());
        order.setName("Purchase Order #" + String.format("%06d", (int)(Math.random() * 1000000)));
        order.setPath(Collections.singletonList("/purchase-orders/" + order.getName()));
        
        // Create and set SimpleValues (empty in this case)
        order.setSimpleValues(new SimpleValues());
        
        // Create and set Values
        Value statusValue = new Value();
        statusValue.setAttributeId("orderStatus");
        
        Content statusContent = new Content();
        statusContent.setValue("PROCESSING");
        statusValue.setContent(Collections.singletonList(statusContent));
        
        // Create and set OrderCost
        OrderCost orderCost = new OrderCost();
        orderCost.setAttributeId("orderCost");
        
        Content costContent = new Content();
        costContent.setValue("1250.75");
        orderCost.setContent(Collections.singletonList(costContent));
        
        // Create and set OrderQuantity
        OrderQuantity orderQuantity = new OrderQuantity();
        orderQuantity.setAttributeId("orderQuantity");
        
        Content quantityContent = new Content();
        quantityContent.setValue("5");
        orderQuantity.setContent(Collections.singletonList(quantityContent));
        
        // Create and set OrderPrimaryURL
        OrderPrimaryURL orderPrimaryURL = new OrderPrimaryURL();
        orderPrimaryURL.setAttributeId("orderPrimaryURL");
        
        Content urlContent = new Content();
        urlContent.setValue("https://example.com/order/123");
        orderPrimaryURL.setContent(Collections.singletonList(urlContent));
        
        // Create and set OrderProductDetails
        OrderProductDetails orderProductDetails = new OrderProductDetails();
        orderProductDetails.setAttributeId("productDetails");
        
        Content productContent = new Content();
        productContent.setValue("Sample Product Description");
        orderProductDetails.setContent(Collections.singletonList(productContent));
        
        // Create and set OrderSupplierName
        OrderSupplierName orderSupplierName = new OrderSupplierName();
        orderSupplierName.setAttributeId("supplierName");
        
        Content supplierContent = new Content();
        supplierContent.setValue("Sample Supplier Inc.");
        orderSupplierName.setContent(Collections.singletonList(supplierContent));
        
        // Create and set Values object
        Values values = new Values();
        values.setOrderCost(orderCost);
        values.setOrderQuantity(orderQuantity);
        values.setOrderPrimaryURL(orderPrimaryURL);
        values.setOrderProductDetails(orderProductDetails);
        values.setOrderSupplierName(orderSupplierName);
        
        // Create and set OrderPartNumber
        OrderPartNumber orderPartNumber = new OrderPartNumber();
        orderPartNumber.setAttributeId("partNumber");
        
        Content partNumberContent = new Content();
        partNumberContent.setValue("PN-12345");
        orderPartNumber.setContent(Collections.singletonList(partNumberContent));
        values.setOrderPartNumber(orderPartNumber);
        
        // Create and set OrderPartNumberSlug
        //OrderPartNumberSlug orderPartNumberSlug = new OrderPartNumberSlug();
        //orderPartNumberSlug.setAttributeId("partNumberSlug");
        
        Content slugContent = new Content();
        slugContent.setValue("pn-12345");
        //orderPartNumberSlug.setContent(Collections.singletonList(slugContent));
        
        // Create a list of Value objects for the order
        List<Value> valueList = new ArrayList<>();
        valueList.add(statusValue);
        order.setValues(valueList);
        
        // Create and set ComplexValues with a PurchaseOrder
        ComplexValues complexValues = new ComplexValues();
        
        PurchaseOrder purchaseOrder = new PurchaseOrder();
        purchaseOrder.setType("STANDARD");
        purchaseOrder.setName("PO-" + System.currentTimeMillis());
        purchaseOrder.setValues(values);
        
        List<PurchaseOrder> purchaseOrders = new ArrayList<>();
        purchaseOrders.add(purchaseOrder);
        complexValues.setPurchaseOrders(purchaseOrders);
        
        order.setComplexValues(complexValues);
        
        return ResponseEntity.ok(order);
    }
}
