package com.atp.product.karmak_responses;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "OrderPartNumber",
        "OrderPartNumberSlug",
        "OrderCost",
        "OrderQuantity",
        "OrderPrimaryURL",
        "OrderProductDetails",
        "OrderSupplierName"
})
public class Values {

    @JsonProperty("OrderPartNumber")
    private OrderPartNumber orderPartNumber;
    @JsonProperty("OrderPartNumberSlug")
    private OrderPartNumberSlug orderPartNumberSlug;
    @JsonProperty("OrderCost")
    private OrderCost orderCost;
    @JsonProperty("OrderQuantity")
    private OrderQuantity orderQuantity;
    @JsonProperty("OrderPrimaryURL")
    private OrderPrimaryURL orderPrimaryURL;
    @JsonProperty("OrderProductDetails")
    private OrderProductDetails orderProductDetails;
    @JsonProperty("OrderSupplierName")
    private OrderSupplierName orderSupplierName;

    @JsonProperty("OrderPartNumber")
    public OrderPartNumber getOrderPartNumber() {
        return orderPartNumber;
    }

    @JsonProperty("OrderPartNumber")
    public void setOrderPartNumber(OrderPartNumber orderPartNumber) {
        this.orderPartNumber = orderPartNumber;
    }

    @JsonProperty("OrderPartNumberSlug")
    public OrderPartNumberSlug getOrderPartNumberSlug() {
        return orderPartNumberSlug;
    }

    @JsonProperty("OrderPartNumberSlug")
    public void setOrderPartNumberSlug(OrderPartNumberSlug orderPartNumberSlug) {
        this.orderPartNumberSlug = orderPartNumberSlug;
    }

    @JsonProperty("OrderCost")
    public OrderCost getOrderCost() {
        return orderCost;
    }

    @JsonProperty("OrderCost")
    public void setOrderCost(OrderCost orderCost) {
        this.orderCost = orderCost;
    }

    @JsonProperty("OrderQuantity")
    public OrderQuantity getOrderQuantity() {
        return orderQuantity;
    }

    @JsonProperty("OrderQuantity")
    public void setOrderQuantity(OrderQuantity orderQuantity) {
        this.orderQuantity = orderQuantity;
    }

    @JsonProperty("OrderPrimaryURL")
    public OrderPrimaryURL getOrderPrimaryURL() {
        return orderPrimaryURL;
    }

    @JsonProperty("OrderPrimaryURL")
    public void setOrderPrimaryURL(OrderPrimaryURL orderPrimaryURL) {
        this.orderPrimaryURL = orderPrimaryURL;
    }

    @JsonProperty("OrderProductDetails")
    public OrderProductDetails getOrderProductDetails() {
        return orderProductDetails;
    }

    @JsonProperty("OrderProductDetails")
    public void setOrderProductDetails(OrderProductDetails orderProductDetails) {
        this.orderProductDetails = orderProductDetails;
    }

    @JsonProperty("OrderSupplierName")
    public OrderSupplierName getOrderSupplierName() {
        return orderSupplierName;
    }

    @JsonProperty("OrderSupplierName")
    public void setOrderSupplierName(OrderSupplierName orderSupplierName) {
        this.orderSupplierName = orderSupplierName;
    }

}