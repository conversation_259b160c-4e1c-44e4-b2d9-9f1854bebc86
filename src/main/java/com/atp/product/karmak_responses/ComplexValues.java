package com.atp.product.karmak_responses;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "PurchaseOrders"
})
public class ComplexValues {

    @JsonProperty("PurchaseOrders")
    private List<PurchaseOrder> purchaseOrders;

    @JsonProperty("PurchaseOrders")
    public List<PurchaseOrder> getPurchaseOrders() {
        return purchaseOrders;
    }

    @JsonProperty("PurchaseOrders")
    public void setPurchaseOrders(List<PurchaseOrder> purchaseOrders) {
        this.purchaseOrders = purchaseOrders;
    }

}