package com.atp.product.karmak_responses;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "parentId",
        "entityType",
        "workspaceId",
        "datatypeDefinitionId",
        "simpleValues",
        "completeness",
        "deleted",
        "systemEntity",
        "approvalStatus",
        "externalId",
        "name",
        "Values",
        "path",
        "ComplexValues"
})
public class KarmakPurchaseOrder {

    @JsonProperty("parentId")
    private String parentId;
    @JsonProperty("entityType")
    private String entityType;
    @JsonProperty("workspaceId")
    private String workspaceId;
    @JsonProperty("datatypeDefinitionId")
    private String datatypeDefinitionId;
    @JsonProperty("simpleValues")
    private SimpleValues simpleValues;
    @JsonProperty("completeness")
    private Long completeness;
    @JsonProperty("deleted")
    private Boolean deleted;
    @JsonProperty("systemEntity")
    private Boolean systemEntity;
    @JsonProperty("approvalStatus")
    private String approvalStatus;
    @JsonProperty("externalId")
    private String externalId;
    @JsonProperty("name")
    private String name;
    @JsonProperty("Values")
    private List<Value> values;
    @JsonProperty("path")
    private List<String> path;
    @JsonProperty("ComplexValues")
    private ComplexValues complexValues;

    @JsonProperty("parentId")
    public String getParentId() {
        return parentId;
    }

    @JsonProperty("parentId")
    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    @JsonProperty("entityType")
    public String getEntityType() {
        return entityType;
    }

    @JsonProperty("entityType")
    public void setEntityType(String entityType) {
        this.entityType = entityType;
    }

    @JsonProperty("workspaceId")
    public String getWorkspaceId() {
        return workspaceId;
    }

    @JsonProperty("workspaceId")
    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    @JsonProperty("datatypeDefinitionId")
    public String getDatatypeDefinitionId() {
        return datatypeDefinitionId;
    }

    @JsonProperty("datatypeDefinitionId")
    public void setDatatypeDefinitionId(String datatypeDefinitionId) {
        this.datatypeDefinitionId = datatypeDefinitionId;
    }

    @JsonProperty("simpleValues")
    public SimpleValues getSimpleValues() {
        return simpleValues;
    }

    @JsonProperty("simpleValues")
    public void setSimpleValues(SimpleValues simpleValues) {
        this.simpleValues = simpleValues;
    }

    @JsonProperty("completeness")
    public Long getCompleteness() {
        return completeness;
    }

    @JsonProperty("completeness")
    public void setCompleteness(Long completeness) {
        this.completeness = completeness;
    }

    @JsonProperty("deleted")
    public Boolean getDeleted() {
        return deleted;
    }

    @JsonProperty("deleted")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @JsonProperty("systemEntity")
    public Boolean getSystemEntity() {
        return systemEntity;
    }

    @JsonProperty("systemEntity")
    public void setSystemEntity(Boolean systemEntity) {
        this.systemEntity = systemEntity;
    }

    @JsonProperty("approvalStatus")
    public String getApprovalStatus() {
        return approvalStatus;
    }

    @JsonProperty("approvalStatus")
    public void setApprovalStatus(String approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    @JsonProperty("externalId")
    public String getExternalId() {
        return externalId;
    }

    @JsonProperty("externalId")
    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    @JsonProperty("name")
    public String getName() {
        return name;
    }

    @JsonProperty("name")
    public void setName(String name) {
        this.name = name;
    }

    @JsonProperty("Values")
    public List<Value> getValues() {
        return values;
    }

    @JsonProperty("Values")
    public void setValues(List<Value> values) {
        this.values = values;
    }

    @JsonProperty("path")
    public List<String> getPath() {
        return path;
    }

    @JsonProperty("path")
    public void setPath(List<String> path) {
        this.path = path;
    }

    @JsonProperty("ComplexValues")
    public ComplexValues getComplexValues() {
        return complexValues;
    }

    @JsonProperty("ComplexValues")
    public void setComplexValues(ComplexValues complexValues) {
        this.complexValues = complexValues;
    }

}