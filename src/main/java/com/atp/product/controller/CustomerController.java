package com.atp.product.controller;

import com.atp.product.controller.dto.response.CommonResponse;
import com.atp.product.exception.DomainException;
import com.atp.product.exception.bad_request.InvalidInputException;
import com.atp.product.service.impl.CommonServiceImpl;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/customers")
public class CustomerController {

    private final CommonServiceImpl commonService;

    public CustomerController(CommonServiceImpl commonService) {
        this.commonService = commonService;
    }


    @ApiOperation(value = "Get Customer ID by Customer Key")
    @GetMapping("/getCustomerIdByCustomerKey")
    public ResponseEntity<CommonResponse> getCustomerIdByCustomerKey(
            @RequestParam("karmakCustomerKey") String karmakCustomerKey) {
        try {
            // Check for invalid input
            if (karmakCustomerKey == null || karmakCustomerKey.isBlank()) {
                // Return 200 OK with error message in the response body
                return ResponseEntity.ok(
                        new CommonResponse(false, "Input parameter 'karmakCustomerKey' is missing", null)
                );
            }

            // Get customer details from the service
            Map<String, String> customerDetails = commonService.getCustomerIdByCustomerKey(karmakCustomerKey);

            // If no customer details are found, return 200 OK with a "not found" message
            if (customerDetails.isEmpty()) {
                return ResponseEntity.ok(
                        new CommonResponse(false, "No customer details found for the given CustomerKey.", null)
                );
            }

            // Return 200 OK with the customer details
            return ResponseEntity.ok(
                    new CommonResponse(true, "Customer details retrieved successfully.", customerDetails)
            );
        } catch (DomainException e) {
            log.error("Domain error while fetching customer details: {}", e.getMessage(), e);
            // Return 200 OK with error message in the response body for exception handling
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                    new CommonResponse(false, "An error occurred while fetching customer details.", null)
            );
        }
    }
}
