package com.atp.product.controller;

import com.atp.product.controller.base.ResourceBase;
import com.atp.product.controller.dto.request.FilterRequest;
import com.atp.product.controller.dto.response.*;
import com.atp.product.exception.DomainException;
import com.atp.product.exception.bad_request.InvalidInputException;
import com.atp.product.service.CategoryService;
import com.atp.product.utils.Constants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@Api(value = "Category and Subcategory API")
@Slf4j
@RestController
@RequestMapping(value= "/api/category")
public class CategoryController {

    private final CategoryService categoryService;

    public CategoryController(CategoryService categoryService) {
        this.categoryService = categoryService;
    }

    @ApiOperation(value= "Get Category and sub category list for Home Page Screen")
    @GetMapping(value = "/getCategorySubcategoryList")
    public ResponseEntity<CommonResponse> getCategorySubcategoryList() {
        log.info("Inside getCategorySubcategoryList() API");
        try {
            List<CategorySubcategoryResponse> categorySubcategoryResponses = categoryService.getCategorySubCategoryList();
            return ResponseEntity.ok(new CommonResponse(true,"Category and subcategory mapping details retrieved successfully.",categorySubcategoryResponses));
        }
        catch (DomainException e){
            log.error("Unexpected error while retrieving category and subCategory: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while retrieving category and subCategory");
        }
    }

    /*@ApiOperation(value= "Get Filter Option for product catalog")
    @PostMapping(value = "/getAllFilterListForProductCatalog")
    public ResponseEntity<CommonResponse> getAllFilterListForProductCatalog(@RequestBody FilterRequest filterRequest) {
        log.info("Inside getAllFilterListForProductCatalog() API");
        try {
            if(Objects.nonNull(filterRequest)) {
                FilterResponse filterResponse = categoryService.getAllFilterListForProductCatalog(filterRequest);
                return ResponseEntity.ok(new CommonResponse(true,"All Filter list retrieved successfully.", filterResponse));
            }
            else {
                throw new InvalidInputException("Request body is invalid or missing");
            }
        }
        catch (DomainException e){
            log.error("Unexpected error while retrieving product catalog details: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while retrieving product catalog details");
        }
    }*/

    C

    @ApiOperation(value= "Get Category details by brand for Menu bar")
    @GetMapping(value = "/getCategoryListByBrand")
    public ResponseEntity<CommonResponse> getCategoryListByBrand() {
        log.info("Inside getCategoryListByBrand() API");
        try {
            List<BrandCategoryResponse> brandResponseHomePagesList = categoryService.getCategoryListByBrand();
            return ResponseEntity.ok(new CommonResponse(true,"Brand and category mapping details retrieved successfully.", brandResponseHomePagesList));
        } catch (Exception e) {
            log.error("Unexpected error while retrieving categories details: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while retrieving category list by brand details");
        }
    }
}
