package com.atp.product.controller;

import com.atp.product.service.InvoiceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * Controller for invoice generation
 */
@RestController
@RequestMapping("/api/invoices")
@Tag(name = "Invoice Management", description = "APIs for generating invoices")
@Slf4j
public class InvoiceController {

    private final InvoiceService invoiceService;

    @Autowired
    public InvoiceController(InvoiceService invoiceService) {
        this.invoiceService = invoiceService;
    }

    @Operation(summary = "Generate invoice PDF", 
               description = "Generate a PDF invoice for a purchase order")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Invoice generated successfully"),
        @ApiResponse(responseCode = "404", description = "Purchase order not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/generate")
    public ResponseEntity<ByteArrayResource> generateInvoice(
            @Parameter(description = "Customer name") @RequestParam String name,
            @Parameter(description = "Customer email") @RequestParam String email,
            @Parameter(description = "External order ID") @RequestParam String externalId) {
        
        log.info("Received invoice generation request for customer: {}, email: {}, externalId: {}", 
                name, email, externalId);
        
        try {
            ByteArrayResource invoicePdf = invoiceService.generateInvoice(name, email, externalId);
            
            if (invoicePdf == null) {
                log.warn("Invoice generation failed - order not found for externalId: {}", externalId);
                return ResponseEntity.notFound().build();
            }
            
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, 
                       "attachment; filename=invoice_" + externalId + ".pdf");
            
            log.info("Invoice generated successfully for externalId: {}", externalId);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentType(MediaType.APPLICATION_PDF)
                    .body(invoicePdf);
            
        } catch (Exception e) {
            log.error("Error generating invoice for externalId: {}", externalId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "Generate invoice by order ID", 
               description = "Generate a PDF invoice using purchase order ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Invoice generated successfully"),
        @ApiResponse(responseCode = "404", description = "Purchase order not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/generate/{orderId}")
    public ResponseEntity<ByteArrayResource> generateInvoiceById(
            @Parameter(description = "Purchase order ID") @PathVariable String orderId,
            @Parameter(description = "Customer name") @RequestParam String name,
            @Parameter(description = "Customer email") @RequestParam String email) {
        
        log.info("Received invoice generation request for orderId: {}, customer: {}, email: {}", 
                orderId, name, email);
        
        try {
            ByteArrayResource invoicePdf = invoiceService.generateInvoiceById(orderId, name, email);
            
            if (invoicePdf == null) {
                log.warn("Invoice generation failed - order not found for orderId: {}", orderId);
                return ResponseEntity.notFound().build();
            }
            
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION, 
                       "attachment; filename=invoice_" + orderId + ".pdf");
            
            log.info("Invoice generated successfully for orderId: {}", orderId);
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentType(MediaType.APPLICATION_PDF)
                    .body(invoicePdf);
            
        } catch (Exception e) {
            log.error("Error generating invoice for orderId: {}", orderId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
