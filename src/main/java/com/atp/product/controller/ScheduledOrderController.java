package com.atp.product.controller;

import com.atp.product.controller.dto.request.ScheduledOrderRequest;
import com.atp.product.controller.dto.request.UpdateScheduleRequest;
import com.atp.product.controller.dto.response.ScheduledOrderResponse;
import com.atp.product.service.ScheduledOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST Controller for managing scheduled orders
 */
@Slf4j
@RestController
@RequestMapping("/api/scheduled-orders")
@Tag(name = "Scheduled Orders", description = "API for managing scheduled orders and subscriptions")
public class ScheduledOrderController {

    private final ScheduledOrderService scheduledOrderService;

    @Autowired
    public ScheduledOrderController(ScheduledOrderService scheduledOrderService) {
        this.scheduledOrderService = scheduledOrderService;
    }

    @Operation(summary = "Subscribe to scheduled orders", 
               description = "Create a new scheduled order subscription for automatic recurring orders")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Scheduled order created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "404", description = "Original order not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/subscribe")
    public ResponseEntity<ScheduledOrderResponse> subscribe(
            @Valid @RequestBody ScheduledOrderRequest request) {
        
        log.info("Received scheduled order subscription request for customer: {}", request.getCustomerId());
        
        try {
            ScheduledOrderResponse response = scheduledOrderService.subscribe(request);
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
            
        } catch (IllegalArgumentException e) {
            log.error("Invalid request for scheduled order subscription: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
            
        } catch (Exception e) {
            log.error("Error creating scheduled order subscription", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "Cancel scheduled order", 
               description = "Cancel an existing scheduled order subscription")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Scheduled order cancelled successfully"),
        @ApiResponse(responseCode = "404", description = "Scheduled order not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @DeleteMapping("/{scheduledOrderId}")
    public ResponseEntity<Void> cancel(
            @Parameter(description = "Scheduled order ID") @PathVariable String scheduledOrderId,
            @Parameter(description = "Customer ID") @RequestParam String customerId) {
        
        log.info("Received cancellation request for scheduled order: {} by customer: {}", 
                scheduledOrderId, customerId);
        
        try {
            boolean cancelled = scheduledOrderService.cancel(scheduledOrderId, customerId);
            
            if (cancelled) {
                return ResponseEntity.ok().build();
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("Error cancelling scheduled order: {}", scheduledOrderId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "Update scheduled order", 
               description = "Update scheduling parameters for an existing scheduled order")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Scheduled order updated successfully"),
        @ApiResponse(responseCode = "404", description = "Scheduled order not found"),
        @ApiResponse(responseCode = "400", description = "Invalid update data"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PutMapping("/{scheduledOrderId}")
    public ResponseEntity<ScheduledOrderResponse> updateSchedule(
            @Parameter(description = "Scheduled order ID") @PathVariable String scheduledOrderId,
            @Valid @RequestBody UpdateScheduleRequest request) {
        
        log.info("Received update request for scheduled order: {}", scheduledOrderId);
        
        try {
            ScheduledOrderResponse response = scheduledOrderService.updateSchedule(scheduledOrderId, request);
            
            if (response != null) {
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("Error updating scheduled order: {}", scheduledOrderId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "Get scheduled orders by customer", 
               description = "Retrieve all scheduled orders for a specific customer")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Scheduled orders retrieved successfully"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/customer/{customerId}")
    public ResponseEntity<List<ScheduledOrderResponse>> getScheduledOrdersByCustomer(
            @Parameter(description = "Customer ID") @PathVariable String customerId) {
        
        log.info("Received request to get scheduled orders for customer: {}", customerId);
        
        try {
            List<ScheduledOrderResponse> orders = scheduledOrderService.getScheduledOrdersByCustomer(customerId);
            return ResponseEntity.ok(orders);
            
        } catch (Exception e) {
            log.error("Error retrieving scheduled orders for customer: {}", customerId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "Get scheduled order by ID", 
               description = "Retrieve a specific scheduled order by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Scheduled order retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Scheduled order not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/{scheduledOrderId}")
    public ResponseEntity<ScheduledOrderResponse> getScheduledOrderById(
            @Parameter(description = "Scheduled order ID") @PathVariable String scheduledOrderId,
            @Parameter(description = "Customer ID") @RequestParam String customerId) {
        
        log.info("Received request to get scheduled order: {} for customer: {}", scheduledOrderId, customerId);
        
        try {
            ScheduledOrderResponse order = scheduledOrderService.getScheduledOrderById(scheduledOrderId, customerId);
            
            if (order != null) {
                return ResponseEntity.ok(order);
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("Error retrieving scheduled order: {}", scheduledOrderId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "Pause scheduled order", 
               description = "Temporarily pause a scheduled order")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Scheduled order paused successfully"),
        @ApiResponse(responseCode = "404", description = "Scheduled order not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PatchMapping("/{scheduledOrderId}/pause")
    public ResponseEntity<Void> pauseScheduledOrder(
            @Parameter(description = "Scheduled order ID") @PathVariable String scheduledOrderId,
            @Parameter(description = "Customer ID") @RequestParam String customerId) {
        
        log.info("Received request to pause scheduled order: {} for customer: {}", scheduledOrderId, customerId);
        
        try {
            boolean paused = scheduledOrderService.pauseScheduledOrder(scheduledOrderId, customerId);
            
            if (paused) {
                return ResponseEntity.ok().build();
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("Error pausing scheduled order: {}", scheduledOrderId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "Resume scheduled order", 
               description = "Resume a paused scheduled order")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Scheduled order resumed successfully"),
        @ApiResponse(responseCode = "404", description = "Scheduled order not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PatchMapping("/{scheduledOrderId}/resume")
    public ResponseEntity<Void> resumeScheduledOrder(
            @Parameter(description = "Scheduled order ID") @PathVariable String scheduledOrderId,
            @Parameter(description = "Customer ID") @RequestParam String customerId) {
        
        log.info("Received request to resume scheduled order: {} for customer: {}", scheduledOrderId, customerId);
        
        try {
            boolean resumed = scheduledOrderService.resumeScheduledOrder(scheduledOrderId, customerId);
            
            if (resumed) {
                return ResponseEntity.ok().build();
            } else {
                return ResponseEntity.notFound().build();
            }
            
        } catch (Exception e) {
            log.error("Error resuming scheduled order: {}", scheduledOrderId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "Get all active scheduled orders", 
               description = "Retrieve all active scheduled orders (admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Active scheduled orders retrieved successfully"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/admin/active")
    public ResponseEntity<List<ScheduledOrderResponse>> getAllActiveScheduledOrders() {
        
        log.info("Received request to get all active scheduled orders");
        
        try {
            List<ScheduledOrderResponse> orders = scheduledOrderService.getAllActiveScheduledOrders();
            return ResponseEntity.ok(orders);
            
        } catch (Exception e) {
            log.error("Error retrieving all active scheduled orders", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(summary = "Trigger scheduled order processing", 
               description = "Manually trigger the processing of due scheduled orders (admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Scheduled order processing triggered successfully"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/admin/process")
    public ResponseEntity<Void> triggerScheduledOrderProcessing() {
        
        log.info("Received request to manually trigger scheduled order processing");
        
        try {
            scheduledOrderService.processScheduledOrders();
            return ResponseEntity.ok().build();
            
        } catch (Exception e) {
            log.error("Error triggering scheduled order processing", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
