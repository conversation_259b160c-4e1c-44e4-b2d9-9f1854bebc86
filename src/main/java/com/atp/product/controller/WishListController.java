package com.atp.product.controller;

import com.atp.product.controller.dto.request.WishlistRequest;
import com.atp.product.controller.dto.response.CommonResponse;
import com.atp.product.controller.dto.response.WishlistResponse;
import com.atp.product.exception.DomainException;
import com.atp.product.exception.bad_request.InvalidInputException;
import com.atp.product.service.WishListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@Api(value = "WishList API")
@RestController
@Slf4j
@RequestMapping(value= "/api/WishList", produces= MediaType.APPLICATION_JSON_VALUE)
public class WishListController {
    private final WishListService wishListService;
    public WishListController(WishListService wishListService) {
        this.wishListService = wishListService;
    }

    @ApiOperation(value= "Get Wishlist list")
    @GetMapping(value = "/getWishlist")
    public ResponseEntity<CommonResponse> getWishlist(@RequestParam("customerCorrelationId") String customerCorrelationId) {
        log.info("Inside getWishlist() API");
        try {
            return Optional.ofNullable(customerCorrelationId)
                    .filter(id -> !id.isBlank())
                    .map(id -> {
                        WishlistResponse wishlistResponse = wishListService.getWishlist(id);
                        return ResponseEntity.ok(new CommonResponse(true,"Wishlist details retrieved successfully.", wishlistResponse));
                    })
                    .orElseThrow(() -> new IllegalArgumentException("Input parameter 'customerCorrelationId' is invalid or missing"));
        } catch (DomainException e) {
            log.error("Unexpected error while retrieving retrieving wishlist: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while retrieving wishlist");
        }
    }
    @ApiOperation(value= "Save Wishlist details")
    @PostMapping(value = "/saveWishlistDetails")
    public ResponseEntity<CommonResponse> saveWishlistDetails(@RequestBody WishlistRequest wishlistRequest) {
        log.info("Inside saveWishlistDetails() API");
        try {
            return Optional.ofNullable(wishlistRequest)
                    .map(requestData -> {
                        Integer totalProductCount = wishListService.saveWishlistDetails(requestData);
                        String message = (totalProductCount != 0)
                                ? "Wishlist details saved successfully."
                                : "Failed to save Wishlist details.";
                        return ResponseEntity.ok(new CommonResponse(true, message, totalProductCount));
                    })
                    .orElseThrow(() -> new InvalidInputException("Request body is invalid or missing"));
        } catch (DomainException e) {
            log.error("Unexpected error while retrieving saving wishlist details: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while saving wishlist details");
        }
    }
    @ApiOperation(value= "Update Wishlist details")
    @PostMapping(value = "/updateWishlistDetails")
    public ResponseEntity<CommonResponse> updateWishlistDetails(@RequestBody WishlistRequest wishlistRequest) {
        log.info("Inside updateWishlistDetails() API");
        try {
            return Optional.ofNullable(wishlistRequest)
                    .map(requestData -> {
                        boolean isSaved = wishListService.updateWishlistDetails(requestData);
                        String message = isSaved
                                ? "Wishlist details updated successfully."
                                : "Failed to update Wishlist details.";
                        return ResponseEntity.ok(new CommonResponse(isSaved, message, ""));
                    })
                    .orElseThrow(() -> new InvalidInputException("Request body is invalid or missing"));
        } catch (DomainException e) {
            log.error("Unexpected error while retrieving updating wishlist details: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while updating wishlist details");
        }
    }
    @ApiOperation(value= "Delete all Wishlist details")
    @GetMapping(value = "/deleteWishlistByCustomerCorrelationId")
    public ResponseEntity<CommonResponse> deleteWishlistByCustomerCorrelationId(@RequestParam("customerCorrelationId") String customerCorrelationId) {
        log.info("Inside deleteWishlistByCustomerCorrelationId() API");
        try {
            return Optional.ofNullable(customerCorrelationId)
                    .filter(id -> !id.isBlank())
                    .map(id -> {
                        boolean isDeleted = wishListService.deleteWishlistByCustomerCorrelationId(id);
                        return ResponseEntity.ok(isDeleted
                                ? new CommonResponse(true, "Wishlist details deleted successfully.", "")
                                : new CommonResponse(false, "Failed to delete Wishlist details.", ""));
                    })
                    .orElseThrow(() -> new InvalidInputException("Input parameter 'customerCorrelationId' is invalid or missing"));
        } catch (DomainException e) {
            log.error("Unexpected error while retrieving deleting wishlist details: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while deleting wishlist details");
        }
    }
    @ApiOperation(value= "Delete particular one product in the Wishlist details")
    @PostMapping(value = "/deleteWishlistDetailsByPartNumber")
    public ResponseEntity<CommonResponse> deleteWishlistDetailsByPartNumber(@RequestBody WishlistRequest wishlistRequest) {
        log.info("Inside deleteAllWishlistDetails() API");
        try {
            return Optional.ofNullable(wishlistRequest)
                    .map(requestData -> {
                        boolean isDeleted = wishListService.deleteWishlistDetailsByPartNumber(requestData);
                        return ResponseEntity.ok(isDeleted
                                ? new CommonResponse(true, "Wishlist details deleted successfully.", "")
                                : new CommonResponse(false, "Failed to delete Wishlist details.", ""));
                    })
                    .orElseThrow(() -> new InvalidInputException("Request body is invalid or missing"));
        } catch (DomainException e) {
            log.error("Unexpected error while retrieving deleting wishlist details: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while deleting wishlist details");
        }
    }
}
