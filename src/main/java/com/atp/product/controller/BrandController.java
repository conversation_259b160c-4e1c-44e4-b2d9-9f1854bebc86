package com.atp.product.controller;

import com.atp.product.controller.base.ResourceBase;
import com.atp.product.controller.dto.response.*;
import com.atp.product.exception.DomainException;
import com.atp.product.service.BrandService;
import com.atp.product.exception.bad_request.InvalidInputException;
import com.atp.product.utils.Constants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(value = "Brand API")
@Slf4j
@RestController
@RequestMapping(value= "/api/brand", produces= MediaType.APPLICATION_JSON_VALUE)
public class BrandController {

    private final BrandService brandService;

    public BrandController(BrandService brandService) {
        this.brandService = brandService;
    }

    @ApiOperation(value= "Get Shop by brand details for Home Page Screen")
    @GetMapping(value = "/getShopByBrands")
    public ResponseEntity<CommonResponse> getShopByBrands() {
        log.info("Inside getShopByBrands() API");
        try {
            List<BrandResponseHomePage> brandResponseHomePagesList = brandService.getShopByBrands();
            return ResponseEntity.ok(new CommonResponse(true,"Featured brand details retrieved successfully.",brandResponseHomePagesList));
        } catch (DomainException e) {
            log.error("Unexpected error while retrieving shop by brand details: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while retrieving shop by brand details");
        }
    }

    @ApiOperation(value= "Get Brand details by category for Menu bar")
    @GetMapping(value = "/getBrandListByCategory")
    public ResponseEntity<CommonResponse> getBrandListByCategory() {
        log.info("Inside getBrandListByCategory() API");
        try {
            List<CategoryBrandResponse> brandResponseHomePagesList = brandService.getBrandListByCategory();
            return ResponseEntity.ok(new CommonResponse(true,"Brand details retrieved successfully.",brandResponseHomePagesList));
        } catch (DomainException e) {
            log.error("Unexpected error while retrieving brand list by category: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while retrieving brand list by category details");
        }
    }

   @ApiOperation(value= "Get Brand details, brand specific category, brand specific new product and brand specific popular product by brand for brand page")
   @GetMapping(value = "/getBrandPageByBrandSlug")
   public ResponseEntity<CommonResponse> getBrandPageByBrandSlug(@RequestParam("brandSlug") String brandSlug) {
       log.info("Inside getBrandPageByBrandSlug() API");
       try {
           if (StringUtils.isBlank(brandSlug)) {
               log.error(Constants.INPUT_PARAMETER_NOT_FOUND);
               throw new InvalidInputException("Input parameter 'brandSlug' is invalid or missing");
           }
           BrandPageResponse brandPageResponse = brandService.getBrandPageByBrandSlug(brandSlug);
           return ResponseEntity.ok(new CommonResponse(true,"Brand page details retrieved successfully.", brandPageResponse));
       }
       catch (DomainException e) {
           log.error("Unexpected error while retrieving brand details: {}", e.getMessage(), e);
           throw new DomainException("Something went wrong while retrieving brand details");
       }
   }
}
