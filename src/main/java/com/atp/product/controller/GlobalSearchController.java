package com.atp.product.controller;

import com.atp.product.controller.base.ResourceBase;
import com.atp.product.controller.dto.response.*;
import com.atp.product.service.GlobalSearchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(value = "Product API")
@RestController
@RequestMapping(value= "/api/search", produces= MediaType.APPLICATION_JSON_VALUE)
public class GlobalSearchController extends ResourceBase {
    private final GlobalSearchService globalSearchService;

    public GlobalSearchController(GlobalSearchService globalSearchService) {
        this.globalSearchService = globalSearchService;
    }

    @GetMapping("/typeAhead")
    @ApiOperation(value = "Type Ahead Search", notes = "Search for products by name, description, and category")
    public ResponseEntity<CommonResponse> typeAheadSearch(@RequestParam("queryString") String queryString, @RequestParam("category") String category) {
        List<GlobalSearchResponse> filterResponse = globalSearchService.typeAheadSearch(queryString,category);
        return commonResponse("Product details retrieved successfully.",filterResponse);
    }
}
