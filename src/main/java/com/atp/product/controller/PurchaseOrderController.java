package com.atp.product.controller;

import com.atp.product.controller.dto.request.PurchaseOrderRequest;
import com.atp.product.controller.dto.response.PurchaseOrderResponse;
import com.atp.product.service.impl.CommonServiceImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/purchase-order")
public class PurchaseOrderController {

    private final CommonServiceImpl commonService;

    public PurchaseOrderController(CommonServiceImpl commonService) {
        this.commonService = commonService;
    }

    @PostMapping("/create")
    public ResponseEntity<PurchaseOrderResponse> createPurchaseOrder(@RequestBody PurchaseOrderRequest poRequest) {
        PurchaseOrderResponse response = commonService.createPurchaseOrder(poRequest);
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }
}
