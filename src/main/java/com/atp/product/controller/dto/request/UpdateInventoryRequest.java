package com.atp.product.controller.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class UpdateInventoryRequest {
    @JsonProperty("Branch")
    private String branch;
    @JsonProperty("PartNumber")
    private String partNumber;
    @JsonProperty("Supplier")
    private String supplier;
    @JsonProperty("QuantityAvailable")
    private double quantityAvailable;
}
