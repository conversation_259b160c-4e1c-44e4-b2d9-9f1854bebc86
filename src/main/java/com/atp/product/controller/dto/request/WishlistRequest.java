package com.atp.product.controller.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WishlistRequest {
    private String customerCorrelationId;
    private String partNumber;
  //  private int productQuantity;
    @JsonProperty("isMovedWishlist")
    private boolean isMovedWishlist;
}
