package com.atp.product.controller.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShoppingCartRequest {
    private String customerCorrelationId;
    private String partNumber;
    private int productQuantity;
    @JsonProperty("isMovedToCart")
    private boolean isMovedToCart;
}
