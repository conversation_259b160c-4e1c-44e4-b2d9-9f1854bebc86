package com.atp.product.controller.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * Request DTO for creating scheduled orders
 */
@Data
public class ScheduledOrderRequest {

    @JsonProperty("customerId")
    @NotBlank(message = "Customer ID is required")
    private String customerId;

    @JsonProperty("originalOrderId")
    @NotBlank(message = "Original order ID is required")
    private String originalOrderId;

    @JsonProperty("frequencyDays")
    @NotNull(message = "Frequency in days is required")
    @Min(value = 1, message = "Frequency must be at least 1 day")
    private Integer frequencyDays;

    @JsonProperty("notes")
    private String notes;

    @JsonProperty("notifyOnOutOfStock")
    private Boolean notifyOnOutOfStock = true;
}
