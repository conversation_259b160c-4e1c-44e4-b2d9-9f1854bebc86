package com.atp.product.controller.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * Response DTO for scheduled orders
 */
@Data
public class ScheduledOrderResponse {

    @JsonProperty("id")
    private String id;

    @JsonProperty("customerId")
    private String customerId;

    @JsonProperty("originalOrderId")
    private String originalOrderId;

    @JsonProperty("subscriptionDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate subscriptionDate;

    @JsonProperty("frequencyDays")
    private Integer frequencyDays;

    @JsonProperty("nextRunDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate nextRunDate;

    @JsonProperty("endDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @JsonProperty("active")
    private Boolean active;

    @JsonProperty("notes")
    private String notes;

    @JsonProperty("ordersProcessed")
    private Integer ordersProcessed;

    @JsonProperty("lastProcessedDate")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime lastProcessedDate;

    @JsonProperty("notifyOnOutOfStock")
    private Boolean notifyOnOutOfStock;

    @JsonProperty("status")
    private String status; // ACTIVE, CANCELLED, COMPLETED

    @JsonProperty("createdAt")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createdAt;

    @JsonProperty("updatedAt")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime updatedAt;
}
