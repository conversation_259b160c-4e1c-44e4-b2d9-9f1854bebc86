package com.atp.product.controller.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FilterRequest {
    private List<String> subCategoryList;
    private List<String> makeList;
    private List<String> modelList;
    private List<String> vendorList;
    private List<String> yearList;
    private List<String> brandList;
    private int pageSize;
    private int size;
    private String sort;
    private String queryString;
    private List<String> categoryList;
}
