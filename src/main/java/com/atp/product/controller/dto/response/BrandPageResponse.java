package com.atp.product.controller.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BrandPageResponse {
    String brandName;
    String brandDescription;
    String brandLogoUrl;
    String brandSlug;
    String brandId;
    List<CategoryResponse> categoryList;
    List<ProductResponseHomePage> newProductList;
    List<ProductResponseHomePage> popularProductList;
}
