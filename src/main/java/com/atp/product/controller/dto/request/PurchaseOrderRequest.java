package com.atp.product.controller.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class PurchaseOrderRequest {

    @JsonProperty("LocationID")
    private int locationID;

    @JsonProperty("FillingLocationID")
    private int fillingLocationID;

    @JsonProperty("APVendorID")
    private int apVendorID;

    @JsonProperty("OrderedBy")
    private String orderedBy;

    @JsonProperty("SupplierID")
    private int supplierID;

    @JsonProperty("PODate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate poDate;

    @JsonProperty("IsDirectShip")
    private boolean isDirectShip;

    @JsonProperty("LineItems")
    private List<LineItem> lineItems;

    @Data
    public static class LineItem {
        @JsonProperty("PartID")
        private int partID;

        @JsonProperty("Quantity")
        private int quantity;

        @JsonProperty("Cost")
        private double cost;

        @JsonProperty("Message")
        private String message;
    }

    @JsonProperty("Shipping")
    private InternalPurchaseOrderRequest.Shipping shipping;
}
