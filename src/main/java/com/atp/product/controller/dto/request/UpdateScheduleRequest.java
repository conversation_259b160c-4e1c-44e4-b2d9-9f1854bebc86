package com.atp.product.controller.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import jakarta.validation.constraints.Min;
import java.time.LocalDate;

/**
 * Request DTO for updating scheduled orders
 */
@Data
public class UpdateScheduleRequest {

    @JsonProperty("customerId")
    private String customerId;

    @JsonProperty("frequencyDays")
    @Min(value = 1, message = "Frequency must be at least 1 day")
    private Integer frequencyDays;

    @JsonProperty("nextRunDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate nextRunDate;

    @JsonProperty("notes")
    private String notes;

    @JsonProperty("notifyOnOutOfStock")
    private Boolean notifyOnOutOfStock;

    @JsonProperty("active")
    private Boolean active;
}
