package com.atp.product.controller.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import jakarta.validation.constraints.Min;
import java.time.LocalDate;

/**
 * Request DTO for updating scheduled orders
 */
@Data
public class UpdateScheduleRequest {

    @JsonProperty("customerId")
    private String customerId;

    @JsonProperty("frequencyDays")
    @Min(value = 1, message = "Frequency must be at least 1 day")
    private Integer frequencyDays;

    @JsonProperty("nextRunDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate nextRunDate;

    @JsonProperty("endDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;

    @JsonProperty("notes")
    private String notes;

    @JsonProperty("autoRenew")
    private Boolean autoRenew;

    @JsonProperty("maxOrders")
    @Min(value = 1, message = "Max orders must be at least 1")
    private Integer maxOrders;

    @JsonProperty("notifyOnOutOfStock")
    private Boolean notifyOnOutOfStock;

    @JsonProperty("notifyOnSuccess")
    private Boolean notifyOnSuccess;

    @JsonProperty("active")
    private Boolean active;
}
