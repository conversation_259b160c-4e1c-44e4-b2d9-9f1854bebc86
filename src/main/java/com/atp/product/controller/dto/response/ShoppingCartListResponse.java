package com.atp.product.controller.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShoppingCartListResponse {
    private String customerCorrelationId;
    private double grandTotal;
    private int totalProductCount;
    private List<ProductResponseHomePage> productDetails;
}
