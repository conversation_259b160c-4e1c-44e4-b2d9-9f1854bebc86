package com.atp.product.controller.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FilterResponse {
    private List<ProductResponseHomePage> productList;
    private Map<String,Integer> subCategoryList;
    private Map<String,Integer> brandList;
    private Map<String,Integer> yearList;
    private Map<String,Integer> makeList;
    private Map<String,Integer> modelList;
    private Map<String,Integer> vendorList;
    private Map<String,Integer> categoryList;
    private Integer totalProductCount;
}
