package com.atp.product.controller.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProductCatalogResponse {
    private String catalogId;
    private String name;
    private double price;
    private String primaryImage;
    private int quantity;
    private String brandName;
    private String vendorName;
    private List<String> model;
    private List<String> year;
    private List<String> make;
    private String category;
    private String subCategory;
}
