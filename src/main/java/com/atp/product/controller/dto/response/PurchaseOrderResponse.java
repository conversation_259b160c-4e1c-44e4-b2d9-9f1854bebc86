package com.atp.product.controller.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class PurchaseOrderResponse {

    @JsonProperty("PartsPurchaseOrder")
    private PartsPurchaseOrder partsPurchaseOrder;

    @JsonProperty("Messages")
    private List<String> messages;

    @JsonProperty("ProcessGUID")
    private String processGUID;

    @Data
    public static class PartsPurchaseOrder {
        @JsonProperty("LocationID")
        private int locationID;

        @JsonProperty("FillingLocationID")
        private int fillingLocationID;

        @JsonProperty("PartsPurchaseOrderID")
        private int partsPurchaseOrderID;

        @JsonProperty("PONumber")
        private String poNumber;

        @JsonProperty("APVendorID")
        private int apVendorID;

        @JsonProperty("SupplierID")
        private int supplierID;

        @JsonProperty("OrderedBy")
        private String orderedBy;

        @JsonProperty("POTotal")
        private double poTotal;

        @JsonProperty("LineItems")
        private List<LineItem> lineItems;
    }

    @Data
    public static class LineItem {
        @JsonProperty("PartID")
        private int partID;

        @JsonProperty("PartNumber")
        private String partNumber;

        @JsonProperty("Supplier")
        private String supplier;

        @JsonProperty("Description")
        private String description;

        @JsonProperty("Quantity")
        private int quantity;

        @JsonProperty("Cost")
        private double cost;

        @JsonProperty("ExtendedCost")
        private double extendedCost;

        @JsonProperty("InherentCoreCost")
        private double inherentCoreCost;

        @JsonProperty("InherentCoreExtendedCost")
        private double inherentCoreExtendedCost;

        @JsonProperty("OrderAsPart")
        private String orderAsPart;

        @JsonProperty("OrderAsAPVendor")
        private String orderAsAPVendor;
    }
}
