package com.atp.product.controller.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class InternalPurchaseOrderRequest {

    @JsonProperty("LocationID")
    private int locationID;

    @JsonProperty("FillingLocationID")
    private int fillingLocationID;

    @JsonProperty("APVendorID")
    private int apVendorID;

    @JsonProperty("OrderedBy")
    private String orderedBy;

    @JsonProperty("SupplierID")
    private int supplierID;

    @JsonProperty("PODate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate poDate;

    @JsonProperty("IsDirectShip")
    private boolean isDirectShip;

    @JsonProperty("LineItems")
    private List<InternalLineItem> lineItems;

    @JsonProperty("Shipping")
    private Shipping shipping;

    @Data
    public static class InternalLineItem {
        @JsonProperty("PartID")
        private int partID;

        @JsonProperty("Quantity")
        private int quantity;

        @JsonProperty("Cost")
        private double cost;

        @JsonProperty("Message")
        private String message;

        // 👇 Extra internal fields
        @JsonProperty("TotalQuantity")
        private int totalQuantity;

        @JsonProperty("Branch")
        private String branch;
    }

    @Data
    public static class Shipping {
        @JsonProperty("CompanyName")
        private String companyName;
        @JsonProperty("AddressLine1")
        private String addressLine1;
        @JsonProperty("AddressLine2")
        private String addressLine2;
        @JsonProperty("City")
        private String city;
        @JsonProperty("Region")
        private String region;
        @JsonProperty("PostalCode")
        private String postalCode;
    }
}
