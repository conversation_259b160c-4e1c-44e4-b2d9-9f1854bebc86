package com.atp.product.controller;

import com.atp.product.controller.base.ResourceBase;
import com.atp.product.controller.dto.request.ShoppingCartRequest;
import com.atp.product.controller.dto.response.CommonResponse;
import com.atp.product.controller.dto.response.ShoppingCartListResponse;
import com.atp.product.exception.DomainException;
import com.atp.product.exception.bad_request.InvalidInputException;
import com.atp.product.service.ShoppingCartService;
import com.atp.product.utils.Constants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@Api(value = "Shopping Cart API")
@RestController
@Slf4j
@RequestMapping(value= "/api/ShoppingCart", produces= MediaType.APPLICATION_JSON_VALUE)
public class ShoppingCartController {
    private final ShoppingCartService shoppingCartService;
    public ShoppingCartController(ShoppingCartService shoppingCartService) {
        this.shoppingCartService = shoppingCartService;
    }

    @ApiOperation(value= "Get Shopping cart list")
    @GetMapping(value = "/getShoppingCartList")
    public ResponseEntity<CommonResponse> getShoppingCartList(@RequestParam("customerCorrelationId") String customerCorrelationId) {
        log.info("Inside getShoppingCartList() API");
        try {
            return Optional.ofNullable(customerCorrelationId)
                    .filter(id -> !id.isBlank())
                    .map(id -> {
                        ShoppingCartListResponse shoppingCartListResponse = shoppingCartService.getShoppingCartList(id);
                        return ResponseEntity.ok(new CommonResponse(true,"Shopping cart details retrieved successfully.", shoppingCartListResponse));
                    })
                    .orElseThrow(() -> new InvalidInputException("Input parameter 'customerCorrelationId' is invalid or missing"));
        } catch (DomainException e) {
            log.error("Unexpected error while retrieving retrieving shopping cart list: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while retrieving shopping cart list");
        }
    }
    /*@ApiOperation(value= "Save Shopping cart details")
    @PostMapping(value = "/saveShoppingCartDetails")
    public ResponseEntity<CommonResponse> saveShoppingCartDetails(@RequestBody ShoppingCartRequest shoppingCartRequest) {
        log.info("Inside saveShoppingCartDetails() API");
        try {
            return Optional.ofNullable(shoppingCartRequest)
                    .map(request -> {
                        Integer totalProductCount = shoppingCartService.saveShoppingCartDetails(request);
                        String message = (totalProductCount != 0)
                                ? "Shopping cart details saved successfully."
                                : "Failed to save shopping cart details.";
                        return ResponseEntity.ok(new CommonResponse(true,message, totalProductCount));
                    })
                    .orElseThrow(() -> new InvalidInputException("Request body is invalid or missing"));
        } catch (DomainException e) {
            log.error("Unexpected error while while saving the shopping cart details: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while saving the shopping cart details");
        }
    }*/
    @ApiOperation(value= "Save Shopping cart details")
    @PostMapping(value = "/saveShoppingCartDetails")
    public ResponseEntity<CommonResponse> saveShoppingCartDetails(@RequestBody ShoppingCartRequest shoppingCartRequest) {
        log.info("Inside saveShoppingCartDetails() API");
        try {
            return Optional.ofNullable(shoppingCartRequest)
                    .map(request -> {
                        Integer totalProductCount = shoppingCartService.saveShoppingCartDetails(request);
                        String message = (totalProductCount != 0)
                                ? "Shopping cart details saved successfully."
                                : "Failed to save shopping cart details.";
                        return ResponseEntity.ok(new CommonResponse(true,message, totalProductCount));
                    })
                    .orElseThrow(() -> new InvalidInputException("Request body is invalid or missing"));
        } catch (DomainException e) {
            log.error("Domain error while saving the shopping cart details: {}", e.getMessage(), e);
            return ResponseEntity.ok().body(new CommonResponse(false, e.getMessage(), null));
        } catch (Exception e) {
            log.error("Unexpected error while saving the shopping cart details: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new CommonResponse(false, "An unexpected error occurred.", null));
        }
    }
    /*@ApiOperation(value= "Update Shopping cart details")
    @PostMapping(value = "/updateShoppingCartDetails")
    public ResponseEntity<CommonResponse> updateShoppingCartDetails(@RequestBody ShoppingCartRequest shoppingCartRequest) {
        log.info("Inside updateShoppingCartDetails() API");
        try {
            return Optional.ofNullable(shoppingCartRequest)
                    .map(request -> {
                        boolean isSaved = shoppingCartService.updateShoppingCartDetails(request);
                        String message = isSaved
                                ? "Shopping cart details updated successfully."
                                : "Failed to update shopping cart details.";
                        return ResponseEntity.ok(new CommonResponse(isSaved,message, ""));
                    })
                    .orElseThrow(() -> new InvalidInputException("Request body is invalid or missing"));
        } catch (DomainException e) {
            log.error("Unexpected error while while updating the shopping cart details: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while updating the shopping cart details");
        }
    }*/
    @ApiOperation(value= "Update Shopping cart details")
    @PostMapping(value = "/updateShoppingCartDetails")
    public ResponseEntity<CommonResponse> updateShoppingCartDetails(@RequestBody ShoppingCartRequest shoppingCartRequest) {
        log.info("Inside updateShoppingCartDetails() API");
        try {
            return Optional.ofNullable(shoppingCartRequest)
                    .map(request -> {
                        boolean isSaved = shoppingCartService.updateShoppingCartDetails(request);
                        String message = isSaved
                                ? "Shopping cart details updated successfully."
                                : "Failed to update shopping cart details.";
                        return ResponseEntity.ok(new CommonResponse(isSaved,message, ""));
                    })
                    .orElseThrow(() -> new InvalidInputException("Request body is invalid or missing"));
        } catch (DomainException e) {
            log.error("Domain error while saving the shopping cart details: {}", e.getMessage(), e);
            return ResponseEntity.ok().body(new CommonResponse(false, e.getMessage(), null));
        } catch (Exception e) {
            log.error("Unexpected error while saving the shopping cart details: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(new CommonResponse(false, "An unexpected error occurred.", null));
        }
    }
    @ApiOperation(value= "Delete all shopping cart details")
    @GetMapping(value = "/deleteShoppingCartByCustomerCorrelationId")
    public ResponseEntity<CommonResponse> deleteShoppingCartByCustomerCorrelationId(@RequestParam("customerCorrelationId") String customerCorrelationId) {
        log.info("Inside deleteShoppingCartByCustomerCorrelationId() API");
        try {
            return Optional.ofNullable(customerCorrelationId)
                    .filter(id -> !id.isBlank())
                    .map(id -> {
                        boolean isDeleted = shoppingCartService.deleteShoppingCartByCustomerCorrelationId(id);
                        return ResponseEntity.ok(
                                isDeleted
                                        ? new CommonResponse(true, "Shopping cart details deleted successfully.", "")
                                        : new CommonResponse(false, "Failed to delete shopping cart details.", "")
                        );
                    })
                    .orElseThrow(() -> new InvalidInputException("Input parameter 'CustomerCorrelationId' is invalid or missing"));
        } catch (DomainException e) {
            log.error("Unexpected error while while deleting the shopping cart details: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while deleting the shopping cart details");
        }
    }
    @ApiOperation(value= "Delete particular one product in the shopping cart details")
    @PostMapping(value = "/deleteShoppingCartDetailsByPartNumber")
    public ResponseEntity<CommonResponse> deleteShoppingCartDetailsByPartNumber(@RequestBody ShoppingCartRequest shoppingCartRequest) {
        log.info("Inside deleteAllShoppingCartDetails() API");
        try {
            return Optional.ofNullable(shoppingCartRequest)
                    .map(request -> {
                        boolean isDeleted = shoppingCartService.deleteShoppingCartDetailsByPartNumber(request);
                        return ResponseEntity.ok(isDeleted
                                ? new CommonResponse(true, "Shopping cart details deleted successfully.", "")
                                : new CommonResponse(false,"Failed to delete shopping cart details.", ""));
                    })
                    .orElseThrow(() -> new InvalidInputException("Request body is invalid or missing"));
        } catch (DomainException e) {
            log.error("Unexpected error while while deleting the shopping cart details: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while deleting the shopping cart details");
        }
    }

    @ApiOperation(value= "Get total product count for shopping cart details")
    @GetMapping(value = "/getTotalProductCount")
    public ResponseEntity<CommonResponse> getTotalProductCount(@RequestParam("customerCorrelationId") String customerCorrelationId) {
        log.info("Inside getTotalProductCount() API");
        try {
            return Optional.ofNullable(customerCorrelationId)
                    .filter(id -> !id.isEmpty() && !id.isBlank())
                    .map(id -> {
                        Integer totalProductCount = shoppingCartService.getTotalProductCount(id);
                        return ResponseEntity.ok(new CommonResponse(true,"Total product count fetched successfully.", totalProductCount));
                    })
                    .orElseThrow(() -> new InvalidInputException("Input parameter 'CustomerCorrelationId' is invalid or missing"));
        } catch (DomainException e) {
            log.error("Unexpected error while while retrieving total product count: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while retrieving total product count");
        }
    }
}
