package com.atp.product.controller.base;
import com.atp.product.controller.dto.response.CommonResponse;
import com.atp.product.controller.dto.response.ErrorResponse;
import com.atp.product.exception.DomainException;
import com.atp.product.exception.bad_request.InvalidInputException;
import com.atp.product.utils.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class ResourceBase {
    public static final Logger logger = LoggerFactory.getLogger(ResourceBase.class);
    @ExceptionHandler(Exception.class)
    public ResponseEntity<CommonResponse> exceptionHandling(Throwable e) {
        CommonResponse commonResponse = new CommonResponse();
        commonResponse.setSuccess(false);
        commonResponse.setData(new ArrayList<>());
        if (e instanceof DomainException) {
            commonResponse.setMessage(e.getMessage());
        } else {
            commonResponse.setMessage("There is some problem in server. Please contact to admin.");
        }
        return ResponseEntity.ok(commonResponse);
    }

    public ResponseEntity<CommonResponse> commonResponse(String errorMsg, Object response) {
        CommonResponse commonResponse = new CommonResponse();
        commonResponse.setMessage(errorMsg);
        commonResponse.setSuccess(true);
        commonResponse.setData(response);
        return ResponseEntity.ok(commonResponse);
    }
}
