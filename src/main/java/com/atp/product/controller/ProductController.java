package com.atp.product.controller;
import com.atp.product.controller.dto.response.CommonResponse;
import com.atp.product.controller.dto.response.ProductDetailsResponse;
import com.atp.product.controller.dto.response.ProductResponseHomePage;
import com.atp.product.exception.DomainException;
import com.atp.product.exception.bad_request.InvalidInputException;
import com.atp.product.service.ProductService;
import com.atp.product.utils.Constants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Objects;

@Api(value = "Product API")
@Slf4j
@RestController
@RequestMapping(value= "/api/product", produces= MediaType.APPLICATION_JSON_VALUE)
public class ProductController {

    private final ProductService productService;

    public ProductController(ProductService productService) {
        this.productService = productService;
    }

    @GetMapping(value="/healthCheck")
    public String health()
    {
        return "Product Service Running OK";
    }

    @ApiOperation(value= "Get Featured Product for Home Page Screen")
    @GetMapping(value = "/getFeaturedProducts")
    public ResponseEntity<CommonResponse> getFeaturedProducts() {
        log.info("Inside getFeaturedProducts() API");
        try {
            List<ProductResponseHomePage> productResponseHomePage = productService.getFeaturedProducts();
            return ResponseEntity.ok(new CommonResponse(true,"Featured product details retrieved successfully.",productResponseHomePage));
        }
        catch (DomainException e){
            log.error("Unexpected error while retrieving featured product details: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while retrieving featured product details");
        }
    }

    @ApiOperation(value= "Get New Product for Home Page Screen")
    @GetMapping(value = "/getNewProducts")
    public ResponseEntity<CommonResponse> getNewProducts() {
        log.info("Inside getNewProducts() API");
        try {
            List<ProductResponseHomePage> productResponseHomePage = productService.getNewProducts();
            return ResponseEntity.ok(new CommonResponse(true,"New product details retrieved successfully.",productResponseHomePage));
        }
        catch (DomainException e){
            log.error("Unexpected error while retrieving new product details: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while retrieving new product details");
        }
    }

    @ApiOperation(value= "Get Product Details")
    @GetMapping(value = "/getProductDetailsByPartNumberSlug")
    public ResponseEntity<CommonResponse> getProductDetailsByPartNumberSlug(@RequestParam("partNumberSlug") String partNumberSlug) {
        log.info("Inside getProductDetailsByPartNumberSlug() API");
        try {
            if(StringUtils.isBlank(partNumberSlug)) {
                log.error(Constants.INPUT_PARAMETER_NOT_FOUND);
                throw new InvalidInputException("Input parameter 'partNumberSlug' is invalid or missing");
            }
            ProductDetailsResponse productDetailsResponse = productService.getProductDetailsByPartNumberSlug(partNumberSlug);
            if(Objects.nonNull(productDetailsResponse))
                return ResponseEntity.ok(new CommonResponse(true, "Product details retrieved successfully.",productDetailsResponse));
            else
                return ResponseEntity.ok(new CommonResponse(true,"No product found for given part number.", ""));
        }
        catch (Exception e){
            log.error("Unexpected error while retrieving product details: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while retrieving product details");
        }
    }
    @ApiOperation(value= "Get Similar Product List By CrossReferenceId")
    @GetMapping(value = "/getSimilarProductList")
    public ResponseEntity<CommonResponse> getSimilarProductList(@RequestParam("crossReferenceId") List<String> crossReferenceIds) {
        log.info("Inside getSimilarProductList() API");
        try {
            if(crossReferenceIds.isEmpty()) {
                log.error(Constants.INPUT_PARAMETER_NOT_FOUND);
                throw new InvalidInputException("Input parameter 'crossReferenceIds' is invalid or missing");
            }
            List<ProductResponseHomePage> productResponseHomePage = productService.getSimilarProductListByCrossReferenceId(crossReferenceIds);
            return ResponseEntity.ok(new CommonResponse(true,"Similar Product details retrieved successfully.",productResponseHomePage));
        }
        catch (DomainException e){
            log.error("Unexpected error while retrieving similar product details: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while retrieving similar product details");
        }
    }
}
