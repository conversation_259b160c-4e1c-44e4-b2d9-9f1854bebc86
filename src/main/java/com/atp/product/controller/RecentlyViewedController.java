package com.atp.product.controller;

import com.atp.product.controller.dto.response.CommonResponse;
import com.atp.product.controller.dto.response.ProductResponseHomePage;
import com.atp.product.exception.DomainException;
import com.atp.product.service.RecentlyViewedService;
import com.atp.product.utils.AuthorizationUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/recentlyViewed")
@Api(value = "Recently Viewed API", tags = "Recently Viewed")
@Slf4j
public class RecentlyViewedController {

    @Autowired
    private RecentlyViewedService recentlyViewedService;
    
    @Autowired
    private HttpServletRequest request;

    /**
     * Get detailed information for recently viewed products
     * @param customerCorrelationId The unique identifier for the customer
     * @param limit Maximum number of results to return
     * @return List of product details in order of most recently viewed first
     */
    @ApiOperation(value = "Get recently viewed products with details")
    @GetMapping("/getRecentlyViewedProducts")
    public ResponseEntity<CommonResponse> getRecentlyViewedProducts(@RequestParam("customerCorrelationId") String customerCorrelationId,
            @RequestParam(value = "limit", defaultValue = "10") int limit) {
        log.info("Inside getRecentlyViewedProducts() API with limit: {}", limit);
        try {
            List<ProductResponseHomePage> products = recentlyViewedService.getRecentlyViewedProductDetails(customerCorrelationId, limit);
            
            return ResponseEntity.ok(new CommonResponse(
                    true, 
                    products.isEmpty() ? "No recently viewed products found" : "Recently viewed products retrieved successfully",
                    products
            ));
        } catch (Exception e) {
            log.error("Unexpected error while retrieving recently viewed products: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while retrieving recently viewed products");
        }
    }

    /**
     * Record a product view by a user
     * @param customerCorrelationId The unique identifier for the customer
     * @param partNumberSlug The part number slug of the viewed product
     */
    @ApiOperation(value = "Record a product view")
    @PostMapping("/recordProductView")
    public ResponseEntity<CommonResponse> recordProductView(@RequestParam("customerCorrelationId") String customerCorrelationId,
            @RequestParam("partNumberSlug") String partNumberSlug) {
        log.info("Inside recordProductView() API with partNumberSlug: {}", partNumberSlug);
        try {
            recentlyViewedService.recordProductView(customerCorrelationId, partNumberSlug);

            return ResponseEntity.ok(new CommonResponse(
                    true,
                    "Product view recorded successfully",
                    null
            ));
        } catch (Exception e) {
            log.error("Unexpected error while recording product view: {}", e.getMessage(), e);
            throw new DomainException("Something went wrong while recording product view");
        }
    }
}