package com.atp.product.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * Utility class for performance benchmarking and comparison
 */
@Component
public class PerformanceBenchmark {

    private static final Logger logger = LoggerFactory.getLogger(PerformanceBenchmark.class);

    /**
     * Benchmark a method execution and return the result with timing information
     */
    public <T> BenchmarkResult<T> benchmark(String operationName, Supplier<T> operation) {
        long startTime = System.nanoTime();
        long startMemory = getUsedMemory();
        
        T result = operation.get();
        
        long endTime = System.nanoTime();
        long endMemory = getUsedMemory();
        
        long executionTimeNanos = endTime - startTime;
        long memoryUsed = endMemory - startMemory;
        
        BenchmarkResult<T> benchmarkResult = new BenchmarkResult<>(
            result,
            executionTimeNanos,
            TimeUnit.NANOSECONDS.toMillis(executionTimeNanos),
            memoryUsed
        );
        
        logger.info("Operation '{}' completed in {} ms, Memory used: {} bytes", 
                   operationName, benchmarkResult.getExecutionTimeMillis(), memoryUsed);
        
        return benchmarkResult;
    }

    /**
     * Compare two operations and log the performance difference
     */
    public <T> void compareOperations(String operation1Name, Supplier<T> operation1,
                                     String operation2Name, Supplier<T> operation2) {
        
        // Warm up JVM
        operation1.get();
        operation2.get();
        
        // Run benchmarks
        BenchmarkResult<T> result1 = benchmark(operation1Name, operation1);
        BenchmarkResult<T> result2 = benchmark(operation2Name, operation2);
        
        // Calculate improvement
        double timeImprovement = calculateImprovement(
            result1.getExecutionTimeMillis(), 
            result2.getExecutionTimeMillis()
        );
        
        double memoryImprovement = calculateImprovement(
            result1.getMemoryUsed(), 
            result2.getMemoryUsed()
        );
        
        logger.info("Performance Comparison:");
        logger.info("{}: {} ms, {} bytes", operation1Name, 
                   result1.getExecutionTimeMillis(), result1.getMemoryUsed());
        logger.info("{}: {} ms, {} bytes", operation2Name, 
                   result2.getExecutionTimeMillis(), result2.getMemoryUsed());
        logger.info("Time improvement: {:.2f}%", timeImprovement);
        logger.info("Memory improvement: {:.2f}%", memoryImprovement);
    }

    private double calculateImprovement(long baseline, long optimized) {
        if (baseline == 0) return 0;
        return ((double) (baseline - optimized) / baseline) * 100;
    }

    private long getUsedMemory() {
        Runtime runtime = Runtime.getRuntime();
        return runtime.totalMemory() - runtime.freeMemory();
    }

    /**
     * Result class for benchmark operations
     */
    public static class BenchmarkResult<T> {
        private final T result;
        private final long executionTimeNanos;
        private final long executionTimeMillis;
        private final long memoryUsed;

        public BenchmarkResult(T result, long executionTimeNanos, long executionTimeMillis, long memoryUsed) {
            this.result = result;
            this.executionTimeNanos = executionTimeNanos;
            this.executionTimeMillis = executionTimeMillis;
            this.memoryUsed = memoryUsed;
        }

        public T getResult() { return result; }
        public long getExecutionTimeNanos() { return executionTimeNanos; }
        public long getExecutionTimeMillis() { return executionTimeMillis; }
        public long getMemoryUsed() { return memoryUsed; }
    }
}
