# Authentication Integration for PlaceOrder API

## 🔐 **Overview**

The `placeOrder` method now includes automatic authentication using the login API. Instead of using static tokens, the system dynamically obtains JWT tokens and caches them for efficiency.

## 🔄 **Authentication Flow**

### **1. Token Request Process**
```
1. Check if cached token exists and is valid
2. If not, call login API with credentials
3. Extract JWT token from response
4. C<PERSON> token with expiry time
5. Use token in placeOrder API call
```

### **2. Login API Integration**

#### **Request:**
```http
POST http://localhost:8080/server/rest/auth/login
Content-Type: application/json

{
    "userid": "IshaniPandya",
    "password": "Indianic@123",
    "deviceUUID": "",
    "enc": false,
    "userProperties": []
}
```

#### **Response:**
```json
{
    "status": 1,
    "userId": "IshaniPandya",
    "tenantId": "ActionTruckDEV",
    "tenantName": "Action Truck DEV",
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9...",
    "roles": ["Customers"],
    "name": "Ishani Pandya",
    "userProperties": {
        "KarmakCustomerId": "00205",
        "CustomerCorrelationId": "CSTM_1072"
    }
}
```

## ⚙️ **Configuration**

### **Application Properties**
```properties
# API Endpoints
third-party.api.url=http://localhost:8080/server/api/data-entities/create
third-party.api.login.url=http://localhost:8080/server/rest/auth/login
third-party.api.timeout=30

# Authentication Credentials
third-party.api.username=IshaniPandya
third-party.api.password=Indianic@123
```

### **Environment-Specific Configuration**
```properties
# Development
third-party.api.login.url=http://localhost:8080/server/rest/auth/login
third-party.api.username=IshaniPandya
third-party.api.password=Indianic@123

# Production
third-party.api.login.url=https://api.production.com/server/rest/auth/login
third-party.api.username=${API_USERNAME}
third-party.api.password=${API_PASSWORD}
```

## 🔧 **Implementation Details**

### **Authentication Method**
```java
private String getAuthToken() {
    // Check cached token validity
    if (cachedToken != null && System.currentTimeMillis() < tokenExpiryTime) {
        return cachedToken;
    }

    // Create login request
    ObjectNode loginPayload = objectMapper.createObjectNode();
    loginPayload.put("userid", apiUsername);
    loginPayload.put("password", apiPassword);
    loginPayload.put("deviceUUID", "");
    loginPayload.put("enc", false);
    loginPayload.set("userProperties", objectMapper.createArrayNode());

    // Execute login request and extract token
    // Cache token for 55 minutes
    // Return token or null if authentication fails
}
```

### **Integration with PlaceOrder**
```java
public boolean placeOrder(KarmakPurchaseOrder originalOrder) {
    // Get authentication token dynamically
    String authToken = getAuthToken();
    if (authToken != null) {
        requestBuilder.addHeader("Authorization", authToken);
    } else {
        log.error("Failed to get authentication token");
        return false;
    }
    
    // Continue with order placement...
}
```

## 🚀 **Key Features**

### **1. Token Caching**
- **Cache Duration**: 55 minutes (5 minutes before typical 1-hour expiry)
- **Automatic Refresh**: Gets new token when cached token expires
- **Memory Efficient**: Stores only token string and expiry time

### **2. Error Handling**
- **Authentication Failures**: Returns null and logs error
- **Network Issues**: Handles IOException gracefully
- **Invalid Responses**: Validates response status before extracting token

### **3. Security**
- **Credentials**: Configurable via properties (use environment variables in production)
- **Token Storage**: In-memory caching (not persisted)
- **Logging**: Sensitive data excluded from logs

## 📊 **Performance Benefits**

| Aspect | Before (Static Token) | After (Dynamic Auth) |
|--------|----------------------|---------------------|
| **Token Management** | Manual | Automatic |
| **Token Expiry** | Manual refresh needed | Auto-refresh |
| **API Calls** | 1 per order | 1 per order + 1 auth per hour |
| **Reliability** | Fails when token expires | Self-healing |
| **Maintenance** | High (manual token updates) | Zero |

## 🧪 **Testing**

### **Unit Tests Coverage**
- ✅ Successful authentication
- ✅ Authentication failure (wrong credentials)
- ✅ HTTP failure (network issues)
- ✅ Token caching functionality
- ✅ Expired token refresh
- ✅ Request payload validation

### **Integration Testing**
```java
@Test
void testPlaceOrderWithDynamicAuth() {
    // Mock successful authentication
    // Mock successful order placement
    // Verify token is obtained and used
}
```

## 🔍 **Monitoring and Logging**

### **Log Levels**
```java
// Authentication success
log.info("Successfully authenticated and cached token");

// Authentication failure
log.error("Authentication failed. Status: {}", status);

// Token cache usage
log.debug("Using cached authentication token");

// Network errors
log.error("Exception during authentication", exception);
```

### **Key Metrics to Monitor**
- **Authentication Success Rate**: Should be >99%
- **Token Cache Hit Rate**: Should be >95%
- **Authentication Response Time**: Should be <500ms
- **Token Expiry Events**: Should occur ~every hour

## 🚨 **Error Scenarios**

### **1. Invalid Credentials**
```json
{
    "status": 0,
    "message": "Invalid credentials"
}
```
**Handling**: Returns null, logs error, placeOrder fails gracefully

### **2. Network Timeout**
**Handling**: IOException caught, returns null, placeOrder fails gracefully

### **3. Malformed Response**
**Handling**: JSON parsing error caught, returns null, logs warning

### **4. Missing Token in Response**
**Handling**: Null check on token field, returns null if missing

## 🔒 **Security Considerations**

### **Credential Management**
- **Development**: Use properties file
- **Production**: Use environment variables or secrets management
- **Rotation**: Update credentials in configuration

### **Token Security**
- **In-Memory Only**: Tokens not persisted to disk
- **Automatic Expiry**: Cached tokens expire automatically
- **No Logging**: Token values excluded from logs

### **Network Security**
- **HTTPS**: Use HTTPS in production
- **Timeouts**: Configured to prevent hanging requests
- **Error Handling**: No sensitive data in error messages

## 🔄 **Workflow Example**

```
1. placeOrder() called with originalOrder
2. getAuthToken() called
3. Check cached token validity
4. If expired/missing:
   a. Call login API with credentials
   b. Extract token from response
   c. Cache token with 55-minute expiry
5. Use token in Authorization header
6. Call placeOrder API with originalOrder JSON
7. Return success/failure result
```

## 📈 **Benefits**

1. **✅ Automatic Token Management** - No manual intervention needed
2. **✅ Self-Healing** - Automatically recovers from token expiry
3. **✅ Efficient** - Caches tokens to minimize auth calls
4. **✅ Reliable** - Handles all error scenarios gracefully
5. **✅ Secure** - Follows security best practices
6. **✅ Configurable** - Easy to adapt to different environments
7. **✅ Testable** - Comprehensive test coverage

## 🎯 **Usage**

The authentication is completely transparent to the caller:

```java
// Just call placeOrder - authentication happens automatically
boolean success = scheduledOrderService.placeOrder(originalOrder);

if (success) {
    log.info("Order placed successfully");
} else {
    log.error("Order placement failed");
}
```

**The system handles all authentication complexity behind the scenes!** 🔐
