# PO Information Update in Original Order

## 🎯 **Overview**

After successfully creating a purchase order via `createPurchaseOrder`, the system now automatically updates the `originalOrder.Values` array with the PO information returned from the response. This ensures the original order data is enriched with the generated PO details.

## 🔄 **Implementation Details**

### **Data Flow:**
1. **Create PO** → `commonService.createPurchaseOrder(internalRequest)`
2. **Extract PO Info** → Get `PartsPurchaseOrderID` and `PONumber` from response
3. **Update Original Order** → Add/update these fields in `originalOrder.Values`
4. **Place Order** → Call external API with enriched `originalOrder`

### **Fields Updated:**
- **`PartsPurchaseOrderID`** - The Parts Purchase Order ID (e.g., "12112")
- **`PONumber`** - The Purchase Order Number (e.g., "200645")

## 🔧 **Code Implementation**

### **Main Update Method:**
```java
private void updateOriginalOrderWithPOInfo(KarmakPurchaseOrder originalOrder, 
                                         String partsPurchaseOrderID, 
                                         String poNumber) {
    // Get existing Values list or create new one
    List<Value> values = originalOrder.getValues();
    if (values == null) {
        values = new ArrayList<>();
        originalOrder.setValues(values);
    }

    // Update or add PartsPurchaseOrderID
    updateOrAddValueField(values, "PartsPurchaseOrderID", partsPurchaseOrderID);
    
    // Update or add PONumber
    updateOrAddValueField(values, "PONumber", poNumber);
}
```

### **Field Update Logic:**
```java
private void updateOrAddValueField(List<Value> values, String attributeId, String value) {
    // Look for existing field
    Value existingValue = values.stream()
            .filter(v -> attributeId.equals(v.getAttributeId()))
            .findFirst()
            .orElse(null);

    if (existingValue != null) {
        // Update existing field
        existingValue.getContent().get(0).setValue(value);
    } else {
        // Add new field
        Value newValue = new Value();
        newValue.setAttributeId(attributeId);
        
        Content content = new Content();
        content.setValue(value);
        newValue.setContent(Arrays.asList(content));
        
        values.add(newValue);
    }
}
```

### **Integration in Process Flow:**
```java
// In processAvailableOrder method
if (response != null) {
    // Extract PO information from response
    String partsPurchaseOrderID = String.valueOf(response.getPartsPurchaseOrder().getId());
    String poNumber = response.getPartsPurchaseOrder().getPoNumber();

    // Update originalOrder Values with PO information
    updateOriginalOrderWithPOInfo(originalOrder, partsPurchaseOrderID, poNumber);

    // Place the order (external API call) with enriched data
    boolean orderPlaced = placeOrder(originalOrder);
}
```

## 📊 **Before and After Examples**

### **Before PO Creation:**
```json
{
    "Values": [
        {
            "attributeId": "ECM_OrderDate",
            "content": [{"value": "07/10/2025"}]
        },
        {
            "attributeId": "OrderStatus", 
            "content": [{"value": "Order Placed/New Order"}]
        },
        {
            "attributeId": "ECM_GrandTotal",
            "content": [{"value": 24.69}]
        }
    ]
}
```

### **After PO Creation:**
```json
{
    "Values": [
        {
            "attributeId": "ECM_OrderDate",
            "content": [{"value": "07/10/2025"}]
        },
        {
            "attributeId": "OrderStatus",
            "content": [{"value": "Order Placed/New Order"}]
        },
        {
            "attributeId": "ECM_GrandTotal",
            "content": [{"value": 24.69}]
        },
        {
            "attributeId": "PartsPurchaseOrderID",
            "content": [{"value": "12112"}]
        },
        {
            "attributeId": "PONumber",
            "content": [{"value": "200645"}]
        }
    ]
}
```

## ✅ **Key Features**

### **1. Smart Field Management:**
- **Add New Fields** - If `PartsPurchaseOrderID` or `PONumber` don't exist, they're added
- **Update Existing** - If fields already exist, their values are updated
- **No Duplicates** - Prevents duplicate fields in the Values array

### **2. Null Safety:**
- **Handles null Values** - Creates Values array if it doesn't exist
- **Handles empty Values** - Works with empty Values arrays
- **Safe Content Access** - Properly handles Content arrays

### **3. Preserves Existing Data:**
- **Non-destructive** - Existing Values fields are preserved
- **Order maintained** - New fields are appended to the end
- **Type safety** - Maintains proper Value/Content structure

## 🧪 **Testing Coverage**

### **Test Scenarios:**
- ✅ **Add new fields** to empty Values array
- ✅ **Update existing fields** without creating duplicates
- ✅ **Handle null Values** array gracefully
- ✅ **Preserve existing fields** while adding new ones
- ✅ **Maintain proper structure** of Value/Content objects

### **Example Test:**
```java
@Test
void testUpdateOriginalOrderWithPOInfo_NewFields() {
    // Given
    String partsPurchaseOrderID = "12112";
    String poNumber = "200645";

    // When
    updateOriginalOrderWithPOInfo(originalOrder, partsPurchaseOrderID, poNumber);

    // Then
    Value partsPOIDValue = findValueByAttributeId("PartsPurchaseOrderID");
    assertEquals("12112", partsPOIDValue.getContent().get(0).getValue());
    
    Value poNumberValue = findValueByAttributeId("PONumber");
    assertEquals("200645", poNumberValue.getContent().get(0).getValue());
}
```

## 🔍 **Error Handling**

### **Exception Safety:**
```java
try {
    updateOriginalOrderWithPOInfo(originalOrder, partsPurchaseOrderID, poNumber);
    log.info("Successfully updated originalOrder Values with PO info");
} catch (Exception e) {
    log.error("Failed to update originalOrder with PO info", e);
    // Process continues - this is not a critical failure
}
```

### **Logging:**
- **Debug logs** for field updates
- **Info logs** for successful updates
- **Error logs** for failures with full context

## 📈 **Benefits**

### **1. Data Enrichment:**
- **Complete traceability** - Original order now contains PO references
- **Audit trail** - Full history of order processing
- **Integration ready** - External API receives enriched data

### **2. Operational Advantages:**
- **Automatic updates** - No manual intervention required
- **Consistent data** - PO info always included when available
- **Error resilient** - Failures don't break the main flow

### **3. API Enhancement:**
- **Richer payloads** - External API gets more complete data
- **Better correlation** - Easy to match orders with POs
- **Improved tracking** - Enhanced order lifecycle visibility

## 🎯 **Usage in Scheduled Orders**

### **Process Flow:**
1. **Scheduled order triggers** → `processScheduledOrders()`
2. **Order processing** → `processAvailableOrder()`
3. **PO creation** → `commonService.createPurchaseOrder()`
4. **Data enrichment** → `updateOriginalOrderWithPOInfo()` ✅ **NEW**
5. **External API call** → `placeOrder()` with enriched data
6. **Success notification** → Order completed with full PO info

### **Result:**
When the external API receives the order via `placeOrder()`, it now includes:
- All original order data
- **Plus** the generated `PartsPurchaseOrderID`
- **Plus** the generated `PONumber`

## 🚀 **Impact**

This enhancement ensures that:
- **✅ Original orders are enriched** with PO information automatically
- **✅ External APIs receive complete data** including PO references
- **✅ Data consistency is maintained** across all systems
- **✅ Traceability is enhanced** with full order-to-PO correlation

**The system now provides seamless PO information integration in the order processing workflow!** 🎯
